# TRUNK-USDC Pool Monitor

A TypeScript/Node.js script that monitors your TRUNK-USDC position on Save.Finance (Solend protocol) and automatically attempts to withdraw your funds when conditions allow.

## ⚠️ IMPORTANT SECURITY NOTICE

This script requires your wallet's private key to function. **NEVER share your private key with anyone!** Make sure to:
- Keep your `.env` file secure and never commit it to version control
- Only run this script on a secure, trusted computer
- Consider using a separate wallet with minimal funds for testing

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Set Up Configuration

**Option A: Interactive Setup (Recommended)**
```bash
npm run setup
```

**Option B: Manual Setup**
Copy the example environment file and fill in your details:

```bash
cp .env.example .env
```

Edit `.env` with your information:

```env
# Your wallet private key (base58 encoded)
PRIVATE_KEY=your_private_key_here

# Your wallet address (for verification)
WALLET_ADDRESS=En9w4JmoYh894xy4qxx1JkWHsifPLEA8U3hDEovpvV32

# Pool and obligation addresses (already filled for your case)
POOL_ADDRESS=616Kxm68sCsFJTzKfqJYt1o2Na5qrujgfhrhUJsSPXHx
OBLIGATION_ADDRESS=BpgFy4iymzUjLzWiP3fGVASnLh1wqcT7NWnoTcybMQwg

# RPC endpoint
RPC_ENDPOINT=https://solendf-solendf-67c7.rpcpool.com/

# Monitoring settings
CHECK_INTERVAL_MINUTES=5
MIN_USDC_BALANCE_TO_WITHDRAW=1

# Optional: Notification settings
DISCORD_WEBHOOK_URL=
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=
```

### 3. Get Your Private Key

**From Phantom Wallet:**
1. Open Phantom wallet
2. Click the settings gear icon
3. Go to "Security & Privacy"
4. Click "Export Private Key"
5. Enter your password
6. Copy the private key (it's in base58 format)

**From Solflare:**
1. Open Solflare wallet
2. Go to Settings → Security
3. Click "Export Private Key"
4. Copy the private key

### 4. Test Your Setup

```bash
npm run test
```

This will verify your configuration, test the connection, and check your wallet.

**Test the monitor functionality:**
```bash
npm run test-monitor
```

This will test the complete monitoring system without making any transactions.

### 5. Run the Script

**Check current status:**
```bash
npm run dev status
```

**Start standard monitoring (1-minute intervals):**
```bash
npm run dev monitor
```

**Start real-time monitoring (30-second intervals + account change detection):**
```bash
npm run monitor-realtime
```

**Production monitoring:**
```bash
npm run build
npm run monitor
```

## 📊 Features

- **Real-time Monitoring**: Checks your pool position every 5 minutes (configurable)
- **Automatic Withdrawal**: Attempts to withdraw when conditions are safe
- **Health Factor Monitoring**: Tracks your position's health to avoid liquidation
- **Notifications**: Supports Discord and Telegram notifications
- **Conservative Approach**: Only withdraws when health factor > 1.5 for safety
- **Detailed Logging**: Comprehensive logs of all activities

## 🔧 Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `CHECK_INTERVAL_MINUTES` | How often to check the pool (standard mode) | 1 |
| `MIN_USDC_BALANCE_TO_WITHDRAW` | Minimum USDC amount to withdraw | 0.01 |
| `ENABLE_REALTIME_MONITORING` | Enable real-time monitoring mode | false |
| `REALTIME_CHECK_INTERVAL_SECONDS` | Check interval for real-time mode | 30 |
| `DISCORD_WEBHOOK_URL` | Discord webhook for notifications | Optional |
| `TELEGRAM_BOT_TOKEN` | Telegram bot token | Optional |
| `TELEGRAM_CHAT_ID` | Telegram chat ID | Optional |

## 📱 Setting Up Notifications

### Discord Notifications
1. Create a Discord webhook in your server
2. Copy the webhook URL
3. Add it to your `.env` file as `DISCORD_WEBHOOK_URL`

### Telegram Notifications
1. Create a bot by messaging @BotFather on Telegram
2. Get your bot token
3. Get your chat ID by messaging @userinfobot
4. Add both to your `.env` file

## 🛡️ Safety Features

- **Health Factor Check**: Only withdraws when health factor > 1.5
- **Conservative Withdrawal**: Withdraws only 95% of maximum possible amount
- **Error Handling**: Comprehensive error handling and logging
- **Transaction Verification**: Verifies wallet address matches private key
- **Dry Run Mode**: Check status without making transactions

## 📈 Understanding the Output

When you run `npm run dev status`, you'll see:

- **Pool Information**: Total supply, borrows, available liquidity
- **Your Position**: Your deposits, borrows, and collateral
- **Health Metrics**: Borrow limit, liquidation threshold, health factor
- **Withdrawal Status**: Whether you can withdraw and how much

## 🚨 Troubleshooting

### Common Issues

1. **"Private key does not match wallet address"**
   - Double-check your private key and wallet address
   - Make sure the private key is in base58 format

2. **"Market not initialized"**
   - Check your internet connection
   - Verify the RPC endpoint is working
   - Try a different RPC endpoint

3. **"TRUNK reserve not found"**
   - The pool might not be available in the main Solend market
   - Check if Save.Finance is using a different market

4. **Transaction failures**
   - Ensure you have enough SOL for transaction fees
   - Check if the pool has sufficient liquidity

### Getting Help

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify your configuration in `.env`
3. Test with `npm run dev status` first
4. Make sure you have SOL for transaction fees

## ⚡ Commands Reference

```bash
# Install dependencies
npm install

# Interactive setup
npm run setup

# Test configuration
npm run test

# Test monitor functionality
npm run test-monitor

# Check current pool status
npm run dev status

# Start development monitoring
npm run dev monitor

# Build for production
npm run build

# Start production monitoring
npm run start

# Direct monitoring script
npm run monitor
```

## 🔒 Security Best Practices

1. **Never share your private key**
2. **Use a dedicated monitoring machine**
3. **Keep minimal SOL in the wallet (just for fees)**
4. **Regularly check the script's activity**
5. **Stop the script if you notice unusual behavior**

## 📝 License

MIT License - Use at your own risk. This software is provided as-is without any warranties.
