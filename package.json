{"name": "trunk-usdc-monitor", "version": "1.0.0", "description": "Monitor TRUNK-USDC pool on Save.Finance and attempt withdrawals", "main": "dist/index.js", "scripts": {"setup": "node setup.js", "test": "ts-node src/test-connection.ts", "test-monitor": "ts-node src/test-monitor.ts", "find-position": "ts-node src/find-position.ts", "build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "monitor": "ts-node src/monitor.ts", "monitor-realtime": "ENABLE_REALTIME_MONITORING=true ts-node src/monitor.ts"}, "dependencies": {"@solana/web3.js": "^1.95.4", "@solana/spl-token": "^0.4.8", "@solendprotocol/solend-sdk": "^0.6.16", "bn.js": "^5.2.1", "bs58": "^5.0.0", "dotenv": "^16.4.5", "node-cron": "^3.0.3", "axios": "^1.7.9"}, "devDependencies": {"@types/node": "^22.10.2", "@types/node-cron": "^3.0.11", "typescript": "^5.7.2", "ts-node": "^10.9.2"}, "keywords": ["solana", "defi", "solend", "monitoring"], "author": "", "license": "MIT"}