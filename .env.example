# Your Solana wallet private key (base58 encoded)
# You can get this from your wallet (<PERSON>, Solflar<PERSON>, etc.)
# NEVER share this with anyone!
PRIVATE_KEY=5W2e4XCGK6yVkCQ5Mn9T2DGUWRpvcyyZ2qqpToatqLdodBvR7UUwaGyhJiSdyzGK6hqwXavd8FBCh11AL5WW1bec

# Your wallet public key (for verification)
WALLET_ADDRESS=En9w4JmoYh894xy4qxx1JkWHsifPLEA8U3hDEovpvV32

# Pool and obligation addresses from Save.Finance
POOL_ADDRESS=616Kxm68sCsFJTzKfqJYt1o2Na5qrujgfhrhUJsSPXHx
OBLIGATION_ADDRESS=BpgFy4iymzUjLzWiP3fGVASnLh1wqcT7NWnoTcybMQwg

# RPC endpoint (you can use this one or get your own from Helius, QuickNode, etc.)
RPC_ENDPOINT=https://api.mainnet-beta.solana.com

# Monitoring settings
CHECK_INTERVAL_MINUTES=1
MIN_USDC_BALANCE_TO_WITHDRAW=0.01

# Advanced monitoring (optional)
# Set to true to enable real-time account monitoring via WebSocket
ENABLE_REALTIME_MONITORING=false
# How often to check in seconds when using real-time mode (minimum 10 seconds)
REALTIME_CHECK_INTERVAL_SECONDS=30

# Notification settings (optional)
DISCORD_WEBHOOK_URL=
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=
