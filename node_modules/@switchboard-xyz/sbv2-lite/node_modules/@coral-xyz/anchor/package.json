{"name": "@coral-xyz/anchor", "version": "0.28.0", "description": "Anchor client", "module": "./dist/esm/index.js", "main": "./dist/cjs/index.js", "browser": "./dist/browser/index.js", "license": "(MIT OR Apache-2.0)", "types": "dist/cjs/index.d.ts", "homepage": "https://github.com/coral-xyz/anchor#readme", "bugs": {"url": "https://github.com/coral-xyz/anchor/issues"}, "repository": {"type": "git", "url": "https://github.com/coral-xyz/anchor.git"}, "publishConfig": {"access": "public"}, "engines": {"node": ">=11"}, "scripts": {"build": "rimraf dist/ && yarn build:node && yarn build:browser", "build:node": "cd ../borsh && yarn build && cd ../anchor && tsc && tsc -p tsconfig.cjs.json", "build:browser": "rollup --config", "lint:fix": "prettier src/** tests/** -w", "lint": "prettier src/** tests/** --check", "watch": "tsc -p tsconfig.cjs.json --watch", "prepublishOnly": "yarn build", "docs": "typedoc --excludePrivate --includeVersion --out ../../../docs/src/.vuepress/dist/ts/ --readme none src/index.ts", "test": "jest tests --detect<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dependencies": {"@coral-xyz/borsh": "^0.28.0", "@solana/web3.js": "^1.68.0", "base64-js": "^1.5.1", "bn.js": "^5.1.2", "bs58": "^4.0.1", "buffer-layout": "^1.2.2", "camelcase": "^6.3.0", "cross-fetch": "^3.1.5", "crypto-hash": "^1.3.0", "eventemitter3": "^4.0.7", "js-sha256": "^0.9.0", "pako": "^2.0.3", "snake-case": "^3.0.4", "superstruct": "^0.15.4", "toml": "^3.0.0"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-typescript": "^8.3.0", "@types/bn.js": "^4.11.6", "@types/bs58": "^4.0.1", "@types/crypto-hash": "^1.1.2", "@types/jest": "^27.4.1", "@types/pako": "^1.0.1", "@typescript-eslint/eslint-plugin": "^4.6.0", "@typescript-eslint/parser": "^4.6.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "husky": "^4.3.0", "jest": "27.3.1", "jest-config": "27.3.1", "lint-staged": "^10.5.0", "prettier": "^2.1.2", "rimraf": "^3.0.2", "rollup": "^2.60.2", "ts-jest": "^27.0.7", "ts-jest-resolver": "^2.0.0", "ts-node": "^9.0.0", "tslib": "^2.3.1", "typedoc": "^0.22.10", "typescript": "^4.5.2"}, "files": ["dist", "types"]}