cmd_Release/obj.target/bigint_buffer/src/bigint-buffer.o := cc -o Release/obj.target/bigint_buffer/src/bigint-buffer.o ../src/bigint-buffer.c '-DNODE_GYP_MODULE_NAME=bigint_buffer' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/src -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/v8/include  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter  -MMD -MF ./Release/.deps/Release/obj.target/bigint_buffer/src/bigint-buffer.o.d.raw   -c
Release/obj.target/bigint_buffer/src/bigint-buffer.o: \
  ../src/bigint-buffer.c \
  /Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/node_api_types.h
../src/bigint-buffer.c:
/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/node_api_types.h:
