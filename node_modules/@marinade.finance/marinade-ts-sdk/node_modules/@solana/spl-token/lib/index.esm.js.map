{"version": 3, "file": "index.esm.js", "sources": ["../client/layout.js", "../client/util/send-and-confirm-transaction.js", "../client/token.js"], "sourcesContent": ["// @flow\n\nimport * as BufferLayout from 'buffer-layout';\n\n/**\n * Layout for a public key\n */\nexport const publicKey = (property: string = 'publicKey'): Object => {\n  return BufferLayout.blob(32, property);\n};\n\n/**\n * Layout for a 64bit unsigned value\n */\nexport const uint64 = (property: string = 'uint64'): Object => {\n  return BufferLayout.blob(8, property);\n};\n", "// @flow\n\nimport {sendAndConfirmTransaction as realSendAndConfirmTransaction} from '@solana/web3.js';\nimport type {\n  Connection,\n  Signer,\n  Transaction,\n  TransactionSignature,\n} from '@solana/web3.js';\n\nexport function sendAndConfirmTransaction(\n  title: string,\n  connection: Connection,\n  transaction: Transaction,\n  ...signers: Array<Signer>\n): Promise<TransactionSignature> {\n  return realSendAndConfirmTransaction(connection, transaction, signers, {\n    skipPreflight: false,\n  });\n}\n", "/**\n * @flow\n */\n\nimport {Buffer} from 'buffer';\nimport assert from 'assert';\nimport BN from 'bn.js';\nimport * as BufferLayout from 'buffer-layout';\nimport {\n  Keypair,\n  PublicKey,\n  SystemProgram,\n  Transaction,\n  TransactionInstruction,\n  SYSVAR_RENT_PUBKEY,\n} from '@solana/web3.js';\nimport type {\n  Connection,\n  Commitment,\n  Signer,\n  TransactionSignature,\n} from '@solana/web3.js';\n\nimport * as Layout from './layout';\nimport {sendAndConfirmTransaction} from './util/send-and-confirm-transaction';\n\nexport const TOKEN_PROGRAM_ID: PublicKey = new PublicKey(\n  'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',\n);\n\nexport const ASSOCIATED_TOKEN_PROGRAM_ID: PublicKey = new PublicKey(\n  'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',\n);\n\nconst FAILED_TO_FIND_ACCOUNT = 'Failed to find account';\nconst INVALID_ACCOUNT_OWNER = 'Invalid account owner';\n\n/**\n * Unfortunately, BufferLayout.encode uses an `instanceof` check for `Buffer`\n * which fails when using `publicKey.toBuffer()` directly because the bundled `Buffer`\n * class in `@solana/web3.js` is different from the bundled `Buffer` class in this package\n */\nfunction pubkeyToBuffer(publicKey: PublicKey): typeof Buffer {\n  return Buffer.from(publicKey.toBuffer());\n}\n\n/**\n * 64-bit value\n */\nexport class u64 extends BN {\n  /**\n   * Convert to Buffer representation\n   */\n  toBuffer(): typeof Buffer {\n    const a = super.toArray().reverse();\n    const b = Buffer.from(a);\n    if (b.length === 8) {\n      return b;\n    }\n    assert(b.length < 8, 'u64 too large');\n\n    const zeroPad = Buffer.alloc(8);\n    b.copy(zeroPad);\n    return zeroPad;\n  }\n\n  /**\n   * Construct a u64 from Buffer representation\n   */\n  static fromBuffer(buffer: typeof Buffer): u64 {\n    assert(buffer.length === 8, `Invalid buffer length: ${buffer.length}`);\n    return new u64(\n      [...buffer]\n        .reverse()\n        .map(i => `00${i.toString(16)}`.slice(-2))\n        .join(''),\n      16,\n    );\n  }\n}\n\nfunction isAccount(accountOrPublicKey: any): boolean {\n  return 'publicKey' in accountOrPublicKey;\n}\n\ntype AuthorityType =\n  | 'MintTokens'\n  | 'FreezeAccount'\n  | 'AccountOwner'\n  | 'CloseAccount';\n\nconst AuthorityTypeCodes = {\n  MintTokens: 0,\n  FreezeAccount: 1,\n  AccountOwner: 2,\n  CloseAccount: 3,\n};\n\n// The address of the special mint for wrapped native token.\nexport const NATIVE_MINT: PublicKey = new PublicKey(\n  'So11111111111111111111111111111111111111112',\n);\n\n/**\n * Information about the mint\n */\ntype MintInfo = {|\n  /**\n   * Optional authority used to mint new tokens. The mint authority may only be provided during\n   * mint creation. If no mint authority is present then the mint has a fixed supply and no\n   * further tokens may be minted.\n   */\n  mintAuthority: null | PublicKey,\n\n  /**\n   * Total supply of tokens\n   */\n  supply: u64,\n\n  /**\n   * Number of base 10 digits to the right of the decimal place\n   */\n  decimals: number,\n\n  /**\n   * Is this mint initialized\n   */\n  isInitialized: boolean,\n\n  /**\n   * Optional authority to freeze token accounts\n   */\n  freezeAuthority: null | PublicKey,\n|};\n\nexport const MintLayout: typeof BufferLayout.Structure = BufferLayout.struct([\n  BufferLayout.u32('mintAuthorityOption'),\n  Layout.publicKey('mintAuthority'),\n  Layout.uint64('supply'),\n  BufferLayout.u8('decimals'),\n  BufferLayout.u8('isInitialized'),\n  BufferLayout.u32('freezeAuthorityOption'),\n  Layout.publicKey('freezeAuthority'),\n]);\n\n/**\n * Information about an account\n */\ntype AccountInfo = {|\n  /**\n   * The address of this account\n   */\n  address: PublicKey,\n\n  /**\n   * The mint associated with this account\n   */\n  mint: PublicKey,\n\n  /**\n   * Owner of this account\n   */\n  owner: PublicKey,\n\n  /**\n   * Amount of tokens this account holds\n   */\n  amount: u64,\n\n  /**\n   * The delegate for this account\n   */\n  delegate: null | PublicKey,\n\n  /**\n   * The amount of tokens the delegate authorized to the delegate\n   */\n  delegatedAmount: u64,\n\n  /**\n   * Is this account initialized\n   */\n  isInitialized: boolean,\n\n  /**\n   * Is this account frozen\n   */\n  isFrozen: boolean,\n\n  /**\n   * Is this a native token account\n   */\n  isNative: boolean,\n\n  /**\n   * If this account is a native token, it must be rent-exempt. This\n   * value logs the rent-exempt reserve which must remain in the balance\n   * until the account is closed.\n   */\n  rentExemptReserve: null | u64,\n\n  /**\n   * Optional authority to close the account\n   */\n  closeAuthority: null | PublicKey,\n|};\n\n/**\n * @private\n */\nexport const AccountLayout: typeof BufferLayout.Structure = BufferLayout.struct(\n  [\n    Layout.publicKey('mint'),\n    Layout.publicKey('owner'),\n    Layout.uint64('amount'),\n    BufferLayout.u32('delegateOption'),\n    Layout.publicKey('delegate'),\n    BufferLayout.u8('state'),\n    BufferLayout.u32('isNativeOption'),\n    Layout.uint64('isNative'),\n    Layout.uint64('delegatedAmount'),\n    BufferLayout.u32('closeAuthorityOption'),\n    Layout.publicKey('closeAuthority'),\n  ],\n);\n\n/**\n * Information about an multisig\n */\ntype MultisigInfo = {|\n  /**\n   * The number of signers required\n   */\n  m: number,\n\n  /**\n   * Number of possible signers, corresponds to the\n   * number of `signers` that are valid.\n   */\n  n: number,\n\n  /**\n   * Is this mint initialized\n   */\n  initialized: boolean,\n\n  /**\n   * The signers\n   */\n  signer1: PublicKey,\n  signer2: PublicKey,\n  signer3: PublicKey,\n  signer4: PublicKey,\n  signer5: PublicKey,\n  signer6: PublicKey,\n  signer7: PublicKey,\n  signer8: PublicKey,\n  signer9: PublicKey,\n  signer10: PublicKey,\n  signer11: PublicKey,\n|};\n\n/**\n * @private\n */\nconst MultisigLayout = BufferLayout.struct([\n  BufferLayout.u8('m'),\n  BufferLayout.u8('n'),\n  BufferLayout.u8('is_initialized'),\n  Layout.publicKey('signer1'),\n  Layout.publicKey('signer2'),\n  Layout.publicKey('signer3'),\n  Layout.publicKey('signer4'),\n  Layout.publicKey('signer5'),\n  Layout.publicKey('signer6'),\n  Layout.publicKey('signer7'),\n  Layout.publicKey('signer8'),\n  Layout.publicKey('signer9'),\n  Layout.publicKey('signer10'),\n  Layout.publicKey('signer11'),\n]);\n\n/**\n * An ERC20-like Token\n */\nexport class Token {\n  /**\n   * @private\n   */\n  connection: Connection;\n\n  /**\n   * The public key identifying this mint\n   */\n  publicKey: PublicKey;\n\n  /**\n   * Program Identifier for the Token program\n   */\n  programId: PublicKey;\n\n  /**\n   * Program Identifier for the Associated Token program\n   */\n  associatedProgramId: PublicKey;\n\n  /**\n   * Fee payer\n   */\n  payer: Signer;\n\n  /**\n   * Create a Token object attached to the specific mint\n   *\n   * @param connection The connection to use\n   * @param token Public key of the mint\n   * @param programId token programId\n   * @param payer Payer of fees\n   */\n  constructor(\n    connection: Connection,\n    publicKey: PublicKey,\n    programId: PublicKey,\n    payer: Signer,\n  ) {\n    Object.assign(this, {\n      connection,\n      publicKey,\n      programId,\n      payer,\n      // Hard code is ok; Overriding is needed only for tests\n      associatedProgramId: ASSOCIATED_TOKEN_PROGRAM_ID,\n    });\n  }\n\n  /**\n   * Get the minimum balance for the mint to be rent exempt\n   *\n   * @return Number of lamports required\n   */\n  static async getMinBalanceRentForExemptMint(\n    connection: Connection,\n  ): Promise<number> {\n    return await connection.getMinimumBalanceForRentExemption(MintLayout.span);\n  }\n\n  /**\n   * Get the minimum balance for the account to be rent exempt\n   *\n   * @return Number of lamports required\n   */\n  static async getMinBalanceRentForExemptAccount(\n    connection: Connection,\n  ): Promise<number> {\n    return await connection.getMinimumBalanceForRentExemption(\n      AccountLayout.span,\n    );\n  }\n\n  /**\n   * Get the minimum balance for the multsig to be rent exempt\n   *\n   * @return Number of lamports required\n   */\n  static async getMinBalanceRentForExemptMultisig(\n    connection: Connection,\n  ): Promise<number> {\n    return await connection.getMinimumBalanceForRentExemption(\n      MultisigLayout.span,\n    );\n  }\n\n  /**\n   * Create and initialize a token.\n   *\n   * @param connection The connection to use\n   * @param payer Fee payer for transaction\n   * @param mintAuthority Account or multisig that will control minting\n   * @param freezeAuthority Optional account or multisig that can freeze token accounts\n   * @param decimals Location of the decimal place\n   * @param programId Optional token programId, uses the system programId by default\n   * @return Token object for the newly minted token\n   */\n  static async createMint(\n    connection: Connection,\n    payer: Signer,\n    mintAuthority: PublicKey,\n    freezeAuthority: PublicKey | null,\n    decimals: number,\n    programId: PublicKey,\n  ): Promise<Token> {\n    const mintAccount = Keypair.generate();\n    const token = new Token(\n      connection,\n      mintAccount.publicKey,\n      programId,\n      payer,\n    );\n\n    // Allocate memory for the account\n    const balanceNeeded = await Token.getMinBalanceRentForExemptMint(\n      connection,\n    );\n\n    const transaction = new Transaction();\n    transaction.add(\n      SystemProgram.createAccount({\n        fromPubkey: payer.publicKey,\n        newAccountPubkey: mintAccount.publicKey,\n        lamports: balanceNeeded,\n        space: MintLayout.span,\n        programId,\n      }),\n    );\n\n    transaction.add(\n      Token.createInitMintInstruction(\n        programId,\n        mintAccount.publicKey,\n        decimals,\n        mintAuthority,\n        freezeAuthority,\n      ),\n    );\n\n    // Send the two instructions\n    await sendAndConfirmTransaction(\n      'createAccount and InitializeMint',\n      connection,\n      transaction,\n      payer,\n      mintAccount,\n    );\n\n    return token;\n  }\n\n  /**\n   * Create and initialize a new account.\n   *\n   * This account may then be used as a `transfer()` or `approve()` destination\n   *\n   * @param owner User account that will own the new account\n   * @return Public key of the new empty account\n   */\n  async createAccount(owner: PublicKey): Promise<PublicKey> {\n    // Allocate memory for the account\n    const balanceNeeded = await Token.getMinBalanceRentForExemptAccount(\n      this.connection,\n    );\n\n    const newAccount = Keypair.generate();\n    const transaction = new Transaction();\n    transaction.add(\n      SystemProgram.createAccount({\n        fromPubkey: this.payer.publicKey,\n        newAccountPubkey: newAccount.publicKey,\n        lamports: balanceNeeded,\n        space: AccountLayout.span,\n        programId: this.programId,\n      }),\n    );\n\n    const mintPublicKey = this.publicKey;\n    transaction.add(\n      Token.createInitAccountInstruction(\n        this.programId,\n        mintPublicKey,\n        newAccount.publicKey,\n        owner,\n      ),\n    );\n\n    // Send the two instructions\n    await sendAndConfirmTransaction(\n      'createAccount and InitializeAccount',\n      this.connection,\n      transaction,\n      this.payer,\n      newAccount,\n    );\n\n    return newAccount.publicKey;\n  }\n\n  /**\n   * Create and initialize the associated account.\n   *\n   * This account may then be used as a `transfer()` or `approve()` destination\n   *\n   * @param owner User account that will own the new account\n   * @return Public key of the new associated account\n   */\n  async createAssociatedTokenAccount(owner: PublicKey): Promise<PublicKey> {\n    const associatedAddress = await Token.getAssociatedTokenAddress(\n      this.associatedProgramId,\n      this.programId,\n      this.publicKey,\n      owner,\n    );\n\n    return this.createAssociatedTokenAccountInternal(owner, associatedAddress);\n  }\n\n  async createAssociatedTokenAccountInternal(\n    owner: PublicKey,\n    associatedAddress: PublicKey,\n  ): Promise<PublicKey> {\n    await sendAndConfirmTransaction(\n      'CreateAssociatedTokenAccount',\n      this.connection,\n      new Transaction().add(\n        Token.createAssociatedTokenAccountInstruction(\n          this.associatedProgramId,\n          this.programId,\n          this.publicKey,\n          associatedAddress,\n          owner,\n          this.payer.publicKey,\n        ),\n      ),\n      this.payer,\n    );\n\n    return associatedAddress;\n  }\n\n  /**\n   * Retrieve the associated account or create one if not found.\n   *\n   * This account may then be used as a `transfer()` or `approve()` destination\n   *\n   * @param owner User account that will own the new account\n   * @return The new associated account\n   */\n  async getOrCreateAssociatedAccountInfo(\n    owner: PublicKey,\n  ): Promise<AccountInfo> {\n    const associatedAddress = await Token.getAssociatedTokenAddress(\n      this.associatedProgramId,\n      this.programId,\n      this.publicKey,\n      owner,\n    );\n\n    // This is the optimum logic, considering TX fee, client-side computation,\n    // RPC roundtrips and guaranteed idempotent.\n    // Sadly we can't do this atomically;\n    try {\n      return await this.getAccountInfo(associatedAddress);\n    } catch (err) {\n      // INVALID_ACCOUNT_OWNER can be possible if the associatedAddress has\n      // already been received some lamports (= became system accounts).\n      // Assuming program derived addressing is safe, this is the only case\n      // for the INVALID_ACCOUNT_OWNER in this code-path\n      if (\n        err.message === FAILED_TO_FIND_ACCOUNT ||\n        err.message === INVALID_ACCOUNT_OWNER\n      ) {\n        // as this isn't atomic, it's possible others can create associated\n        // accounts meanwhile\n        try {\n          await this.createAssociatedTokenAccountInternal(\n            owner,\n            associatedAddress,\n          );\n        } catch (err) {\n          // ignore all errors; for now there is no API compatible way to\n          // selectively ignore the expected instruction error if the\n          // associated account is existing already.\n        }\n\n        // Now this should always succeed\n        return await this.getAccountInfo(associatedAddress);\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * Create and initialize a new account on the special native token mint.\n   *\n   * In order to be wrapped, the account must have a balance of native tokens\n   * when it is initialized with the token program.\n   *\n   * This function sends lamports to the new account before initializing it.\n   *\n   * @param connection A solana web3 connection\n   * @param programId The token program ID\n   * @param owner The owner of the new token account\n   * @param payer The source of the lamports to initialize, and payer of the initialization fees.\n   * @param amount The amount of lamports to wrap\n   * @return {Promise<PublicKey>} The new token account\n   */\n  static async createWrappedNativeAccount(\n    connection: Connection,\n    programId: PublicKey,\n    owner: PublicKey,\n    payer: Signer,\n    amount: number,\n  ): Promise<PublicKey> {\n    // Allocate memory for the account\n    const balanceNeeded = await Token.getMinBalanceRentForExemptAccount(\n      connection,\n    );\n\n    // Create a new account\n    const newAccount = Keypair.generate();\n    const transaction = new Transaction();\n    transaction.add(\n      SystemProgram.createAccount({\n        fromPubkey: payer.publicKey,\n        newAccountPubkey: newAccount.publicKey,\n        lamports: balanceNeeded,\n        space: AccountLayout.span,\n        programId,\n      }),\n    );\n\n    // Send lamports to it (these will be wrapped into native tokens by the token program)\n    transaction.add(\n      SystemProgram.transfer({\n        fromPubkey: payer.publicKey,\n        toPubkey: newAccount.publicKey,\n        lamports: amount,\n      }),\n    );\n\n    // Assign the new account to the native token mint.\n    // the account will be initialized with a balance equal to the native token balance.\n    // (i.e. amount)\n    transaction.add(\n      Token.createInitAccountInstruction(\n        programId,\n        NATIVE_MINT,\n        newAccount.publicKey,\n        owner,\n      ),\n    );\n\n    // Send the three instructions\n    await sendAndConfirmTransaction(\n      'createAccount, transfer, and initializeAccount',\n      connection,\n      transaction,\n      payer,\n      newAccount,\n    );\n\n    return newAccount.publicKey;\n  }\n\n  /**\n   * Create and initialize a new multisig.\n   *\n   * This account may then be used for multisignature verification\n   *\n   * @param m Number of required signatures\n   * @param signers Full set of signers\n   * @return Public key of the new multisig account\n   */\n  async createMultisig(\n    m: number,\n    signers: Array<PublicKey>,\n  ): Promise<PublicKey> {\n    const multisigAccount = Keypair.generate();\n\n    // Allocate memory for the account\n    const balanceNeeded = await Token.getMinBalanceRentForExemptMultisig(\n      this.connection,\n    );\n    const transaction = new Transaction();\n    transaction.add(\n      SystemProgram.createAccount({\n        fromPubkey: this.payer.publicKey,\n        newAccountPubkey: multisigAccount.publicKey,\n        lamports: balanceNeeded,\n        space: MultisigLayout.span,\n        programId: this.programId,\n      }),\n    );\n\n    // create the new account\n    let keys = [\n      {pubkey: multisigAccount.publicKey, isSigner: false, isWritable: true},\n      {pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false},\n    ];\n    signers.forEach(signer =>\n      keys.push({pubkey: signer, isSigner: false, isWritable: false}),\n    );\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      BufferLayout.u8('m'),\n    ]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 2, // InitializeMultisig instruction\n        m,\n      },\n      data,\n    );\n    transaction.add({\n      keys,\n      programId: this.programId,\n      data,\n    });\n\n    // Send the two instructions\n    await sendAndConfirmTransaction(\n      'createAccount and InitializeMultisig',\n      this.connection,\n      transaction,\n      this.payer,\n      multisigAccount,\n    );\n\n    return multisigAccount.publicKey;\n  }\n\n  /**\n   * Retrieve mint information\n   */\n  async getMintInfo(): Promise<MintInfo> {\n    const info = await this.connection.getAccountInfo(this.publicKey);\n    if (info === null) {\n      throw new Error('Failed to find mint account');\n    }\n    if (!info.owner.equals(this.programId)) {\n      throw new Error(`Invalid mint owner: ${JSON.stringify(info.owner)}`);\n    }\n    if (info.data.length != MintLayout.span) {\n      throw new Error(`Invalid mint size`);\n    }\n\n    const data = Buffer.from(info.data);\n    const mintInfo = MintLayout.decode(data);\n\n    if (mintInfo.mintAuthorityOption === 0) {\n      mintInfo.mintAuthority = null;\n    } else {\n      mintInfo.mintAuthority = new PublicKey(mintInfo.mintAuthority);\n    }\n\n    mintInfo.supply = u64.fromBuffer(mintInfo.supply);\n    mintInfo.isInitialized = mintInfo.isInitialized != 0;\n\n    if (mintInfo.freezeAuthorityOption === 0) {\n      mintInfo.freezeAuthority = null;\n    } else {\n      mintInfo.freezeAuthority = new PublicKey(mintInfo.freezeAuthority);\n    }\n    return mintInfo;\n  }\n\n  /**\n   * Retrieve account information\n   *\n   * @param account Public key of the account\n   */\n  async getAccountInfo(\n    account: PublicKey,\n    commitment?: Commitment,\n  ): Promise<AccountInfo> {\n    const info = await this.connection.getAccountInfo(account, commitment);\n    if (info === null) {\n      throw new Error(FAILED_TO_FIND_ACCOUNT);\n    }\n    if (!info.owner.equals(this.programId)) {\n      throw new Error(INVALID_ACCOUNT_OWNER);\n    }\n    if (info.data.length != AccountLayout.span) {\n      throw new Error(`Invalid account size`);\n    }\n\n    const data = Buffer.from(info.data);\n    const accountInfo = AccountLayout.decode(data);\n    accountInfo.address = account;\n    accountInfo.mint = new PublicKey(accountInfo.mint);\n    accountInfo.owner = new PublicKey(accountInfo.owner);\n    accountInfo.amount = u64.fromBuffer(accountInfo.amount);\n\n    if (accountInfo.delegateOption === 0) {\n      accountInfo.delegate = null;\n      accountInfo.delegatedAmount = new u64();\n    } else {\n      accountInfo.delegate = new PublicKey(accountInfo.delegate);\n      accountInfo.delegatedAmount = u64.fromBuffer(accountInfo.delegatedAmount);\n    }\n\n    accountInfo.isInitialized = accountInfo.state !== 0;\n    accountInfo.isFrozen = accountInfo.state === 2;\n\n    if (accountInfo.isNativeOption === 1) {\n      accountInfo.rentExemptReserve = u64.fromBuffer(accountInfo.isNative);\n      accountInfo.isNative = true;\n    } else {\n      accountInfo.rentExemptReserve = null;\n      accountInfo.isNative = false;\n    }\n\n    if (accountInfo.closeAuthorityOption === 0) {\n      accountInfo.closeAuthority = null;\n    } else {\n      accountInfo.closeAuthority = new PublicKey(accountInfo.closeAuthority);\n    }\n\n    if (!accountInfo.mint.equals(this.publicKey)) {\n      throw new Error(\n        `Invalid account mint: ${JSON.stringify(\n          accountInfo.mint,\n        )} !== ${JSON.stringify(this.publicKey)}`,\n      );\n    }\n    return accountInfo;\n  }\n\n  /**\n   * Retrieve Multisig information\n   *\n   * @param multisig Public key of the account\n   */\n  async getMultisigInfo(multisig: PublicKey): Promise<MultisigInfo> {\n    const info = await this.connection.getAccountInfo(multisig);\n    if (info === null) {\n      throw new Error('Failed to find multisig');\n    }\n    if (!info.owner.equals(this.programId)) {\n      throw new Error(`Invalid multisig owner`);\n    }\n    if (info.data.length != MultisigLayout.span) {\n      throw new Error(`Invalid multisig size`);\n    }\n\n    const data = Buffer.from(info.data);\n    const multisigInfo = MultisigLayout.decode(data);\n    multisigInfo.signer1 = new PublicKey(multisigInfo.signer1);\n    multisigInfo.signer2 = new PublicKey(multisigInfo.signer2);\n    multisigInfo.signer3 = new PublicKey(multisigInfo.signer3);\n    multisigInfo.signer4 = new PublicKey(multisigInfo.signer4);\n    multisigInfo.signer5 = new PublicKey(multisigInfo.signer5);\n    multisigInfo.signer6 = new PublicKey(multisigInfo.signer6);\n    multisigInfo.signer7 = new PublicKey(multisigInfo.signer7);\n    multisigInfo.signer8 = new PublicKey(multisigInfo.signer8);\n    multisigInfo.signer9 = new PublicKey(multisigInfo.signer9);\n    multisigInfo.signer10 = new PublicKey(multisigInfo.signer10);\n    multisigInfo.signer11 = new PublicKey(multisigInfo.signer11);\n\n    return multisigInfo;\n  }\n\n  /**\n   * Transfer tokens to another account\n   *\n   * @param source Source account\n   * @param destination Destination account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Number of tokens to transfer\n   */\n  async transfer(\n    source: PublicKey,\n    destination: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): Promise<TransactionSignature> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    return await sendAndConfirmTransaction(\n      'Transfer',\n      this.connection,\n      new Transaction().add(\n        Token.createTransferInstruction(\n          this.programId,\n          source,\n          destination,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Grant a third-party permission to transfer up the specified number of tokens from an account\n   *\n   * @param account Public key of the account\n   * @param delegate Account authorized to perform a transfer tokens from the source account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Maximum number of tokens the delegate may transfer\n   */\n  async approve(\n    account: PublicKey,\n    delegate: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'Approve',\n      this.connection,\n      new Transaction().add(\n        Token.createApproveInstruction(\n          this.programId,\n          account,\n          delegate,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Remove approval for the transfer of any remaining tokens\n   *\n   * @param account Public key of the account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  async revoke(\n    account: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'Revoke',\n      this.connection,\n      new Transaction().add(\n        Token.createRevokeInstruction(\n          this.programId,\n          account,\n          ownerPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Assign a new authority to the account\n   *\n   * @param account Public key of the account\n   * @param newAuthority New authority of the account\n   * @param authorityType Type of authority to set\n   * @param currentAuthority Current authority of the account\n   * @param multiSigners Signing accounts if `currentAuthority` is a multiSig\n   */\n  async setAuthority(\n    account: PublicKey,\n    newAuthority: PublicKey | null,\n    authorityType: AuthorityType,\n    currentAuthority: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let currentAuthorityPublicKey: PublicKey;\n    let signers;\n    if (isAccount(currentAuthority)) {\n      currentAuthorityPublicKey = currentAuthority.publicKey;\n      signers = [currentAuthority];\n    } else {\n      currentAuthorityPublicKey = currentAuthority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'SetAuthority',\n      this.connection,\n      new Transaction().add(\n        Token.createSetAuthorityInstruction(\n          this.programId,\n          account,\n          newAuthority,\n          authorityType,\n          currentAuthorityPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Mint new tokens\n   *\n   * @param dest Public key of the account to mint to\n   * @param authority Minting authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Amount to mint\n   */\n  async mintTo(\n    dest: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      ownerPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      ownerPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'MintTo',\n      this.connection,\n      new Transaction().add(\n        Token.createMintToInstruction(\n          this.programId,\n          this.publicKey,\n          dest,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Burn tokens\n   *\n   * @param account Account to burn tokens from\n   * @param owner Account owner\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Amount to burn\n   */\n  async burn(\n    account: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'Burn',\n      this.connection,\n      new Transaction().add(\n        Token.createBurnInstruction(\n          this.programId,\n          this.publicKey,\n          account,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Close account\n   *\n   * @param account Account to close\n   * @param dest Account to receive the remaining balance of the closed account\n   * @param authority Authority which is allowed to close the account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   */\n  async closeAccount(\n    account: PublicKey,\n    dest: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let authorityPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      authorityPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      authorityPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'CloseAccount',\n      this.connection,\n      new Transaction().add(\n        Token.createCloseAccountInstruction(\n          this.programId,\n          account,\n          dest,\n          authorityPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Freeze account\n   *\n   * @param account Account to freeze\n   * @param authority The mint freeze authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   */\n  async freezeAccount(\n    account: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let authorityPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      authorityPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      authorityPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'FreezeAccount',\n      this.connection,\n      new Transaction().add(\n        Token.createFreezeAccountInstruction(\n          this.programId,\n          account,\n          this.publicKey,\n          authorityPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Thaw account\n   *\n   * @param account Account to thaw\n   * @param authority The mint freeze authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   */\n  async thawAccount(\n    account: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let authorityPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      authorityPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      authorityPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'ThawAccount',\n      this.connection,\n      new Transaction().add(\n        Token.createThawAccountInstruction(\n          this.programId,\n          account,\n          this.publicKey,\n          authorityPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Transfer tokens to another account, asserting the token mint and decimals\n   *\n   * @param source Source account\n   * @param destination Destination account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Number of tokens to transfer\n   * @param decimals Number of decimals in transfer amount\n   */\n  async transferChecked(\n    source: PublicKey,\n    destination: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): Promise<TransactionSignature> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    return await sendAndConfirmTransaction(\n      'TransferChecked',\n      this.connection,\n      new Transaction().add(\n        Token.createTransferCheckedInstruction(\n          this.programId,\n          source,\n          this.publicKey,\n          destination,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n          decimals,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Grant a third-party permission to transfer up the specified number of tokens from an account,\n   * asserting the token mint and decimals\n   *\n   * @param account Public key of the account\n   * @param delegate Account authorized to perform a transfer tokens from the source account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Maximum number of tokens the delegate may transfer\n   * @param decimals Number of decimals in approve amount\n   */\n  async approveChecked(\n    account: PublicKey,\n    delegate: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'ApproveChecked',\n      this.connection,\n      new Transaction().add(\n        Token.createApproveCheckedInstruction(\n          this.programId,\n          account,\n          this.publicKey,\n          delegate,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n          decimals,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Mint new tokens, asserting the token mint and decimals\n   *\n   * @param dest Public key of the account to mint to\n   * @param authority Minting authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Amount to mint\n   * @param decimals Number of decimals in amount to mint\n   */\n  async mintToChecked(\n    dest: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      ownerPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      ownerPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'MintToChecked',\n      this.connection,\n      new Transaction().add(\n        Token.createMintToCheckedInstruction(\n          this.programId,\n          this.publicKey,\n          dest,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n          decimals,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Burn tokens, asserting the token mint and decimals\n   *\n   * @param account Account to burn tokens from\n   * @param owner Account owner\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Amount to burn\n   * @param decimals Number of decimals in amount to burn\n   */\n  async burnChecked(\n    account: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'BurnChecked',\n      this.connection,\n      new Transaction().add(\n        Token.createBurnCheckedInstruction(\n          this.programId,\n          this.publicKey,\n          account,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n          decimals,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Sync amount in native SPL token account to underlying lamports\n   *\n   * @param nativeAccount Account to sync\n   */\n  async syncNative(nativeAccount: PublicKey): Promise<void> {\n    await sendAndConfirmTransaction(\n      'SyncNative',\n      this.connection,\n      new Transaction().add(\n        Token.createSyncNativeInstruction(this.programId, nativeAccount),\n      ),\n      this.payer,\n    );\n  }\n\n  /**\n   * Construct an InitializeMint instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Token mint account\n   * @param decimals Number of decimals in token account amounts\n   * @param mintAuthority Minting authority\n   * @param freezeAuthority Optional authority that can freeze token accounts\n   */\n  static createInitMintInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    decimals: number,\n    mintAuthority: PublicKey,\n    freezeAuthority: PublicKey | null,\n  ): TransactionInstruction {\n    let keys = [\n      {pubkey: mint, isSigner: false, isWritable: true},\n      {pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false},\n    ];\n    const commandDataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      BufferLayout.u8('decimals'),\n      Layout.publicKey('mintAuthority'),\n      BufferLayout.u8('option'),\n      Layout.publicKey('freezeAuthority'),\n    ]);\n    let data = Buffer.alloc(1024);\n    {\n      const encodeLength = commandDataLayout.encode(\n        {\n          instruction: 0, // InitializeMint instruction\n          decimals,\n          mintAuthority: pubkeyToBuffer(mintAuthority),\n          option: freezeAuthority === null ? 0 : 1,\n          freezeAuthority: pubkeyToBuffer(freezeAuthority || new PublicKey(0)),\n        },\n        data,\n      );\n      data = data.slice(0, encodeLength);\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct an InitializeAccount instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Token mint account\n   * @param account New account\n   * @param owner Owner of the new account\n   */\n  static createInitAccountInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    account: PublicKey,\n    owner: PublicKey,\n  ): TransactionInstruction {\n    const keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n      {pubkey: owner, isSigner: false, isWritable: false},\n      {pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false},\n    ];\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 1, // InitializeAccount instruction\n      },\n      data,\n    );\n\n    return new TransactionInstruction({\n      keys,\n      programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Transfer instruction\n   *\n   * @param programId SPL Token program account\n   * @param source Source account\n   * @param destination Destination account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Number of tokens to transfer\n   */\n  static createTransferInstruction(\n    programId: PublicKey,\n    source: PublicKey,\n    destination: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 3, // Transfer instruction\n        amount: new u64(amount).toBuffer(),\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: source, isSigner: false, isWritable: true},\n      {pubkey: destination, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: owner,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct an Approve instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Public key of the account\n   * @param delegate Account authorized to perform a transfer of tokens from the source account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Maximum number of tokens the delegate may transfer\n   */\n  static createApproveInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    delegate: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 4, // Approve instruction\n        amount: new u64(amount).toBuffer(),\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: delegate, isSigner: false, isWritable: false},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: owner, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Revoke instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Public key of the account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  static createRevokeInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 5, // Approve instruction\n      },\n      data,\n    );\n\n    let keys = [{pubkey: account, isSigner: false, isWritable: true}];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: owner, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a SetAuthority instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Public key of the account\n   * @param newAuthority New authority of the account\n   * @param authorityType Type of authority to set\n   * @param currentAuthority Current authority of the specified type\n   * @param multiSigners Signing accounts if `currentAuthority` is a multiSig\n   */\n  static createSetAuthorityInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    newAuthority: PublicKey | null,\n    authorityType: AuthorityType,\n    currentAuthority: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const commandDataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      BufferLayout.u8('authorityType'),\n      BufferLayout.u8('option'),\n      Layout.publicKey('newAuthority'),\n    ]);\n\n    let data = Buffer.alloc(1024);\n    {\n      const encodeLength = commandDataLayout.encode(\n        {\n          instruction: 6, // SetAuthority instruction\n          authorityType: AuthorityTypeCodes[authorityType],\n          option: newAuthority === null ? 0 : 1,\n          newAuthority: pubkeyToBuffer(newAuthority || new PublicKey(0)),\n        },\n        data,\n      );\n      data = data.slice(0, encodeLength);\n    }\n\n    let keys = [{pubkey: account, isSigner: false, isWritable: true}];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: currentAuthority, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: currentAuthority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a MintTo instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Public key of the mint\n   * @param dest Public key of the account to mint to\n   * @param authority The mint authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Amount to mint\n   */\n  static createMintToInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    dest: PublicKey,\n    authority: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 7, // MintTo instruction\n        amount: new u64(amount).toBuffer(),\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: mint, isSigner: false, isWritable: true},\n      {pubkey: dest, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: authority,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: authority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Burn instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Mint for the account\n   * @param account Account to burn tokens from\n   * @param owner Owner of the account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount amount to burn\n   */\n  static createBurnInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    account: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 8, // Burn instruction\n        amount: new u64(amount).toBuffer(),\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: owner,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Close instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Account to close\n   * @param dest Account to receive the remaining balance of the closed account\n   * @param authority Account Close authority\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  static createCloseAccountInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    dest: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 9, // CloseAccount instruction\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: dest, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: owner, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Freeze instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Account to freeze\n   * @param mint Mint account\n   * @param authority Mint freeze authority\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  static createFreezeAccountInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    mint: PublicKey,\n    authority: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 10, // FreezeAccount instruction\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: authority, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: authority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Thaw instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Account to thaw\n   * @param mint Mint account\n   * @param authority Mint freeze authority\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  static createThawAccountInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    mint: PublicKey,\n    authority: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 11, // ThawAccount instruction\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: authority, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: authority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a TransferChecked instruction\n   *\n   * @param programId SPL Token program account\n   * @param source Source account\n   * @param mint Mint account\n   * @param destination Destination account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Number of tokens to transfer\n   * @param decimals Number of decimals in transfer amount\n   */\n  static createTransferCheckedInstruction(\n    programId: PublicKey,\n    source: PublicKey,\n    mint: PublicKey,\n    destination: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n      BufferLayout.u8('decimals'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 12, // TransferChecked instruction\n        amount: new u64(amount).toBuffer(),\n        decimals,\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: source, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n      {pubkey: destination, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: owner,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct an ApproveChecked instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Public key of the account\n   * @param mint Mint account\n   * @param delegate Account authorized to perform a transfer of tokens from the source account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Maximum number of tokens the delegate may transfer\n   * @param decimals Number of decimals in approve amount\n   */\n  static createApproveCheckedInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    mint: PublicKey,\n    delegate: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n      BufferLayout.u8('decimals'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 13, // ApproveChecked instruction\n        amount: new u64(amount).toBuffer(),\n        decimals,\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n      {pubkey: delegate, isSigner: false, isWritable: false},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: owner, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a MintToChecked instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Public key of the mint\n   * @param dest Public key of the account to mint to\n   * @param authority The mint authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Amount to mint\n   * @param decimals Number of decimals in amount to mint\n   */\n  static createMintToCheckedInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    dest: PublicKey,\n    authority: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n      BufferLayout.u8('decimals'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 14, // MintToChecked instruction\n        amount: new u64(amount).toBuffer(),\n        decimals,\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: mint, isSigner: false, isWritable: true},\n      {pubkey: dest, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: authority,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: authority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a BurnChecked instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Mint for the account\n   * @param account Account to burn tokens from\n   * @param owner Owner of the account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount amount to burn\n   */\n  static createBurnCheckedInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    account: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n      BufferLayout.u8('decimals'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 15, // BurnChecked instruction\n        amount: new u64(amount).toBuffer(),\n        decimals,\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: owner,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a SyncNative instruction\n   *\n   * @param programId SPL Token program account\n   * @param nativeAccount Account to sync lamports from\n   */\n  static createSyncNativeInstruction(\n    programId: PublicKey,\n    nativeAccount: PublicKey,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 17, // SyncNative instruction\n      },\n      data,\n    );\n\n    let keys = [{pubkey: nativeAccount, isSigner: false, isWritable: true}];\n    return new TransactionInstruction({keys, programId: programId, data});\n  }\n\n  /**\n   * Get the address for the associated token account\n   *\n   * @param associatedProgramId SPL Associated Token program account\n   * @param programId SPL Token program account\n   * @param mint Token mint account\n   * @param owner Owner of the new account\n   * @return Public key of the associated token account\n   */\n  static async getAssociatedTokenAddress(\n    associatedProgramId: PublicKey,\n    programId: PublicKey,\n    mint: PublicKey,\n    owner: PublicKey,\n    allowOwnerOffCurve: boolean = false,\n  ): Promise<PublicKey> {\n    if (!allowOwnerOffCurve && !PublicKey.isOnCurve(owner.toBuffer())) {\n      throw new Error(`Owner cannot sign: ${owner.toString()}`);\n    }\n    return (\n      await PublicKey.findProgramAddress(\n        [owner.toBuffer(), programId.toBuffer(), mint.toBuffer()],\n        associatedProgramId,\n      )\n    )[0];\n  }\n\n  /**\n   * Construct the AssociatedTokenProgram instruction to create the associated\n   * token account\n   *\n   * @param associatedProgramId SPL Associated Token program account\n   * @param programId SPL Token program account\n   * @param mint Token mint account\n   * @param associatedAccount New associated account\n   * @param owner Owner of the new account\n   * @param payer Payer of fees\n   */\n  static createAssociatedTokenAccountInstruction(\n    associatedProgramId: PublicKey,\n    programId: PublicKey,\n    mint: PublicKey,\n    associatedAccount: PublicKey,\n    owner: PublicKey,\n    payer: PublicKey,\n  ): TransactionInstruction {\n    const data = Buffer.alloc(0);\n\n    let keys = [\n      {pubkey: payer, isSigner: true, isWritable: true},\n      {pubkey: associatedAccount, isSigner: false, isWritable: true},\n      {pubkey: owner, isSigner: false, isWritable: false},\n      {pubkey: mint, isSigner: false, isWritable: false},\n      {pubkey: SystemProgram.programId, isSigner: false, isWritable: false},\n      {pubkey: programId, isSigner: false, isWritable: false},\n      {pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false},\n    ];\n\n    return new TransactionInstruction({\n      keys,\n      programId: associatedProgramId,\n      data,\n    });\n  }\n}\n"], "names": ["public<PERSON>ey", "property", "BufferLayout", "blob", "uint64", "sendAndConfirmTransaction", "title", "connection", "transaction", "signers", "realSendAndConfirmTransaction", "skipPreflight", "TOKEN_PROGRAM_ID", "PublicKey", "ASSOCIATED_TOKEN_PROGRAM_ID", "FAILED_TO_FIND_ACCOUNT", "INVALID_ACCOUNT_OWNER", "pubkeyToBuffer", "<PERSON><PERSON><PERSON>", "from", "<PERSON><PERSON><PERSON><PERSON>", "u64", "BN", "a", "toArray", "reverse", "b", "length", "assert", "zeroPad", "alloc", "copy", "fromBuffer", "buffer", "map", "i", "toString", "slice", "join", "isAccount", "accountOrPublicKey", "AuthorityTypeCodes", "MintTokens", "FreezeA<PERSON>unt", "Account<PERSON><PERSON><PERSON>", "CloseAccount", "NATIVE_MINT", "MintLayout", "struct", "u32", "Layout", "u8", "AccountLayout", "MultisigLayout", "Token", "constructor", "programId", "payer", "Object", "assign", "associatedProgramId", "getMinBalanceRentForExemptMint", "getMinimumBalanceForRentExemption", "span", "getMinBalanceRentForExemptAccount", "getMinBalanceRentForExemptMultisig", "createMint", "mintAuthority", "freezeAuthority", "decimals", "mintAccount", "Keypair", "generate", "token", "balanceNeeded", "Transaction", "add", "SystemProgram", "createAccount", "fromPubkey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lamports", "space", "createInitMintInstruction", "owner", "newAccount", "mintPublicKey", "createInitAccountInstruction", "createAssociatedTokenAccount", "associatedAddress", "getAssociatedTokenAddress", "createAssociatedTokenAccountInternal", "createAssociatedTokenAccountInstruction", "getOrCreateAssociatedAccountInfo", "getAccountInfo", "err", "message", "createWrappedNativeAccount", "amount", "transfer", "to<PERSON><PERSON><PERSON>", "createMultisig", "m", "multisigAccount", "keys", "pubkey", "<PERSON><PERSON><PERSON><PERSON>", "isWritable", "SYSVAR_RENT_PUBKEY", "for<PERSON>ach", "signer", "push", "dataLayout", "data", "encode", "instruction", "getMintInfo", "info", "Error", "equals", "JSON", "stringify", "mintInfo", "decode", "mintAuthorityOption", "supply", "isInitialized", "freezeAuthorityOption", "account", "commitment", "accountInfo", "address", "mint", "delegateOption", "delegate", "delegated<PERSON><PERSON>", "state", "isFrozen", "isNativeOption", "rentExemptReserve", "isNative", "closeAuthorityOption", "closeAuthority", "getMultisigInfo", "multisig", "multisigInfo", "signer1", "signer2", "signer3", "signer4", "signer5", "signer6", "signer7", "signer8", "signer9", "signer10", "signer11", "source", "destination", "multiSigners", "ownerPublicKey", "createTransferInstruction", "approve", "createApproveInstruction", "revoke", "createRevokeInstruction", "setAuthority", "newAuthority", "authorityType", "currentAuthority", "currentAuthorityPublicKey", "createSetAuthorityInstruction", "mintTo", "dest", "authority", "createMintToInstruction", "burn", "createBurnInstruction", "closeAccount", "authorityPublicKey", "createCloseAccountInstruction", "freezeAccount", "createFreezeAccountInstruction", "thawAccount", "createThawAccountInstruction", "transferChecked", "createTransferCheckedInstruction", "approveChecked", "createApproveCheckedInstruction", "mintToChecked", "createMintToCheckedInstruction", "burnChecked", "createBurnCheckedInstruction", "syncNative", "nativeAccount", "createSyncNativeInstruction", "commandDataLayout", "encodeLength", "option", "TransactionInstruction", "allowOwnerOffCurve", "isOnCurve", "findProgramAddress", "associatedAccount"], "mappings": ";;;;;;;AAAA;AAIA;AACA;AACA;;AACO,MAAMA,SAAS,GAAG,CAACC,QAAQ,GAAW,WAApB,KAA4C;AACnE,SAAOC,YAAY,CAACC,IAAb,CAAkB,EAAlB,EAAsBF,QAAtB,CAAP;AACD,CAFM;AAIP;AACA;AACA;;AACO,MAAMG,MAAM,GAAG,CAACH,QAAQ,GAAW,QAApB,KAAyC;AAC7D,SAAOC,YAAY,CAACC,IAAb,CAAkB,CAAlB,EAAqBF,QAArB,CAAP;AACD,CAFM;;ACdP;AAUO,SAASI,yBAAT,CACLC,KADK,EAELC,UAFK,EAGLC,WAHK,EAIL,GAAGC,OAJE,EAK0B;AAC/B,SAAOC,2BAA6B,CAACH,UAAD,EAAaC,WAAb,EAA0BC,OAA1B,EAAmC;AACrEE,IAAAA,aAAa,EAAE;AADsD,GAAnC,CAApC;AAGD;;MCOYC,gBAAgB,GAAc,IAAIC,SAAJ,CACzC,6CADyC;MAI9BC,2BAA2B,GAAc,IAAID,SAAJ,CACpD,8CADoD;AAItD,MAAME,sBAAsB,GAAG,wBAA/B;AACA,MAAMC,qBAAqB,GAAG,uBAA9B;AAEA;AACA;AACA;AACA;AACA;;AACA,SAASC,cAAT,CAAwBjB,SAAxB,EAA6D;AAC3D,SAAOkB,MAAM,CAACC,IAAP,CAAYnB,SAAS,CAACoB,QAAV,EAAZ,CAAP;AACD;AAED;AACA;AACA;;;AACO,MAAMC,GAAN,SAAkBC,EAAlB,CAAqB;AAC1B;AACF;AACA;AACEF,EAAAA,QAAQ,GAAkB;AACxB,UAAMG,CAAC,GAAG,MAAMC,OAAN,GAAgBC,OAAhB,EAAV;AACA,UAAMC,CAAC,GAAGR,MAAM,CAACC,IAAP,CAAYI,CAAZ,CAAV;;AACA,QAAIG,CAAC,CAACC,MAAF,KAAa,CAAjB,EAAoB;AAClB,aAAOD,CAAP;AACD;;AACDE,IAAAA,MAAM,CAACF,CAAC,CAACC,MAAF,GAAW,CAAZ,EAAe,eAAf,CAAN;AAEA,UAAME,OAAO,GAAGX,MAAM,CAACY,KAAP,CAAa,CAAb,CAAhB;AACAJ,IAAAA,CAAC,CAACK,IAAF,CAAOF,OAAP;AACA,WAAOA,OAAP;AACD;AAED;AACF;AACA;;;AACmB,SAAVG,UAAU,CAACC,MAAD,EAA6B;AAC5CL,IAAAA,MAAM,CAACK,MAAM,CAACN,MAAP,KAAkB,CAAnB,EAAuB,0BAAyBM,MAAM,CAACN,MAAO,EAA9D,CAAN;AACA,WAAO,IAAIN,GAAJ,CACL,CAAC,GAAGY,MAAJ,EACGR,OADH,GAEGS,GAFH,CAEOC,CAAC,IAAK,KAAIA,CAAC,CAACC,QAAF,CAAW,EAAX,CAAe,EAApB,CAAsBC,KAAtB,CAA4B,CAAC,CAA7B,CAFZ,EAGGC,IAHH,CAGQ,EAHR,CADK,EAKL,EALK,CAAP;AAOD;;AA7ByB;;AAgC5B,SAASC,SAAT,CAAmBC,kBAAnB,EAAqD;AACnD,SAAO,eAAeA,kBAAtB;AACD;;AAQD,MAAMC,kBAAkB,GAAG;AACzBC,EAAAA,UAAU,EAAE,CADa;AAEzBC,EAAAA,aAAa,EAAE,CAFU;AAGzBC,EAAAA,YAAY,EAAE,CAHW;AAIzBC,EAAAA,YAAY,EAAE;AAJW,CAA3B;;MAQaC,WAAW,GAAc,IAAIjC,SAAJ,CACpC,6CADoC;AAItC;AACA;AACA;;MA8BakC,UAAU,GAAkC7C,YAAY,CAAC8C,MAAb,CAAoB,CAC3E9C,YAAY,CAAC+C,GAAb,CAAiB,qBAAjB,CAD2E,EAE3EC,SAAA,CAAiB,eAAjB,CAF2E,EAG3EA,MAAA,CAAc,QAAd,CAH2E,EAI3EhD,YAAY,CAACiD,EAAb,CAAgB,UAAhB,CAJ2E,EAK3EjD,YAAY,CAACiD,EAAb,CAAgB,eAAhB,CAL2E,EAM3EjD,YAAY,CAAC+C,GAAb,CAAiB,uBAAjB,CAN2E,EAO3EC,SAAA,CAAiB,iBAAjB,CAP2E,CAApB;AAUzD;AACA;AACA;;AA4DA;AACA;AACA;;MACaE,aAAa,GAAkClD,YAAY,CAAC8C,MAAb,CAC1D,CACEE,SAAA,CAAiB,MAAjB,CADF,EAEEA,SAAA,CAAiB,OAAjB,CAFF,EAGEA,MAAA,CAAc,QAAd,CAHF,EAIEhD,YAAY,CAAC+C,GAAb,CAAiB,gBAAjB,CAJF,EAKEC,SAAA,CAAiB,UAAjB,CALF,EAMEhD,YAAY,CAACiD,EAAb,CAAgB,OAAhB,CANF,EAOEjD,YAAY,CAAC+C,GAAb,CAAiB,gBAAjB,CAPF,EAQEC,MAAA,CAAc,UAAd,CARF,EASEA,MAAA,CAAc,iBAAd,CATF,EAUEhD,YAAY,CAAC+C,GAAb,CAAiB,sBAAjB,CAVF,EAWEC,SAAA,CAAiB,gBAAjB,CAXF,CAD0D;AAgB5D;AACA;AACA;;AAkCA;AACA;AACA;;AACA,MAAMG,cAAc,GAAGnD,YAAY,CAAC8C,MAAb,CAAoB,CACzC9C,YAAY,CAACiD,EAAb,CAAgB,GAAhB,CADyC,EAEzCjD,YAAY,CAACiD,EAAb,CAAgB,GAAhB,CAFyC,EAGzCjD,YAAY,CAACiD,EAAb,CAAgB,gBAAhB,CAHyC,EAIzCD,SAAA,CAAiB,SAAjB,CAJyC,EAKzCA,SAAA,CAAiB,SAAjB,CALyC,EAMzCA,SAAA,CAAiB,SAAjB,CANyC,EAOzCA,SAAA,CAAiB,SAAjB,CAPyC,EAQzCA,SAAA,CAAiB,SAAjB,CARyC,EASzCA,SAAA,CAAiB,SAAjB,CATyC,EAUzCA,SAAA,CAAiB,SAAjB,CAVyC,EAWzCA,SAAA,CAAiB,SAAjB,CAXyC,EAYzCA,SAAA,CAAiB,SAAjB,CAZyC,EAazCA,SAAA,CAAiB,UAAjB,CAbyC,EAczCA,SAAA,CAAiB,UAAjB,CAdyC,CAApB,CAAvB;AAiBA;AACA;AACA;;AACO,MAAMI,KAAN,CAAY;AACjB;AACF;AACA;;AAGE;AACF;AACA;;AAGE;AACF;AACA;;AAGE;AACF;AACA;;AAGE;AACF;AACA;;AAGE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACEC,EAAAA,WAAW,CACThD,UADS,EAETP,SAFS,EAGTwD,SAHS,EAITC,KAJS,EAKT;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AACAC,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoB;AAClBpD,MAAAA,UADkB;AAElBP,MAAAA,SAFkB;AAGlBwD,MAAAA,SAHkB;AAIlBC,MAAAA,KAJkB;AAKlB;AACAG,MAAAA,mBAAmB,EAAE9C;AANH,KAApB;AAQD;AAED;AACF;AACA;AACA;AACA;;;AAC6C,eAA9B+C,8BAA8B,CACzCtD,UADyC,EAExB;AACjB,WAAO,MAAMA,UAAU,CAACuD,iCAAX,CAA6Cf,UAAU,CAACgB,IAAxD,CAAb;AACD;AAED;AACF;AACA;AACA;AACA;;;AACgD,eAAjCC,iCAAiC,CAC5CzD,UAD4C,EAE3B;AACjB,WAAO,MAAMA,UAAU,CAACuD,iCAAX,CACXV,aAAa,CAACW,IADH,CAAb;AAGD;AAED;AACF;AACA;AACA;AACA;;;AACiD,eAAlCE,kCAAkC,CAC7C1D,UAD6C,EAE5B;AACjB,WAAO,MAAMA,UAAU,CAACuD,iCAAX,CACXT,cAAc,CAACU,IADJ,CAAb;AAGD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACyB,eAAVG,UAAU,CACrB3D,UADqB,EAErBkD,KAFqB,EAGrBU,aAHqB,EAIrBC,eAJqB,EAKrBC,QALqB,EAMrBb,SANqB,EAOL;AAChB,UAAMc,WAAW,GAAGC,OAAO,CAACC,QAAR,EAApB;AACA,UAAMC,KAAK,GAAG,IAAInB,KAAJ,CACZ/C,UADY,EAEZ+D,WAAW,CAACtE,SAFA,EAGZwD,SAHY,EAIZC,KAJY,CAAd,CAFgB;;AAUhB,UAAMiB,aAAa,GAAG,MAAMpB,KAAK,CAACO,8BAAN,CAC1BtD,UAD0B,CAA5B;AAIA,UAAMC,WAAW,GAAG,IAAImE,WAAJ,EAApB;AACAnE,IAAAA,WAAW,CAACoE,GAAZ,CACEC,aAAa,CAACC,aAAd,CAA4B;AAC1BC,MAAAA,UAAU,EAAEtB,KAAK,CAACzD,SADQ;AAE1BgF,MAAAA,gBAAgB,EAAEV,WAAW,CAACtE,SAFJ;AAG1BiF,MAAAA,QAAQ,EAAEP,aAHgB;AAI1BQ,MAAAA,KAAK,EAAEnC,UAAU,CAACgB,IAJQ;AAK1BP,MAAAA;AAL0B,KAA5B,CADF;AAUAhD,IAAAA,WAAW,CAACoE,GAAZ,CACEtB,KAAK,CAAC6B,yBAAN,CACE3B,SADF,EAEEc,WAAW,CAACtE,SAFd,EAGEqE,QAHF,EAIEF,aAJF,EAKEC,eALF,CADF,EAzBgB;;AAoChB,UAAM/D,yBAAyB,CAC7B,kCAD6B,EAE7BE,UAF6B,EAG7BC,WAH6B,EAI7BiD,KAJ6B,EAK7Ba,WAL6B,CAA/B;AAQA,WAAOG,KAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqB,QAAbK,aAAa,CAACM,KAAD,EAAuC;AACxD;AACA,UAAMV,aAAa,GAAG,MAAMpB,KAAK,CAACU,iCAAN,CAC1B,KAAKzD,UADqB,CAA5B;AAIA,UAAM8E,UAAU,GAAGd,OAAO,CAACC,QAAR,EAAnB;AACA,UAAMhE,WAAW,GAAG,IAAImE,WAAJ,EAApB;AACAnE,IAAAA,WAAW,CAACoE,GAAZ,CACEC,aAAa,CAACC,aAAd,CAA4B;AAC1BC,MAAAA,UAAU,EAAE,KAAKtB,KAAL,CAAWzD,SADG;AAE1BgF,MAAAA,gBAAgB,EAAEK,UAAU,CAACrF,SAFH;AAG1BiF,MAAAA,QAAQ,EAAEP,aAHgB;AAI1BQ,MAAAA,KAAK,EAAE9B,aAAa,CAACW,IAJK;AAK1BP,MAAAA,SAAS,EAAE,KAAKA;AALU,KAA5B,CADF;AAUA,UAAM8B,aAAa,GAAG,KAAKtF,SAA3B;AACAQ,IAAAA,WAAW,CAACoE,GAAZ,CACEtB,KAAK,CAACiC,4BAAN,CACE,KAAK/B,SADP,EAEE8B,aAFF,EAGED,UAAU,CAACrF,SAHb,EAIEoF,KAJF,CADF,EAnBwD;;AA6BxD,UAAM/E,yBAAyB,CAC7B,qCAD6B,EAE7B,KAAKE,UAFwB,EAG7BC,WAH6B,EAI7B,KAAKiD,KAJwB,EAK7B4B,UAL6B,CAA/B;AAQA,WAAOA,UAAU,CAACrF,SAAlB;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoC,QAA5BwF,4BAA4B,CAACJ,KAAD,EAAuC;AACvE,UAAMK,iBAAiB,GAAG,MAAMnC,KAAK,CAACoC,yBAAN,CAC9B,KAAK9B,mBADyB,EAE9B,KAAKJ,SAFyB,EAG9B,KAAKxD,SAHyB,EAI9BoF,KAJ8B,CAAhC;AAOA,WAAO,KAAKO,oCAAL,CAA0CP,KAA1C,EAAiDK,iBAAjD,CAAP;AACD;;AAEyC,QAApCE,oCAAoC,CACxCP,KADwC,EAExCK,iBAFwC,EAGpB;AACpB,UAAMpF,yBAAyB,CAC7B,8BAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACsC,uCAAN,CACE,KAAKhC,mBADP,EAEE,KAAKJ,SAFP,EAGE,KAAKxD,SAHP,EAIEyF,iBAJF,EAKEL,KALF,EAME,KAAK3B,KAAL,CAAWzD,SANb,CADF,CAH6B,EAa7B,KAAKyD,KAbwB,CAA/B;AAgBA,WAAOgC,iBAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACwC,QAAhCI,gCAAgC,CACpCT,KADoC,EAEd;AACtB,UAAMK,iBAAiB,GAAG,MAAMnC,KAAK,CAACoC,yBAAN,CAC9B,KAAK9B,mBADyB,EAE9B,KAAKJ,SAFyB,EAG9B,KAAKxD,SAHyB,EAI9BoF,KAJ8B,CAAhC,CADsB;AAStB;AACA;;AACA,QAAI;AACF,aAAO,MAAM,KAAKU,cAAL,CAAoBL,iBAApB,CAAb;AACD,KAFD,CAEE,OAAOM,GAAP,EAAY;AACZ;AACA;AACA;AACA;AACA,UACEA,GAAG,CAACC,OAAJ,KAAgBjF,sBAAhB,IACAgF,GAAG,CAACC,OAAJ,KAAgBhF,qBAFlB,EAGE;AACA;AACA;AACA,YAAI;AACF,gBAAM,KAAK2E,oCAAL,CACJP,KADI,EAEJK,iBAFI,CAAN;AAID,SALD,CAKE,OAAOM,GAAP,EAAY;AAEZ;AACA;AACD,SAZD;;;AAeA,eAAO,MAAM,KAAKD,cAAL,CAAoBL,iBAApB,CAAb;AACD,OAnBD,MAmBO;AACL,cAAMM,GAAN;AACD;AACF;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACyC,eAA1BE,0BAA0B,CACrC1F,UADqC,EAErCiD,SAFqC,EAGrC4B,KAHqC,EAIrC3B,KAJqC,EAKrCyC,MALqC,EAMjB;AACpB;AACA,UAAMxB,aAAa,GAAG,MAAMpB,KAAK,CAACU,iCAAN,CAC1BzD,UAD0B,CAA5B,CAFoB;;AAOpB,UAAM8E,UAAU,GAAGd,OAAO,CAACC,QAAR,EAAnB;AACA,UAAMhE,WAAW,GAAG,IAAImE,WAAJ,EAApB;AACAnE,IAAAA,WAAW,CAACoE,GAAZ,CACEC,aAAa,CAACC,aAAd,CAA4B;AAC1BC,MAAAA,UAAU,EAAEtB,KAAK,CAACzD,SADQ;AAE1BgF,MAAAA,gBAAgB,EAAEK,UAAU,CAACrF,SAFH;AAG1BiF,MAAAA,QAAQ,EAAEP,aAHgB;AAI1BQ,MAAAA,KAAK,EAAE9B,aAAa,CAACW,IAJK;AAK1BP,MAAAA;AAL0B,KAA5B,CADF,EAToB;;AAoBpBhD,IAAAA,WAAW,CAACoE,GAAZ,CACEC,aAAa,CAACsB,QAAd,CAAuB;AACrBpB,MAAAA,UAAU,EAAEtB,KAAK,CAACzD,SADG;AAErBoG,MAAAA,QAAQ,EAAEf,UAAU,CAACrF,SAFA;AAGrBiF,MAAAA,QAAQ,EAAEiB;AAHW,KAAvB,CADF,EApBoB;AA6BpB;AACA;;AACA1F,IAAAA,WAAW,CAACoE,GAAZ,CACEtB,KAAK,CAACiC,4BAAN,CACE/B,SADF,EAEEV,WAFF,EAGEuC,UAAU,CAACrF,SAHb,EAIEoF,KAJF,CADF,EA/BoB;;AAyCpB,UAAM/E,yBAAyB,CAC7B,gDAD6B,EAE7BE,UAF6B,EAG7BC,WAH6B,EAI7BiD,KAJ6B,EAK7B4B,UAL6B,CAA/B;AAQA,WAAOA,UAAU,CAACrF,SAAlB;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsB,QAAdqG,cAAc,CAClBC,CADkB,EAElB7F,OAFkB,EAGE;AACpB,UAAM8F,eAAe,GAAGhC,OAAO,CAACC,QAAR,EAAxB,CADoB;;AAIpB,UAAME,aAAa,GAAG,MAAMpB,KAAK,CAACW,kCAAN,CAC1B,KAAK1D,UADqB,CAA5B;AAGA,UAAMC,WAAW,GAAG,IAAImE,WAAJ,EAApB;AACAnE,IAAAA,WAAW,CAACoE,GAAZ,CACEC,aAAa,CAACC,aAAd,CAA4B;AAC1BC,MAAAA,UAAU,EAAE,KAAKtB,KAAL,CAAWzD,SADG;AAE1BgF,MAAAA,gBAAgB,EAAEuB,eAAe,CAACvG,SAFR;AAG1BiF,MAAAA,QAAQ,EAAEP,aAHgB;AAI1BQ,MAAAA,KAAK,EAAE7B,cAAc,CAACU,IAJI;AAK1BP,MAAAA,SAAS,EAAE,KAAKA;AALU,KAA5B,CADF,EARoB;;AAmBpB,QAAIgD,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEF,eAAe,CAACvG,SAAzB;AAAoC0G,MAAAA,QAAQ,EAAE,KAA9C;AAAqDC,MAAAA,UAAU,EAAE;AAAjE,KADS,EAET;AAACF,MAAAA,MAAM,EAAEG,kBAAT;AAA6BF,MAAAA,QAAQ,EAAE,KAAvC;AAA8CC,MAAAA,UAAU,EAAE;AAA1D,KAFS,CAAX;AAIAlG,IAAAA,OAAO,CAACoG,OAAR,CAAgBC,MAAM,IACpBN,IAAI,CAACO,IAAL,CAAU;AAACN,MAAAA,MAAM,EAAEK,MAAT;AAAiBJ,MAAAA,QAAQ,EAAE,KAA3B;AAAkCC,MAAAA,UAAU,EAAE;AAA9C,KAAV,CADF;AAGA,UAAMK,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCjD,YAAY,CAACiD,EAAb,CAAgB,GAAhB,CAFqC,CAApB,CAAnB;AAIA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBb,MAAAA;AAFF,KADF,EAKEW,IALF;AAOAzG,IAAAA,WAAW,CAACoE,GAAZ,CAAgB;AACd4B,MAAAA,IADc;AAEdhD,MAAAA,SAAS,EAAE,KAAKA,SAFF;AAGdyD,MAAAA;AAHc,KAAhB,EAtCoB;;AA6CpB,UAAM5G,yBAAyB,CAC7B,sCAD6B,EAE7B,KAAKE,UAFwB,EAG7BC,WAH6B,EAI7B,KAAKiD,KAJwB,EAK7B8C,eAL6B,CAA/B;AAQA,WAAOA,eAAe,CAACvG,SAAvB;AACD;AAED;AACF;AACA;;;AACmB,QAAXoH,WAAW,GAAsB;AACrC,UAAMC,IAAI,GAAG,MAAM,KAAK9G,UAAL,CAAgBuF,cAAhB,CAA+B,KAAK9F,SAApC,CAAnB;;AACA,QAAIqH,IAAI,KAAK,IAAb,EAAmB;AACjB,YAAM,IAAIC,KAAJ,CAAU,6BAAV,CAAN;AACD;;AACD,QAAI,CAACD,IAAI,CAACjC,KAAL,CAAWmC,MAAX,CAAkB,KAAK/D,SAAvB,CAAL,EAAwC;AACtC,YAAM,IAAI8D,KAAJ,CAAW,uBAAsBE,IAAI,CAACC,SAAL,CAAeJ,IAAI,CAACjC,KAApB,CAA2B,EAA5D,CAAN;AACD;;AACD,QAAIiC,IAAI,CAACJ,IAAL,CAAUtF,MAAV,IAAoBoB,UAAU,CAACgB,IAAnC,EAAyC;AACvC,YAAM,IAAIuD,KAAJ,CAAW,mBAAX,CAAN;AACD;;AAED,UAAML,IAAI,GAAG/F,MAAM,CAACC,IAAP,CAAYkG,IAAI,CAACJ,IAAjB,CAAb;AACA,UAAMS,QAAQ,GAAG3E,UAAU,CAAC4E,MAAX,CAAkBV,IAAlB,CAAjB;;AAEA,QAAIS,QAAQ,CAACE,mBAAT,KAAiC,CAArC,EAAwC;AACtCF,MAAAA,QAAQ,CAACvD,aAAT,GAAyB,IAAzB;AACD,KAFD,MAEO;AACLuD,MAAAA,QAAQ,CAACvD,aAAT,GAAyB,IAAItD,SAAJ,CAAc6G,QAAQ,CAACvD,aAAvB,CAAzB;AACD;;AAEDuD,IAAAA,QAAQ,CAACG,MAAT,GAAkBxG,GAAG,CAACW,UAAJ,CAAe0F,QAAQ,CAACG,MAAxB,CAAlB;AACAH,IAAAA,QAAQ,CAACI,aAAT,GAAyBJ,QAAQ,CAACI,aAAT,IAA0B,CAAnD;;AAEA,QAAIJ,QAAQ,CAACK,qBAAT,KAAmC,CAAvC,EAA0C;AACxCL,MAAAA,QAAQ,CAACtD,eAAT,GAA2B,IAA3B;AACD,KAFD,MAEO;AACLsD,MAAAA,QAAQ,CAACtD,eAAT,GAA2B,IAAIvD,SAAJ,CAAc6G,QAAQ,CAACtD,eAAvB,CAA3B;AACD;;AACD,WAAOsD,QAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACsB,QAAd5B,cAAc,CAClBkC,OADkB,EAElBC,UAFkB,EAGI;AACtB,UAAMZ,IAAI,GAAG,MAAM,KAAK9G,UAAL,CAAgBuF,cAAhB,CAA+BkC,OAA/B,EAAwCC,UAAxC,CAAnB;;AACA,QAAIZ,IAAI,KAAK,IAAb,EAAmB;AACjB,YAAM,IAAIC,KAAJ,CAAUvG,sBAAV,CAAN;AACD;;AACD,QAAI,CAACsG,IAAI,CAACjC,KAAL,CAAWmC,MAAX,CAAkB,KAAK/D,SAAvB,CAAL,EAAwC;AACtC,YAAM,IAAI8D,KAAJ,CAAUtG,qBAAV,CAAN;AACD;;AACD,QAAIqG,IAAI,CAACJ,IAAL,CAAUtF,MAAV,IAAoByB,aAAa,CAACW,IAAtC,EAA4C;AAC1C,YAAM,IAAIuD,KAAJ,CAAW,sBAAX,CAAN;AACD;;AAED,UAAML,IAAI,GAAG/F,MAAM,CAACC,IAAP,CAAYkG,IAAI,CAACJ,IAAjB,CAAb;AACA,UAAMiB,WAAW,GAAG9E,aAAa,CAACuE,MAAd,CAAqBV,IAArB,CAApB;AACAiB,IAAAA,WAAW,CAACC,OAAZ,GAAsBH,OAAtB;AACAE,IAAAA,WAAW,CAACE,IAAZ,GAAmB,IAAIvH,SAAJ,CAAcqH,WAAW,CAACE,IAA1B,CAAnB;AACAF,IAAAA,WAAW,CAAC9C,KAAZ,GAAoB,IAAIvE,SAAJ,CAAcqH,WAAW,CAAC9C,KAA1B,CAApB;AACA8C,IAAAA,WAAW,CAAChC,MAAZ,GAAqB7E,GAAG,CAACW,UAAJ,CAAekG,WAAW,CAAChC,MAA3B,CAArB;;AAEA,QAAIgC,WAAW,CAACG,cAAZ,KAA+B,CAAnC,EAAsC;AACpCH,MAAAA,WAAW,CAACI,QAAZ,GAAuB,IAAvB;AACAJ,MAAAA,WAAW,CAACK,eAAZ,GAA8B,IAAIlH,GAAJ,EAA9B;AACD,KAHD,MAGO;AACL6G,MAAAA,WAAW,CAACI,QAAZ,GAAuB,IAAIzH,SAAJ,CAAcqH,WAAW,CAACI,QAA1B,CAAvB;AACAJ,MAAAA,WAAW,CAACK,eAAZ,GAA8BlH,GAAG,CAACW,UAAJ,CAAekG,WAAW,CAACK,eAA3B,CAA9B;AACD;;AAEDL,IAAAA,WAAW,CAACJ,aAAZ,GAA4BI,WAAW,CAACM,KAAZ,KAAsB,CAAlD;AACAN,IAAAA,WAAW,CAACO,QAAZ,GAAuBP,WAAW,CAACM,KAAZ,KAAsB,CAA7C;;AAEA,QAAIN,WAAW,CAACQ,cAAZ,KAA+B,CAAnC,EAAsC;AACpCR,MAAAA,WAAW,CAACS,iBAAZ,GAAgCtH,GAAG,CAACW,UAAJ,CAAekG,WAAW,CAACU,QAA3B,CAAhC;AACAV,MAAAA,WAAW,CAACU,QAAZ,GAAuB,IAAvB;AACD,KAHD,MAGO;AACLV,MAAAA,WAAW,CAACS,iBAAZ,GAAgC,IAAhC;AACAT,MAAAA,WAAW,CAACU,QAAZ,GAAuB,KAAvB;AACD;;AAED,QAAIV,WAAW,CAACW,oBAAZ,KAAqC,CAAzC,EAA4C;AAC1CX,MAAAA,WAAW,CAACY,cAAZ,GAA6B,IAA7B;AACD,KAFD,MAEO;AACLZ,MAAAA,WAAW,CAACY,cAAZ,GAA6B,IAAIjI,SAAJ,CAAcqH,WAAW,CAACY,cAA1B,CAA7B;AACD;;AAED,QAAI,CAACZ,WAAW,CAACE,IAAZ,CAAiBb,MAAjB,CAAwB,KAAKvH,SAA7B,CAAL,EAA8C;AAC5C,YAAM,IAAIsH,KAAJ,CACH,yBAAwBE,IAAI,CAACC,SAAL,CACvBS,WAAW,CAACE,IADW,CAEvB,QAAOZ,IAAI,CAACC,SAAL,CAAe,KAAKzH,SAApB,CAA+B,EAHpC,CAAN;AAKD;;AACD,WAAOkI,WAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACuB,QAAfa,eAAe,CAACC,QAAD,EAA6C;AAChE,UAAM3B,IAAI,GAAG,MAAM,KAAK9G,UAAL,CAAgBuF,cAAhB,CAA+BkD,QAA/B,CAAnB;;AACA,QAAI3B,IAAI,KAAK,IAAb,EAAmB;AACjB,YAAM,IAAIC,KAAJ,CAAU,yBAAV,CAAN;AACD;;AACD,QAAI,CAACD,IAAI,CAACjC,KAAL,CAAWmC,MAAX,CAAkB,KAAK/D,SAAvB,CAAL,EAAwC;AACtC,YAAM,IAAI8D,KAAJ,CAAW,wBAAX,CAAN;AACD;;AACD,QAAID,IAAI,CAACJ,IAAL,CAAUtF,MAAV,IAAoB0B,cAAc,CAACU,IAAvC,EAA6C;AAC3C,YAAM,IAAIuD,KAAJ,CAAW,uBAAX,CAAN;AACD;;AAED,UAAML,IAAI,GAAG/F,MAAM,CAACC,IAAP,CAAYkG,IAAI,CAACJ,IAAjB,CAAb;AACA,UAAMgC,YAAY,GAAG5F,cAAc,CAACsE,MAAf,CAAsBV,IAAtB,CAArB;AACAgC,IAAAA,YAAY,CAACC,OAAb,GAAuB,IAAIrI,SAAJ,CAAcoI,YAAY,CAACC,OAA3B,CAAvB;AACAD,IAAAA,YAAY,CAACE,OAAb,GAAuB,IAAItI,SAAJ,CAAcoI,YAAY,CAACE,OAA3B,CAAvB;AACAF,IAAAA,YAAY,CAACG,OAAb,GAAuB,IAAIvI,SAAJ,CAAcoI,YAAY,CAACG,OAA3B,CAAvB;AACAH,IAAAA,YAAY,CAACI,OAAb,GAAuB,IAAIxI,SAAJ,CAAcoI,YAAY,CAACI,OAA3B,CAAvB;AACAJ,IAAAA,YAAY,CAACK,OAAb,GAAuB,IAAIzI,SAAJ,CAAcoI,YAAY,CAACK,OAA3B,CAAvB;AACAL,IAAAA,YAAY,CAACM,OAAb,GAAuB,IAAI1I,SAAJ,CAAcoI,YAAY,CAACM,OAA3B,CAAvB;AACAN,IAAAA,YAAY,CAACO,OAAb,GAAuB,IAAI3I,SAAJ,CAAcoI,YAAY,CAACO,OAA3B,CAAvB;AACAP,IAAAA,YAAY,CAACQ,OAAb,GAAuB,IAAI5I,SAAJ,CAAcoI,YAAY,CAACQ,OAA3B,CAAvB;AACAR,IAAAA,YAAY,CAACS,OAAb,GAAuB,IAAI7I,SAAJ,CAAcoI,YAAY,CAACS,OAA3B,CAAvB;AACAT,IAAAA,YAAY,CAACU,QAAb,GAAwB,IAAI9I,SAAJ,CAAcoI,YAAY,CAACU,QAA3B,CAAxB;AACAV,IAAAA,YAAY,CAACW,QAAb,GAAwB,IAAI/I,SAAJ,CAAcoI,YAAY,CAACW,QAA3B,CAAxB;AAEA,WAAOX,YAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,QAAR9C,QAAQ,CACZ0D,MADY,EAEZC,WAFY,EAGZ1E,KAHY,EAIZ2E,YAJY,EAKZ7D,MALY,EAMmB;AAC/B,QAAI8D,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAAC6C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAACpF,SAAvB;AACAS,MAAAA,OAAO,GAAG,CAAC2E,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACA3E,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,WAAO,MAAM1J,yBAAyB,CACpC,UADoC,EAEpC,KAAKE,UAF+B,EAGpC,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC2G,yBAAN,CACE,KAAKzG,SADP,EAEEqG,MAFF,EAGEC,WAHF,EAIEE,cAJF,EAKED,YALF,EAME7D,MANF,CADF,CAHoC,EAapC,KAAKzC,KAb+B,EAcpC,GAAGhD,OAdiC,CAAtC;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACe,QAAPyJ,OAAO,CACXlC,OADW,EAEXM,QAFW,EAGXlD,KAHW,EAIX2E,YAJW,EAKX7D,MALW,EAMI;AACf,QAAI8D,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAAC6C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAACpF,SAAvB;AACAS,MAAAA,OAAO,GAAG,CAAC2E,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACA3E,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,SAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC6G,wBAAN,CACE,KAAK3G,SADP,EAEEwE,OAFF,EAGEM,QAHF,EAIE0B,cAJF,EAKED,YALF,EAME7D,MANF,CADF,CAH6B,EAa7B,KAAKzC,KAbwB,EAc7B,GAAGhD,OAd0B,CAA/B;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACc,QAAN2J,MAAM,CACVpC,OADU,EAEV5C,KAFU,EAGV2E,YAHU,EAIK;AACf,QAAIC,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAAC6C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAACpF,SAAvB;AACAS,MAAAA,OAAO,GAAG,CAAC2E,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACA3E,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,QAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC+G,uBAAN,CACE,KAAK7G,SADP,EAEEwE,OAFF,EAGEgC,cAHF,EAIED,YAJF,CADF,CAH6B,EAW7B,KAAKtG,KAXwB,EAY7B,GAAGhD,OAZ0B,CAA/B;AAcD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoB,QAAZ6J,YAAY,CAChBtC,OADgB,EAEhBuC,YAFgB,EAGhBC,aAHgB,EAIhBC,gBAJgB,EAKhBV,YALgB,EAMD;AACf,QAAIW,yBAAJ;AACA,QAAIjK,OAAJ;;AACA,QAAI8B,SAAS,CAACkI,gBAAD,CAAb,EAAiC;AAC/BC,MAAAA,yBAAyB,GAAGD,gBAAgB,CAACzK,SAA7C;AACAS,MAAAA,OAAO,GAAG,CAACgK,gBAAD,CAAV;AACD,KAHD,MAGO;AACLC,MAAAA,yBAAyB,GAAGD,gBAA5B;AACAhK,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,cAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACqH,6BAAN,CACE,KAAKnH,SADP,EAEEwE,OAFF,EAGEuC,YAHF,EAIEC,aAJF,EAKEE,yBALF,EAMEX,YANF,CADF,CAH6B,EAa7B,KAAKtG,KAbwB,EAc7B,GAAGhD,OAd0B,CAA/B;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACc,QAANmK,MAAM,CACVC,IADU,EAEVC,SAFU,EAGVf,YAHU,EAIV7D,MAJU,EAKK;AACf,QAAI8D,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAACuI,SAAD,CAAb,EAA0B;AACxBd,MAAAA,cAAc,GAAGc,SAAS,CAAC9K,SAA3B;AACAS,MAAAA,OAAO,GAAG,CAACqK,SAAD,CAAV;AACD,KAHD,MAGO;AACLd,MAAAA,cAAc,GAAGc,SAAjB;AACArK,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,QAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACyH,uBAAN,CACE,KAAKvH,SADP,EAEE,KAAKxD,SAFP,EAGE6K,IAHF,EAIEb,cAJF,EAKED,YALF,EAME7D,MANF,CADF,CAH6B,EAa7B,KAAKzC,KAbwB,EAc7B,GAAGhD,OAd0B,CAA/B;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACY,QAAJuK,IAAI,CACRhD,OADQ,EAER5C,KAFQ,EAGR2E,YAHQ,EAIR7D,MAJQ,EAKO;AACf,QAAI8D,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAAC6C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAACpF,SAAvB;AACAS,MAAAA,OAAO,GAAG,CAAC2E,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACA3E,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,MAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC2H,qBAAN,CACE,KAAKzH,SADP,EAEE,KAAKxD,SAFP,EAGEgI,OAHF,EAIEgC,cAJF,EAKED,YALF,EAME7D,MANF,CADF,CAH6B,EAa7B,KAAKzC,KAbwB,EAc7B,GAAGhD,OAd0B,CAA/B;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoB,QAAZyK,YAAY,CAChBlD,OADgB,EAEhB6C,IAFgB,EAGhBC,SAHgB,EAIhBf,YAJgB,EAKD;AACf,QAAIoB,kBAAJ;AACA,QAAI1K,OAAJ;;AACA,QAAI8B,SAAS,CAACuI,SAAD,CAAb,EAA0B;AACxBK,MAAAA,kBAAkB,GAAGL,SAAS,CAAC9K,SAA/B;AACAS,MAAAA,OAAO,GAAG,CAACqK,SAAD,CAAV;AACD,KAHD,MAGO;AACLK,MAAAA,kBAAkB,GAAGL,SAArB;AACArK,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,cAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC8H,6BAAN,CACE,KAAK5H,SADP,EAEEwE,OAFF,EAGE6C,IAHF,EAIEM,kBAJF,EAKEpB,YALF,CADF,CAH6B,EAY7B,KAAKtG,KAZwB,EAa7B,GAAGhD,OAb0B,CAA/B;AAeD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACqB,QAAb4K,aAAa,CACjBrD,OADiB,EAEjB8C,SAFiB,EAGjBf,YAHiB,EAIF;AACf,QAAIoB,kBAAJ;AACA,QAAI1K,OAAJ;;AACA,QAAI8B,SAAS,CAACuI,SAAD,CAAb,EAA0B;AACxBK,MAAAA,kBAAkB,GAAGL,SAAS,CAAC9K,SAA/B;AACAS,MAAAA,OAAO,GAAG,CAACqK,SAAD,CAAV;AACD,KAHD,MAGO;AACLK,MAAAA,kBAAkB,GAAGL,SAArB;AACArK,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,eAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACgI,8BAAN,CACE,KAAK9H,SADP,EAEEwE,OAFF,EAGE,KAAKhI,SAHP,EAIEmL,kBAJF,EAKEpB,YALF,CADF,CAH6B,EAY7B,KAAKtG,KAZwB,EAa7B,GAAGhD,OAb0B,CAA/B;AAeD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACmB,QAAX8K,WAAW,CACfvD,OADe,EAEf8C,SAFe,EAGff,YAHe,EAIA;AACf,QAAIoB,kBAAJ;AACA,QAAI1K,OAAJ;;AACA,QAAI8B,SAAS,CAACuI,SAAD,CAAb,EAA0B;AACxBK,MAAAA,kBAAkB,GAAGL,SAAS,CAAC9K,SAA/B;AACAS,MAAAA,OAAO,GAAG,CAACqK,SAAD,CAAV;AACD,KAHD,MAGO;AACLK,MAAAA,kBAAkB,GAAGL,SAArB;AACArK,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,aAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACkI,4BAAN,CACE,KAAKhI,SADP,EAEEwE,OAFF,EAGE,KAAKhI,SAHP,EAIEmL,kBAJF,EAKEpB,YALF,CADF,CAH6B,EAY7B,KAAKtG,KAZwB,EAa7B,GAAGhD,OAb0B,CAA/B;AAeD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACuB,QAAfgL,eAAe,CACnB5B,MADmB,EAEnBC,WAFmB,EAGnB1E,KAHmB,EAInB2E,YAJmB,EAKnB7D,MALmB,EAMnB7B,QANmB,EAOY;AAC/B,QAAI2F,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAAC6C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAACpF,SAAvB;AACAS,MAAAA,OAAO,GAAG,CAAC2E,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACA3E,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,WAAO,MAAM1J,yBAAyB,CACpC,iBADoC,EAEpC,KAAKE,UAF+B,EAGpC,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACoI,gCAAN,CACE,KAAKlI,SADP,EAEEqG,MAFF,EAGE,KAAK7J,SAHP,EAIE8J,WAJF,EAKEE,cALF,EAMED,YANF,EAOE7D,MAPF,EAQE7B,QARF,CADF,CAHoC,EAepC,KAAKZ,KAf+B,EAgBpC,GAAGhD,OAhBiC,CAAtC;AAkBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsB,QAAdkL,cAAc,CAClB3D,OADkB,EAElBM,QAFkB,EAGlBlD,KAHkB,EAIlB2E,YAJkB,EAKlB7D,MALkB,EAMlB7B,QANkB,EAOH;AACf,QAAI2F,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAAC6C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAACpF,SAAvB;AACAS,MAAAA,OAAO,GAAG,CAAC2E,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACA3E,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,gBAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACsI,+BAAN,CACE,KAAKpI,SADP,EAEEwE,OAFF,EAGE,KAAKhI,SAHP,EAIEsI,QAJF,EAKE0B,cALF,EAMED,YANF,EAOE7D,MAPF,EAQE7B,QARF,CADF,CAH6B,EAe7B,KAAKZ,KAfwB,EAgB7B,GAAGhD,OAhB0B,CAA/B;AAkBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqB,QAAboL,aAAa,CACjBhB,IADiB,EAEjBC,SAFiB,EAGjBf,YAHiB,EAIjB7D,MAJiB,EAKjB7B,QALiB,EAMF;AACf,QAAI2F,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAACuI,SAAD,CAAb,EAA0B;AACxBd,MAAAA,cAAc,GAAGc,SAAS,CAAC9K,SAA3B;AACAS,MAAAA,OAAO,GAAG,CAACqK,SAAD,CAAV;AACD,KAHD,MAGO;AACLd,MAAAA,cAAc,GAAGc,SAAjB;AACArK,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,eAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACwI,8BAAN,CACE,KAAKtI,SADP,EAEE,KAAKxD,SAFP,EAGE6K,IAHF,EAIEb,cAJF,EAKED,YALF,EAME7D,MANF,EAOE7B,QAPF,CADF,CAH6B,EAc7B,KAAKZ,KAdwB,EAe7B,GAAGhD,OAf0B,CAA/B;AAiBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACmB,QAAXsL,WAAW,CACf/D,OADe,EAEf5C,KAFe,EAGf2E,YAHe,EAIf7D,MAJe,EAKf7B,QALe,EAMA;AACf,QAAI2F,cAAJ;AACA,QAAIvJ,OAAJ;;AACA,QAAI8B,SAAS,CAAC6C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAACpF,SAAvB;AACAS,MAAAA,OAAO,GAAG,CAAC2E,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACA3E,MAAAA,OAAO,GAAGsJ,YAAV;AACD;;AACD,UAAM1J,yBAAyB,CAC7B,aAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC0I,4BAAN,CACE,KAAKxI,SADP,EAEE,KAAKxD,SAFP,EAGEgI,OAHF,EAIEgC,cAJF,EAKED,YALF,EAME7D,MANF,EAOE7B,QAPF,CADF,CAH6B,EAc7B,KAAKZ,KAdwB,EAe7B,GAAGhD,OAf0B,CAA/B;AAiBD;AAED;AACF;AACA;AACA;AACA;;;AACkB,QAAVwL,UAAU,CAACC,aAAD,EAA0C;AACxD,UAAM7L,yBAAyB,CAC7B,YAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIoE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC6I,2BAAN,CAAkC,KAAK3I,SAAvC,EAAkD0I,aAAlD,CADF,CAH6B,EAM7B,KAAKzI,KANwB,CAA/B;AAQD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACkC,SAAzB0B,yBAAyB,CAC9B3B,SAD8B,EAE9B4E,IAF8B,EAG9B/D,QAH8B,EAI9BF,aAJ8B,EAK9BC,eAL8B,EAMN;AACxB,QAAIoC,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEG,kBAAT;AAA6BF,MAAAA,QAAQ,EAAE,KAAvC;AAA8CC,MAAAA,UAAU,EAAE;AAA1D,KAFS,CAAX;AAIA,UAAMyF,iBAAiB,GAAGlM,YAAY,CAAC8C,MAAb,CAAoB,CAC5C9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CAD4C,EAE5CjD,YAAY,CAACiD,EAAb,CAAgB,UAAhB,CAF4C,EAG5CD,SAAA,CAAiB,eAAjB,CAH4C,EAI5ChD,YAAY,CAACiD,EAAb,CAAgB,QAAhB,CAJ4C,EAK5CD,SAAA,CAAiB,iBAAjB,CAL4C,CAApB,CAA1B;AAOA,QAAI+D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAa,IAAb,CAAX;AACA;AACE,YAAMuK,YAAY,GAAGD,iBAAiB,CAAClF,MAAlB,CACnB;AACEC,QAAAA,WAAW,EAAE,CADf;AACkB;AAChB9C,QAAAA,QAFF;AAGEF,QAAAA,aAAa,EAAElD,cAAc,CAACkD,aAAD,CAH/B;AAIEmI,QAAAA,MAAM,EAAElI,eAAe,KAAK,IAApB,GAA2B,CAA3B,GAA+B,CAJzC;AAKEA,QAAAA,eAAe,EAAEnD,cAAc,CAACmD,eAAe,IAAI,IAAIvD,SAAJ,CAAc,CAAd,CAApB;AALjC,OADmB,EAQnBoG,IARmB,CAArB;AAUAA,MAAAA,IAAI,GAAGA,IAAI,CAAC5E,KAAL,CAAW,CAAX,EAAcgK,YAAd,CAAP;AACD;AAED,WAAO,IAAIE,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAFgC;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqC,SAA5B1B,4BAA4B,CACjC/B,SADiC,EAEjC4E,IAFiC,EAGjCJ,OAHiC,EAIjC5C,KAJiC,EAKT;AACxB,UAAMoB,IAAI,GAAG,CACX;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADW,EAEX;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFW,EAGX;AAACF,MAAAA,MAAM,EAAErB,KAAT;AAAgBsB,MAAAA,QAAQ,EAAE,KAA1B;AAAiCC,MAAAA,UAAU,EAAE;AAA7C,KAHW,EAIX;AAACF,MAAAA,MAAM,EAAEG,kBAAT;AAA6BF,MAAAA,QAAQ,EAAE,KAAvC;AAA8CC,MAAAA,UAAU,EAAE;AAA1D,KAJW,CAAb;AAMA,UAAMK,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CAAC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AACA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;;AAAA,KADF,EAIEF,IAJF;AAOA,WAAO,IAAIsF,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAFgC;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACkC,SAAzBgD,yBAAyB,CAC9BzG,SAD8B,EAE9BqG,MAF8B,EAG9BC,WAH8B,EAI9B1E,KAJ8B,EAK9B2E,YAL8B,EAM9B7D,MAN8B,EAON;AACxB,UAAMc,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCD,MAAA,CAAc,QAAd,CAFqC,CAApB,CAAnB;AAKA,UAAM+D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBjB,MAAAA,MAAM,EAAE,IAAI7E,GAAJ,CAAQ6E,MAAR,EAAgB9E,QAAhB;AAFV,KADF,EAKE6F,IALF;AAQA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEoD,MAAT;AAAiBnD,MAAAA,QAAQ,EAAE,KAA3B;AAAkCC,MAAAA,UAAU,EAAE;AAA9C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEqD,WAAT;AAAsBpD,MAAAA,QAAQ,EAAE,KAAhC;AAAuCC,MAAAA,UAAU,EAAE;AAAnD,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAErB,KADA;AAERsB,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AACD,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACiC,SAAxBkD,wBAAwB,CAC7B3G,SAD6B,EAE7BwE,OAF6B,EAG7BM,QAH6B,EAI7BlD,KAJ6B,EAK7B2E,YAL6B,EAM7B7D,MAN6B,EAOL;AACxB,UAAMc,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCD,MAAA,CAAc,QAAd,CAFqC,CAApB,CAAnB;AAKA,UAAM+D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBjB,MAAAA,MAAM,EAAE,IAAI7E,GAAJ,CAAQ6E,MAAR,EAAgB9E,QAAhB;AAFV,KADF,EAKE6F,IALF;AAQA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE6B,QAAT;AAAmB5B,MAAAA,QAAQ,EAAE,KAA7B;AAAoCC,MAAAA,UAAU,EAAE;AAAhD,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,IAA1B;AAAgCC,QAAAA,UAAU,EAAE;AAA5C,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgC,SAAvBoD,uBAAuB,CAC5B7G,SAD4B,EAE5BwE,OAF4B,EAG5B5C,KAH4B,EAI5B2E,YAJ4B,EAKJ;AACxB,UAAM/C,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CAAC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AAEA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CAAC;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KAAD,CAAX;;AACA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,IAA1B;AAAgCC,QAAAA,UAAU,EAAE;AAA5C,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsC,SAA7B0D,6BAA6B,CAClCnH,SADkC,EAElCwE,OAFkC,EAGlCuC,YAHkC,EAIlCC,aAJkC,EAKlCC,gBALkC,EAMlCV,YANkC,EAOV;AACxB,UAAMqC,iBAAiB,GAAGlM,YAAY,CAAC8C,MAAb,CAAoB,CAC5C9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CAD4C,EAE5CjD,YAAY,CAACiD,EAAb,CAAgB,eAAhB,CAF4C,EAG5CjD,YAAY,CAACiD,EAAb,CAAgB,QAAhB,CAH4C,EAI5CD,SAAA,CAAiB,cAAjB,CAJ4C,CAApB,CAA1B;AAOA,QAAI+D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAa,IAAb,CAAX;AACA;AACE,YAAMuK,YAAY,GAAGD,iBAAiB,CAAClF,MAAlB,CACnB;AACEC,QAAAA,WAAW,EAAE,CADf;AACkB;AAChBqD,QAAAA,aAAa,EAAE/H,kBAAkB,CAAC+H,aAAD,CAFnC;AAGE8B,QAAAA,MAAM,EAAE/B,YAAY,KAAK,IAAjB,GAAwB,CAAxB,GAA4B,CAHtC;AAIEA,QAAAA,YAAY,EAAEtJ,cAAc,CAACsJ,YAAY,IAAI,IAAI1J,SAAJ,CAAc,CAAd,CAAjB;AAJ9B,OADmB,EAOnBoG,IAPmB,CAArB;AASAA,MAAAA,IAAI,GAAGA,IAAI,CAAC5E,KAAL,CAAW,CAAX,EAAcgK,YAAd,CAAP;AACD;AAED,QAAI7F,IAAI,GAAG,CAAC;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KAAD,CAAX;;AACA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEgE,gBAAT;AAA2B/D,QAAAA,QAAQ,EAAE,IAArC;AAA2CC,QAAAA,UAAU,EAAE;AAAvD,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEgE,gBAAT;AAA2B/D,QAAAA,QAAQ,EAAE,KAArC;AAA4CC,QAAAA,UAAU,EAAE;AAAxD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgC,SAAvB8D,uBAAuB,CAC5BvH,SAD4B,EAE5B4E,IAF4B,EAG5ByC,IAH4B,EAI5BC,SAJ4B,EAK5Bf,YAL4B,EAM5B7D,MAN4B,EAOJ;AACxB,UAAMc,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCD,MAAA,CAAc,QAAd,CAFqC,CAApB,CAAnB;AAKA,UAAM+D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBjB,MAAAA,MAAM,EAAE,IAAI7E,GAAJ,CAAQ6E,MAAR,EAAgB9E,QAAhB;AAFV,KADF,EAKE6F,IALF;AAQA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEoE,IAAT;AAAenE,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEqE,SADA;AAERpE,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,KAA9B;AAAqCC,QAAAA,UAAU,EAAE;AAAjD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC8B,SAArBgE,qBAAqB,CAC1BzH,SAD0B,EAE1B4E,IAF0B,EAG1BJ,OAH0B,EAI1B5C,KAJ0B,EAK1B2E,YAL0B,EAM1B7D,MAN0B,EAOF;AACxB,UAAMc,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCD,MAAA,CAAc,QAAd,CAFqC,CAApB,CAAnB;AAKA,UAAM+D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBjB,MAAAA,MAAM,EAAE,IAAI7E,GAAJ,CAAQ6E,MAAR,EAAgB9E,QAAhB;AAFV,KADF,EAKE6F,IALF;AAQA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAErB,KADA;AAERsB,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsC,SAA7BmE,6BAA6B,CAClC5H,SADkC,EAElCwE,OAFkC,EAGlC6C,IAHkC,EAIlCzF,KAJkC,EAKlC2E,YALkC,EAMV;AACxB,UAAM/C,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CAAC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AACA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEoE,IAAT;AAAenE,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,IAA1B;AAAgCC,QAAAA,UAAU,EAAE;AAA5C,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACuC,SAA9BqE,8BAA8B,CACnC9H,SADmC,EAEnCwE,OAFmC,EAGnCI,IAHmC,EAInC0C,SAJmC,EAKnCf,YALmC,EAMX;AACxB,UAAM/C,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CAAC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AACA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,IAA9B;AAAoCC,QAAAA,UAAU,EAAE;AAAhD,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,KAA9B;AAAqCC,QAAAA,UAAU,EAAE;AAAjD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqC,SAA5BuE,4BAA4B,CACjChI,SADiC,EAEjCwE,OAFiC,EAGjCI,IAHiC,EAIjC0C,SAJiC,EAKjCf,YALiC,EAMT;AACxB,UAAM/C,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CAAC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AACA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,IAA9B;AAAoCC,QAAAA,UAAU,EAAE;AAAhD,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,KAA9B;AAAqCC,QAAAA,UAAU,EAAE;AAAjD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACyC,SAAhCyE,gCAAgC,CACrClI,SADqC,EAErCqG,MAFqC,EAGrCzB,IAHqC,EAIrC0B,WAJqC,EAKrC1E,KALqC,EAMrC2E,YANqC,EAOrC7D,MAPqC,EAQrC7B,QARqC,EASb;AACxB,UAAM2C,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCD,MAAA,CAAc,QAAd,CAFqC,EAGrChD,YAAY,CAACiD,EAAb,CAAgB,UAAhB,CAHqC,CAApB,CAAnB;AAMA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;AACmB;AACjBjB,MAAAA,MAAM,EAAE,IAAI7E,GAAJ,CAAQ6E,MAAR,EAAgB9E,QAAhB,EAFV;AAGEiD,MAAAA;AAHF,KADF,EAME4C,IANF;AASA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEoD,MAAT;AAAiBnD,MAAAA,QAAQ,EAAE,KAA3B;AAAkCC,MAAAA,UAAU,EAAE;AAA9C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,EAGT;AAACF,MAAAA,MAAM,EAAEqD,WAAT;AAAsBpD,MAAAA,QAAQ,EAAE,KAAhC;AAAuCC,MAAAA,UAAU,EAAE;AAAnD,KAHS,CAAX;;AAKA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAErB,KADA;AAERsB,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AACD,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACwC,SAA/B2E,+BAA+B,CACpCpI,SADoC,EAEpCwE,OAFoC,EAGpCI,IAHoC,EAIpCE,QAJoC,EAKpClD,KALoC,EAMpC2E,YANoC,EAOpC7D,MAPoC,EAQpC7B,QARoC,EASZ;AACxB,UAAM2C,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCD,MAAA,CAAc,QAAd,CAFqC,EAGrChD,YAAY,CAACiD,EAAb,CAAgB,UAAhB,CAHqC,CAApB,CAAnB;AAMA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;AACmB;AACjBjB,MAAAA,MAAM,EAAE,IAAI7E,GAAJ,CAAQ6E,MAAR,EAAgB9E,QAAhB,EAFV;AAGEiD,MAAAA;AAHF,KADF,EAME4C,IANF;AASA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,EAGT;AAACF,MAAAA,MAAM,EAAE6B,QAAT;AAAmB5B,MAAAA,QAAQ,EAAE,KAA7B;AAAoCC,MAAAA,UAAU,EAAE;AAAhD,KAHS,CAAX;;AAKA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,IAA1B;AAAgCC,QAAAA,UAAU,EAAE;AAA5C,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACuC,SAA9B6E,8BAA8B,CACnCtI,SADmC,EAEnC4E,IAFmC,EAGnCyC,IAHmC,EAInCC,SAJmC,EAKnCf,YALmC,EAMnC7D,MANmC,EAOnC7B,QAPmC,EAQX;AACxB,UAAM2C,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCD,MAAA,CAAc,QAAd,CAFqC,EAGrChD,YAAY,CAACiD,EAAb,CAAgB,UAAhB,CAHqC,CAApB,CAAnB;AAMA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;AACmB;AACjBjB,MAAAA,MAAM,EAAE,IAAI7E,GAAJ,CAAQ6E,MAAR,EAAgB9E,QAAhB,EAFV;AAGEiD,MAAAA;AAHF,KADF,EAME4C,IANF;AASA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEoE,IAAT;AAAenE,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEqE,SADA;AAERpE,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,KAA9B;AAAqCC,QAAAA,UAAU,EAAE;AAAjD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqC,SAA5B+E,4BAA4B,CACjCxI,SADiC,EAEjC4E,IAFiC,EAGjCJ,OAHiC,EAIjC5C,KAJiC,EAKjC2E,YALiC,EAMjC7D,MANiC,EAOjC7B,QAPiC,EAQT;AACxB,UAAM2C,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CACrC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CADqC,EAErCD,MAAA,CAAc,QAAd,CAFqC,EAGrChD,YAAY,CAACiD,EAAb,CAAgB,UAAhB,CAHqC,CAApB,CAAnB;AAMA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;AACmB;AACjBjB,MAAAA,MAAM,EAAE,IAAI7E,GAAJ,CAAQ6E,MAAR,EAAgB9E,QAAhB,EAFV;AAGEiD,MAAAA;AAHF,KADF,EAME4C,IANF;AASA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACpI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B6E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAErB,KADA;AAERsB,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAAC9G,SADP;AAER0G,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACoC,SAA3BkF,2BAA2B,CAChC3I,SADgC,EAEhC0I,aAFgC,EAGR;AACxB,UAAMlF,UAAU,GAAG9G,YAAY,CAAC8C,MAAb,CAAoB,CAAC9C,YAAY,CAACiD,EAAb,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AAEA,UAAM8D,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAakF,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CAAC;AAACC,MAAAA,MAAM,EAAEyF,aAAT;AAAwBxF,MAAAA,QAAQ,EAAE,KAAlC;AAAyCC,MAAAA,UAAU,EAAE;AAArD,KAAD,CAAX;AACA,WAAO,IAAI4F,sBAAJ,CAA2B;AAAC/F,MAAAA,IAAD;AAAOhD,MAAAA,SAAS,EAAEA,SAAlB;AAA6ByD,MAAAA;AAA7B,KAA3B,CAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACwC,eAAzBvB,yBAAyB,CACpC9B,mBADoC,EAEpCJ,SAFoC,EAGpC4E,IAHoC,EAIpChD,KAJoC,EAKpCoH,kBAAkB,GAAY,KALM,EAMhB;AACpB,QAAI,CAACA,kBAAD,IAAuB,CAAC3L,SAAS,CAAC4L,SAAV,CAAoBrH,KAAK,CAAChE,QAAN,EAApB,CAA5B,EAAmE;AACjE,YAAM,IAAIkG,KAAJ,CAAW,sBAAqBlC,KAAK,CAAChD,QAAN,EAAiB,EAAjD,CAAN;AACD;;AACD,WAAO,CACL,MAAMvB,SAAS,CAAC6L,kBAAV,CACJ,CAACtH,KAAK,CAAChE,QAAN,EAAD,EAAmBoC,SAAS,CAACpC,QAAV,EAAnB,EAAyCgH,IAAI,CAAChH,QAAL,EAAzC,CADI,EAEJwC,mBAFI,CADD,EAKL,CALK,CAAP;AAMD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgD,SAAvCgC,uCAAuC,CAC5ChC,mBAD4C,EAE5CJ,SAF4C,EAG5C4E,IAH4C,EAI5CuE,iBAJ4C,EAK5CvH,KAL4C,EAM5C3B,KAN4C,EAOpB;AACxB,UAAMwD,IAAI,GAAG/F,MAAM,CAACY,KAAP,CAAa,CAAb,CAAb;AAEA,QAAI0E,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEhD,KAAT;AAAgBiD,MAAAA,QAAQ,EAAE,IAA1B;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEkG,iBAAT;AAA4BjG,MAAAA,QAAQ,EAAE,KAAtC;AAA6CC,MAAAA,UAAU,EAAE;AAAzD,KAFS,EAGT;AAACF,MAAAA,MAAM,EAAErB,KAAT;AAAgBsB,MAAAA,QAAQ,EAAE,KAA1B;AAAiCC,MAAAA,UAAU,EAAE;AAA7C,KAHS,EAIT;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAJS,EAKT;AAACF,MAAAA,MAAM,EAAE5B,aAAa,CAACrB,SAAvB;AAAkCkD,MAAAA,QAAQ,EAAE,KAA5C;AAAmDC,MAAAA,UAAU,EAAE;AAA/D,KALS,EAMT;AAACF,MAAAA,MAAM,EAAEjD,SAAT;AAAoBkD,MAAAA,QAAQ,EAAE,KAA9B;AAAqCC,MAAAA,UAAU,EAAE;AAAjD,KANS,EAOT;AAACF,MAAAA,MAAM,EAAEG,kBAAT;AAA6BF,MAAAA,QAAQ,EAAE,KAAvC;AAA8CC,MAAAA,UAAU,EAAE;AAA1D,KAPS,CAAX;AAUA,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEI,mBAFqB;AAGhCqD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;;AA7/DgB;;;;"}