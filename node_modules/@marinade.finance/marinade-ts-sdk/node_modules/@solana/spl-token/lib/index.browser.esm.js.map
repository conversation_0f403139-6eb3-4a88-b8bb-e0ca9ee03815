{"version": 3, "file": "index.browser.esm.js", "sources": ["../node_modules/base64-js/index.js", "../node_modules/ieee754/index.js", "../node_modules/buffer/index.js", "../node_modules/rollup-plugin-node-polyfills/polyfills/global.js", "../node_modules/rollup-plugin-node-polyfills/polyfills/inherits.js", "../node_modules/rollup-plugin-node-polyfills/polyfills/util.js", "../node_modules/rollup-plugin-node-polyfills/polyfills/assert.js", "../node_modules/buffer-layout/lib/Layout.js", "../client/layout.js", "../client/util/send-and-confirm-transaction.js", "../client/token.js"], "sourcesContent": ["'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n", "/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nconst base64 = require('base64-js')\nconst ieee754 = require('ieee754')\nconst customInspectSymbol =\n  (typeof Symbol === 'function' && typeof Symbol['for'] === 'function') // eslint-disable-line dot-notation\n    ? Symbol['for']('nodejs.util.inspect.custom') // eslint-disable-line dot-notation\n    : null\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\nconst K_MAX_LENGTH = 0x7fffffff\nexports.kMaxLength = K_MAX_LENGTH\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Print warning and recommend using `buffer` v4.x which has an Object\n *               implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * We report that the browser does not support typed arrays if the are not subclassable\n * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`\n * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support\n * for __proto__ and has a buggy typed array implementation.\n */\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport()\n\nif (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&\n    typeof console.error === 'function') {\n  console.error(\n    'This browser lacks typed array (Uint8Array) support which is required by ' +\n    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'\n  )\n}\n\nfunction typedArraySupport () {\n  // Can typed array instances can be augmented?\n  try {\n    const arr = new Uint8Array(1)\n    const proto = { foo: function () { return 42 } }\n    Object.setPrototypeOf(proto, Uint8Array.prototype)\n    Object.setPrototypeOf(arr, proto)\n    return arr.foo() === 42\n  } catch (e) {\n    return false\n  }\n}\n\nObject.defineProperty(Buffer.prototype, 'parent', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.buffer\n  }\n})\n\nObject.defineProperty(Buffer.prototype, 'offset', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.byteOffset\n  }\n})\n\nfunction createBuffer (length) {\n  if (length > K_MAX_LENGTH) {\n    throw new RangeError('The value \"' + length + '\" is invalid for option \"size\"')\n  }\n  // Return an augmented `Uint8Array` instance\n  const buf = new Uint8Array(length)\n  Object.setPrototypeOf(buf, Buffer.prototype)\n  return buf\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new TypeError(\n        'The \"string\" argument must be of type string. Received type number'\n      )\n    }\n    return allocUnsafe(arg)\n  }\n  return from(arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\nfunction from (value, encodingOrOffset, length) {\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  if (ArrayBuffer.isView(value)) {\n    return fromArrayView(value)\n  }\n\n  if (value == null) {\n    throw new TypeError(\n      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n      'or Array-like Object. Received type ' + (typeof value)\n    )\n  }\n\n  if (isInstance(value, ArrayBuffer) ||\n      (value && isInstance(value.buffer, ArrayBuffer))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof SharedArrayBuffer !== 'undefined' &&\n      (isInstance(value, SharedArrayBuffer) ||\n      (value && isInstance(value.buffer, SharedArrayBuffer)))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'number') {\n    throw new TypeError(\n      'The \"value\" argument must not be of type number. Received type number'\n    )\n  }\n\n  const valueOf = value.valueOf && value.valueOf()\n  if (valueOf != null && valueOf !== value) {\n    return Buffer.from(valueOf, encodingOrOffset, length)\n  }\n\n  const b = fromObject(value)\n  if (b) return b\n\n  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&\n      typeof value[Symbol.toPrimitive] === 'function') {\n    return Buffer.from(value[Symbol.toPrimitive]('string'), encodingOrOffset, length)\n  }\n\n  throw new TypeError(\n    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n    'or Array-like Object. Received type ' + (typeof value)\n  )\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(value, encodingOrOffset, length)\n}\n\n// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:\n// https://github.com/feross/buffer/pull/148\nObject.setPrototypeOf(Buffer.prototype, Uint8Array.prototype)\nObject.setPrototypeOf(Buffer, Uint8Array)\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be of type number')\n  } else if (size < 0) {\n    throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"')\n  }\n}\n\nfunction alloc (size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpreted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(size).fill(fill, encoding)\n      : createBuffer(size).fill(fill)\n  }\n  return createBuffer(size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(size, fill, encoding)\n}\n\nfunction allocUnsafe (size) {\n  assertSize(size)\n  return createBuffer(size < 0 ? 0 : checked(size) | 0)\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(size)\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('Unknown encoding: ' + encoding)\n  }\n\n  const length = byteLength(string, encoding) | 0\n  let buf = createBuffer(length)\n\n  const actual = buf.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual)\n  }\n\n  return buf\n}\n\nfunction fromArrayLike (array) {\n  const length = array.length < 0 ? 0 : checked(array.length) | 0\n  const buf = createBuffer(length)\n  for (let i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255\n  }\n  return buf\n}\n\nfunction fromArrayView (arrayView) {\n  if (isInstance(arrayView, Uint8Array)) {\n    const copy = new Uint8Array(arrayView)\n    return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength)\n  }\n  return fromArrayLike(arrayView)\n}\n\nfunction fromArrayBuffer (array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\"offset\" is outside of buffer bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\"length\" is outside of buffer bounds')\n  }\n\n  let buf\n  if (byteOffset === undefined && length === undefined) {\n    buf = new Uint8Array(array)\n  } else if (length === undefined) {\n    buf = new Uint8Array(array, byteOffset)\n  } else {\n    buf = new Uint8Array(array, byteOffset, length)\n  }\n\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(buf, Buffer.prototype)\n\n  return buf\n}\n\nfunction fromObject (obj) {\n  if (Buffer.isBuffer(obj)) {\n    const len = checked(obj.length) | 0\n    const buf = createBuffer(len)\n\n    if (buf.length === 0) {\n      return buf\n    }\n\n    obj.copy(buf, 0, 0, len)\n    return buf\n  }\n\n  if (obj.length !== undefined) {\n    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {\n      return createBuffer(0)\n    }\n    return fromArrayLike(obj)\n  }\n\n  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n    return fromArrayLike(obj.data)\n  }\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return b != null && b._isBuffer === true &&\n    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false\n}\n\nBuffer.compare = function compare (a, b) {\n  if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength)\n  if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength)\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError(\n      'The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array'\n    )\n  }\n\n  if (a === b) return 0\n\n  let x = a.length\n  let y = b.length\n\n  for (let i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!Array.isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  let i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  const buffer = Buffer.allocUnsafe(length)\n  let pos = 0\n  for (i = 0; i < list.length; ++i) {\n    let buf = list[i]\n    if (isInstance(buf, Uint8Array)) {\n      if (pos + buf.length > buffer.length) {\n        if (!Buffer.isBuffer(buf)) buf = Buffer.from(buf)\n        buf.copy(buffer, pos)\n      } else {\n        Uint8Array.prototype.set.call(\n          buffer,\n          buf,\n          pos\n        )\n      }\n    } else if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    } else {\n      buf.copy(buffer, pos)\n    }\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    throw new TypeError(\n      'The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. ' +\n      'Received type ' + typeof string\n    )\n  }\n\n  const len = string.length\n  const mustMatch = (arguments.length > 2 && arguments[2] === true)\n  if (!mustMatch && len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) {\n          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8\n        }\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  let loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coercion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)\n// to detect a Buffer instance. It's not possible to use `instanceof Buffer`\n// reliably in a browserify context because there could be multiple different\n// copies of the 'buffer' package in use. This method works even for Buffer\n// instances that were created from another copy of the `buffer` package.\n// See: https://github.com/feross/buffer/issues/154\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  const i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  const len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (let i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  const len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (let i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  const len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (let i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  const length = this.length\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.toLocaleString = Buffer.prototype.toString\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  let str = ''\n  const max = exports.INSPECT_MAX_BYTES\n  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()\n  if (this.length > max) str += ' ... '\n  return '<Buffer ' + str + '>'\n}\nif (customInspectSymbol) {\n  Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (isInstance(target, Uint8Array)) {\n    target = Buffer.from(target, target.offset, target.byteLength)\n  }\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError(\n      'The \"target\" argument must be one of type Buffer or Uint8Array. ' +\n      'Received type ' + (typeof target)\n    )\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  let x = thisEnd - thisStart\n  let y = end - start\n  const len = Math.min(x, y)\n\n  const thisCopy = this.slice(thisStart, thisEnd)\n  const targetCopy = target.slice(start, end)\n\n  for (let i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset // Coerce to Number.\n  if (numberIsNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  let indexSize = 1\n  let arrLength = arr.length\n  let valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  let i\n  if (dir) {\n    let foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      let found = true\n      for (let j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  const remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  const strLen = string.length\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  let i\n  for (i = 0; i < length; ++i) {\n    const parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (numberIsNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset >>> 0\n    if (isFinite(length)) {\n      length = length >>> 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  const remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return asciiWrite(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  const res = []\n\n  let i = start\n  while (i < end) {\n    const firstByte = buf[i]\n    let codePoint = null\n    let bytesPerSequence = (firstByte > 0xEF)\n      ? 4\n      : (firstByte > 0xDF)\n          ? 3\n          : (firstByte > 0xBF)\n              ? 2\n              : 1\n\n    if (i + bytesPerSequence <= end) {\n      let secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nconst MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  const len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  let res = ''\n  let i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  const len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  let out = ''\n  for (let i = start; i < end; ++i) {\n    out += hexSliceLookupTable[buf[i]]\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  const bytes = buf.slice(start, end)\n  let res = ''\n  // If bytes.length is odd, the last 8 bits must be ignored (same as node.js)\n  for (let i = 0; i < bytes.length - 1; i += 2) {\n    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  const len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  const newBuf = this.subarray(start, end)\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(newBuf, Buffer.prototype)\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUintLE =\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUintBE =\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  let val = this[offset + --byteLength]\n  let mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUint8 =\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUint16LE =\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUint16BE =\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUint32LE =\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUint32BE =\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readBigUInt64LE = defineBigIntMethod(function readBigUInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const lo = first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24\n\n  const hi = this[++offset] +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    last * 2 ** 24\n\n  return BigInt(lo) + (BigInt(hi) << BigInt(32))\n})\n\nBuffer.prototype.readBigUInt64BE = defineBigIntMethod(function readBigUInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const hi = first * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  const lo = this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last\n\n  return (BigInt(hi) << BigInt(32)) + BigInt(lo)\n})\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let i = byteLength\n  let mul = 1\n  let val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readBigInt64LE = defineBigIntMethod(function readBigInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = this[offset + 4] +\n    this[offset + 5] * 2 ** 8 +\n    this[offset + 6] * 2 ** 16 +\n    (last << 24) // Overflow\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24)\n})\n\nBuffer.prototype.readBigInt64BE = defineBigIntMethod(function readBigInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = (first << 24) + // Overflow\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last)\n})\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUintLE =\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let mul = 1\n  let i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUintBE =\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUint8 =\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeUint16LE =\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint16BE =\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint32LE =\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset + 3] = (value >>> 24)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 1] = (value >>> 8)\n  this[offset] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeUint32BE =\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nfunction wrtBigUInt64LE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  return offset\n}\n\nfunction wrtBigUInt64BE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset + 7] = lo\n  lo = lo >> 8\n  buf[offset + 6] = lo\n  lo = lo >> 8\n  buf[offset + 5] = lo\n  lo = lo >> 8\n  buf[offset + 4] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset + 3] = hi\n  hi = hi >> 8\n  buf[offset + 2] = hi\n  hi = hi >> 8\n  buf[offset + 1] = hi\n  hi = hi >> 8\n  buf[offset] = hi\n  return offset + 8\n}\n\nBuffer.prototype.writeBigUInt64LE = defineBigIntMethod(function writeBigUInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeBigUInt64BE = defineBigIntMethod(function writeBigUInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = 0\n  let mul = 1\n  let sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  let sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 3] = (value >>> 24)\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeBigInt64LE = defineBigIntMethod(function writeBigInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nBuffer.prototype.writeBigInt64BE = defineBigIntMethod(function writeBigInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  const len = end - start\n\n  if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {\n    // Use built-in when available, missing from IE11\n    this.copyWithin(targetStart, start, end)\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, end),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n    if (val.length === 1) {\n      const code = val.charCodeAt(0)\n      if ((encoding === 'utf8' && code < 128) ||\n          encoding === 'latin1') {\n        // Fast path: If `val` fits into a single byte, use that numeric value.\n        val = code\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  } else if (typeof val === 'boolean') {\n    val = Number(val)\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  let i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    const bytes = Buffer.isBuffer(val)\n      ? val\n      : Buffer.from(val, encoding)\n    const len = bytes.length\n    if (len === 0) {\n      throw new TypeError('The value \"' + val +\n        '\" is invalid for argument \"value\"')\n    }\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// CUSTOM ERRORS\n// =============\n\n// Simplified versions from Node, changed for Buffer-only usage\nconst errors = {}\nfunction E (sym, getMessage, Base) {\n  errors[sym] = class NodeError extends Base {\n    constructor () {\n      super()\n\n      Object.defineProperty(this, 'message', {\n        value: getMessage.apply(this, arguments),\n        writable: true,\n        configurable: true\n      })\n\n      // Add the error code to the name to include it in the stack trace.\n      this.name = `${this.name} [${sym}]`\n      // Access the stack to generate the error message including the error code\n      // from the name.\n      this.stack // eslint-disable-line no-unused-expressions\n      // Reset the name to the actual name.\n      delete this.name\n    }\n\n    get code () {\n      return sym\n    }\n\n    set code (value) {\n      Object.defineProperty(this, 'code', {\n        configurable: true,\n        enumerable: true,\n        value,\n        writable: true\n      })\n    }\n\n    toString () {\n      return `${this.name} [${sym}]: ${this.message}`\n    }\n  }\n}\n\nE('ERR_BUFFER_OUT_OF_BOUNDS',\n  function (name) {\n    if (name) {\n      return `${name} is outside of buffer bounds`\n    }\n\n    return 'Attempt to access memory outside buffer bounds'\n  }, RangeError)\nE('ERR_INVALID_ARG_TYPE',\n  function (name, actual) {\n    return `The \"${name}\" argument must be of type number. Received type ${typeof actual}`\n  }, TypeError)\nE('ERR_OUT_OF_RANGE',\n  function (str, range, input) {\n    let msg = `The value of \"${str}\" is out of range.`\n    let received = input\n    if (Number.isInteger(input) && Math.abs(input) > 2 ** 32) {\n      received = addNumericalSeparator(String(input))\n    } else if (typeof input === 'bigint') {\n      received = String(input)\n      if (input > BigInt(2) ** BigInt(32) || input < -(BigInt(2) ** BigInt(32))) {\n        received = addNumericalSeparator(received)\n      }\n      received += 'n'\n    }\n    msg += ` It must be ${range}. Received ${received}`\n    return msg\n  }, RangeError)\n\nfunction addNumericalSeparator (val) {\n  let res = ''\n  let i = val.length\n  const start = val[0] === '-' ? 1 : 0\n  for (; i >= start + 4; i -= 3) {\n    res = `_${val.slice(i - 3, i)}${res}`\n  }\n  return `${val.slice(0, i)}${res}`\n}\n\n// CHECK FUNCTIONS\n// ===============\n\nfunction checkBounds (buf, offset, byteLength) {\n  validateNumber(offset, 'offset')\n  if (buf[offset] === undefined || buf[offset + byteLength] === undefined) {\n    boundsError(offset, buf.length - (byteLength + 1))\n  }\n}\n\nfunction checkIntBI (value, min, max, buf, offset, byteLength) {\n  if (value > max || value < min) {\n    const n = typeof min === 'bigint' ? 'n' : ''\n    let range\n    if (byteLength > 3) {\n      if (min === 0 || min === BigInt(0)) {\n        range = `>= 0${n} and < 2${n} ** ${(byteLength + 1) * 8}${n}`\n      } else {\n        range = `>= -(2${n} ** ${(byteLength + 1) * 8 - 1}${n}) and < 2 ** ` +\n                `${(byteLength + 1) * 8 - 1}${n}`\n      }\n    } else {\n      range = `>= ${min}${n} and <= ${max}${n}`\n    }\n    throw new errors.ERR_OUT_OF_RANGE('value', range, value)\n  }\n  checkBounds(buf, offset, byteLength)\n}\n\nfunction validateNumber (value, name) {\n  if (typeof value !== 'number') {\n    throw new errors.ERR_INVALID_ARG_TYPE(name, 'number', value)\n  }\n}\n\nfunction boundsError (value, length, type) {\n  if (Math.floor(value) !== value) {\n    validateNumber(value, type)\n    throw new errors.ERR_OUT_OF_RANGE(type || 'offset', 'an integer', value)\n  }\n\n  if (length < 0) {\n    throw new errors.ERR_BUFFER_OUT_OF_BOUNDS()\n  }\n\n  throw new errors.ERR_OUT_OF_RANGE(type || 'offset',\n                                    `>= ${type ? 1 : 0} and <= ${length}`,\n                                    value)\n}\n\n// HELPER FUNCTIONS\n// ================\n\nconst INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node takes equal signs as end of the Base64 encoding\n  str = str.split('=')[0]\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = str.trim().replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  let codePoint\n  const length = string.length\n  let leadSurrogate = null\n  const bytes = []\n\n  for (let i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  let c, hi, lo\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  let i\n  for (i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\n// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass\n// the `instanceof` check but they should be treated as of that type.\n// See: https://github.com/feross/buffer/issues/166\nfunction isInstance (obj, type) {\n  return obj instanceof type ||\n    (obj != null && obj.constructor != null && obj.constructor.name != null &&\n      obj.constructor.name === type.name)\n}\nfunction numberIsNaN (obj) {\n  // For IE11 support\n  return obj !== obj // eslint-disable-line no-self-compare\n}\n\n// Create lookup table for `toString('hex')`\n// See: https://github.com/feross/buffer/issues/219\nconst hexSliceLookupTable = (function () {\n  const alphabet = '0123456789abcdef'\n  const table = new Array(256)\n  for (let i = 0; i < 16; ++i) {\n    const i16 = i * 16\n    for (let j = 0; j < 16; ++j) {\n      table[i16 + j] = alphabet[i] + alphabet[j]\n    }\n  }\n  return table\n})()\n\n// Return not function with Error if BigInt not supported\nfunction defineBigIntMethod (fn) {\n  return typeof BigInt === 'undefined' ? BufferBigIntNotDefined : fn\n}\n\nfunction BufferBigIntNotDefined () {\n  throw new Error('BigInt not supported')\n}\n", "export default (typeof global !== \"undefined\" ? global :\n  typeof self !== \"undefined\" ? self :\n  typeof window !== \"undefined\" ? window : {});", "\nvar inherits;\nif (typeof Object.create === 'function'){\n  inherits = function inherits(ctor, superCtor) {\n    // implementation from standard node.js 'util' module\n    ctor.super_ = superCtor\n    ctor.prototype = Object.create(superCtor.prototype, {\n      constructor: {\n        value: ctor,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n  };\n} else {\n  inherits = function inherits(ctor, superCtor) {\n    ctor.super_ = superCtor\n    var TempCtor = function () {}\n    TempCtor.prototype = superCtor.prototype\n    ctor.prototype = new TempCtor()\n    ctor.prototype.constructor = ctor\n  }\n}\nexport default inherits;\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\nimport process from 'process';\nvar formatRegExp = /%[sdj%]/g;\nexport function format(f) {\n  if (!isString(f)) {\n    var objects = [];\n    for (var i = 0; i < arguments.length; i++) {\n      objects.push(inspect(arguments[i]));\n    }\n    return objects.join(' ');\n  }\n\n  var i = 1;\n  var args = arguments;\n  var len = args.length;\n  var str = String(f).replace(formatRegExp, function(x) {\n    if (x === '%%') return '%';\n    if (i >= len) return x;\n    switch (x) {\n      case '%s': return String(args[i++]);\n      case '%d': return Number(args[i++]);\n      case '%j':\n        try {\n          return JSON.stringify(args[i++]);\n        } catch (_) {\n          return '[Circular]';\n        }\n      default:\n        return x;\n    }\n  });\n  for (var x = args[i]; i < len; x = args[++i]) {\n    if (isNull(x) || !isObject(x)) {\n      str += ' ' + x;\n    } else {\n      str += ' ' + inspect(x);\n    }\n  }\n  return str;\n};\n\n\n// Mark that a method should not be used.\n// Returns a modified function which warns once by default.\n// If --no-deprecation is set, then it is a no-op.\nexport function deprecate(fn, msg) {\n  // Allow for deprecating things in the process of starting up.\n  if (isUndefined(global.process)) {\n    return function() {\n      return deprecate(fn, msg).apply(this, arguments);\n    };\n  }\n\n  if (process.noDeprecation === true) {\n    return fn;\n  }\n\n  var warned = false;\n  function deprecated() {\n    if (!warned) {\n      if (process.throwDeprecation) {\n        throw new Error(msg);\n      } else if (process.traceDeprecation) {\n        console.trace(msg);\n      } else {\n        console.error(msg);\n      }\n      warned = true;\n    }\n    return fn.apply(this, arguments);\n  }\n\n  return deprecated;\n};\n\n\nvar debugs = {};\nvar debugEnviron;\nexport function debuglog(set) {\n  if (isUndefined(debugEnviron))\n    debugEnviron = process.env.NODE_DEBUG || '';\n  set = set.toUpperCase();\n  if (!debugs[set]) {\n    if (new RegExp('\\\\b' + set + '\\\\b', 'i').test(debugEnviron)) {\n      var pid = 0;\n      debugs[set] = function() {\n        var msg = format.apply(null, arguments);\n        console.error('%s %d: %s', set, pid, msg);\n      };\n    } else {\n      debugs[set] = function() {};\n    }\n  }\n  return debugs[set];\n};\n\n\n/**\n * Echos the value of a value. Trys to print the value out\n * in the best way possible given the different types.\n *\n * @param {Object} obj The object to print out.\n * @param {Object} opts Optional options object that alters the output.\n */\n/* legacy: obj, showHidden, depth, colors*/\nexport function inspect(obj, opts) {\n  // default options\n  var ctx = {\n    seen: [],\n    stylize: stylizeNoColor\n  };\n  // legacy...\n  if (arguments.length >= 3) ctx.depth = arguments[2];\n  if (arguments.length >= 4) ctx.colors = arguments[3];\n  if (isBoolean(opts)) {\n    // legacy...\n    ctx.showHidden = opts;\n  } else if (opts) {\n    // got an \"options\" object\n    _extend(ctx, opts);\n  }\n  // set default options\n  if (isUndefined(ctx.showHidden)) ctx.showHidden = false;\n  if (isUndefined(ctx.depth)) ctx.depth = 2;\n  if (isUndefined(ctx.colors)) ctx.colors = false;\n  if (isUndefined(ctx.customInspect)) ctx.customInspect = true;\n  if (ctx.colors) ctx.stylize = stylizeWithColor;\n  return formatValue(ctx, obj, ctx.depth);\n}\n\n// http://en.wikipedia.org/wiki/ANSI_escape_code#graphics\ninspect.colors = {\n  'bold' : [1, 22],\n  'italic' : [3, 23],\n  'underline' : [4, 24],\n  'inverse' : [7, 27],\n  'white' : [37, 39],\n  'grey' : [90, 39],\n  'black' : [30, 39],\n  'blue' : [34, 39],\n  'cyan' : [36, 39],\n  'green' : [32, 39],\n  'magenta' : [35, 39],\n  'red' : [31, 39],\n  'yellow' : [33, 39]\n};\n\n// Don't use 'blue' not visible on cmd.exe\ninspect.styles = {\n  'special': 'cyan',\n  'number': 'yellow',\n  'boolean': 'yellow',\n  'undefined': 'grey',\n  'null': 'bold',\n  'string': 'green',\n  'date': 'magenta',\n  // \"name\": intentionally not styling\n  'regexp': 'red'\n};\n\n\nfunction stylizeWithColor(str, styleType) {\n  var style = inspect.styles[styleType];\n\n  if (style) {\n    return '\\u001b[' + inspect.colors[style][0] + 'm' + str +\n           '\\u001b[' + inspect.colors[style][1] + 'm';\n  } else {\n    return str;\n  }\n}\n\n\nfunction stylizeNoColor(str, styleType) {\n  return str;\n}\n\n\nfunction arrayToHash(array) {\n  var hash = {};\n\n  array.forEach(function(val, idx) {\n    hash[val] = true;\n  });\n\n  return hash;\n}\n\n\nfunction formatValue(ctx, value, recurseTimes) {\n  // Provide a hook for user-specified inspect functions.\n  // Check that value is an object with an inspect function on it\n  if (ctx.customInspect &&\n      value &&\n      isFunction(value.inspect) &&\n      // Filter out the util module, it's inspect function is special\n      value.inspect !== inspect &&\n      // Also filter out any prototype objects using the circular check.\n      !(value.constructor && value.constructor.prototype === value)) {\n    var ret = value.inspect(recurseTimes, ctx);\n    if (!isString(ret)) {\n      ret = formatValue(ctx, ret, recurseTimes);\n    }\n    return ret;\n  }\n\n  // Primitive types cannot have properties\n  var primitive = formatPrimitive(ctx, value);\n  if (primitive) {\n    return primitive;\n  }\n\n  // Look up the keys of the object.\n  var keys = Object.keys(value);\n  var visibleKeys = arrayToHash(keys);\n\n  if (ctx.showHidden) {\n    keys = Object.getOwnPropertyNames(value);\n  }\n\n  // IE doesn't make error fields non-enumerable\n  // http://msdn.microsoft.com/en-us/library/ie/dww52sbt(v=vs.94).aspx\n  if (isError(value)\n      && (keys.indexOf('message') >= 0 || keys.indexOf('description') >= 0)) {\n    return formatError(value);\n  }\n\n  // Some type of object without properties can be shortcutted.\n  if (keys.length === 0) {\n    if (isFunction(value)) {\n      var name = value.name ? ': ' + value.name : '';\n      return ctx.stylize('[Function' + name + ']', 'special');\n    }\n    if (isRegExp(value)) {\n      return ctx.stylize(RegExp.prototype.toString.call(value), 'regexp');\n    }\n    if (isDate(value)) {\n      return ctx.stylize(Date.prototype.toString.call(value), 'date');\n    }\n    if (isError(value)) {\n      return formatError(value);\n    }\n  }\n\n  var base = '', array = false, braces = ['{', '}'];\n\n  // Make Array say that they are Array\n  if (isArray(value)) {\n    array = true;\n    braces = ['[', ']'];\n  }\n\n  // Make functions say that they are functions\n  if (isFunction(value)) {\n    var n = value.name ? ': ' + value.name : '';\n    base = ' [Function' + n + ']';\n  }\n\n  // Make RegExps say that they are RegExps\n  if (isRegExp(value)) {\n    base = ' ' + RegExp.prototype.toString.call(value);\n  }\n\n  // Make dates with properties first say the date\n  if (isDate(value)) {\n    base = ' ' + Date.prototype.toUTCString.call(value);\n  }\n\n  // Make error with message first say the error\n  if (isError(value)) {\n    base = ' ' + formatError(value);\n  }\n\n  if (keys.length === 0 && (!array || value.length == 0)) {\n    return braces[0] + base + braces[1];\n  }\n\n  if (recurseTimes < 0) {\n    if (isRegExp(value)) {\n      return ctx.stylize(RegExp.prototype.toString.call(value), 'regexp');\n    } else {\n      return ctx.stylize('[Object]', 'special');\n    }\n  }\n\n  ctx.seen.push(value);\n\n  var output;\n  if (array) {\n    output = formatArray(ctx, value, recurseTimes, visibleKeys, keys);\n  } else {\n    output = keys.map(function(key) {\n      return formatProperty(ctx, value, recurseTimes, visibleKeys, key, array);\n    });\n  }\n\n  ctx.seen.pop();\n\n  return reduceToSingleString(output, base, braces);\n}\n\n\nfunction formatPrimitive(ctx, value) {\n  if (isUndefined(value))\n    return ctx.stylize('undefined', 'undefined');\n  if (isString(value)) {\n    var simple = '\\'' + JSON.stringify(value).replace(/^\"|\"$/g, '')\n                                             .replace(/'/g, \"\\\\'\")\n                                             .replace(/\\\\\"/g, '\"') + '\\'';\n    return ctx.stylize(simple, 'string');\n  }\n  if (isNumber(value))\n    return ctx.stylize('' + value, 'number');\n  if (isBoolean(value))\n    return ctx.stylize('' + value, 'boolean');\n  // For some reason typeof null is \"object\", so special case here.\n  if (isNull(value))\n    return ctx.stylize('null', 'null');\n}\n\n\nfunction formatError(value) {\n  return '[' + Error.prototype.toString.call(value) + ']';\n}\n\n\nfunction formatArray(ctx, value, recurseTimes, visibleKeys, keys) {\n  var output = [];\n  for (var i = 0, l = value.length; i < l; ++i) {\n    if (hasOwnProperty(value, String(i))) {\n      output.push(formatProperty(ctx, value, recurseTimes, visibleKeys,\n          String(i), true));\n    } else {\n      output.push('');\n    }\n  }\n  keys.forEach(function(key) {\n    if (!key.match(/^\\d+$/)) {\n      output.push(formatProperty(ctx, value, recurseTimes, visibleKeys,\n          key, true));\n    }\n  });\n  return output;\n}\n\n\nfunction formatProperty(ctx, value, recurseTimes, visibleKeys, key, array) {\n  var name, str, desc;\n  desc = Object.getOwnPropertyDescriptor(value, key) || { value: value[key] };\n  if (desc.get) {\n    if (desc.set) {\n      str = ctx.stylize('[Getter/Setter]', 'special');\n    } else {\n      str = ctx.stylize('[Getter]', 'special');\n    }\n  } else {\n    if (desc.set) {\n      str = ctx.stylize('[Setter]', 'special');\n    }\n  }\n  if (!hasOwnProperty(visibleKeys, key)) {\n    name = '[' + key + ']';\n  }\n  if (!str) {\n    if (ctx.seen.indexOf(desc.value) < 0) {\n      if (isNull(recurseTimes)) {\n        str = formatValue(ctx, desc.value, null);\n      } else {\n        str = formatValue(ctx, desc.value, recurseTimes - 1);\n      }\n      if (str.indexOf('\\n') > -1) {\n        if (array) {\n          str = str.split('\\n').map(function(line) {\n            return '  ' + line;\n          }).join('\\n').substr(2);\n        } else {\n          str = '\\n' + str.split('\\n').map(function(line) {\n            return '   ' + line;\n          }).join('\\n');\n        }\n      }\n    } else {\n      str = ctx.stylize('[Circular]', 'special');\n    }\n  }\n  if (isUndefined(name)) {\n    if (array && key.match(/^\\d+$/)) {\n      return str;\n    }\n    name = JSON.stringify('' + key);\n    if (name.match(/^\"([a-zA-Z_][a-zA-Z_0-9]*)\"$/)) {\n      name = name.substr(1, name.length - 2);\n      name = ctx.stylize(name, 'name');\n    } else {\n      name = name.replace(/'/g, \"\\\\'\")\n                 .replace(/\\\\\"/g, '\"')\n                 .replace(/(^\"|\"$)/g, \"'\");\n      name = ctx.stylize(name, 'string');\n    }\n  }\n\n  return name + ': ' + str;\n}\n\n\nfunction reduceToSingleString(output, base, braces) {\n  var numLinesEst = 0;\n  var length = output.reduce(function(prev, cur) {\n    numLinesEst++;\n    if (cur.indexOf('\\n') >= 0) numLinesEst++;\n    return prev + cur.replace(/\\u001b\\[\\d\\d?m/g, '').length + 1;\n  }, 0);\n\n  if (length > 60) {\n    return braces[0] +\n           (base === '' ? '' : base + '\\n ') +\n           ' ' +\n           output.join(',\\n  ') +\n           ' ' +\n           braces[1];\n  }\n\n  return braces[0] + base + ' ' + output.join(', ') + ' ' + braces[1];\n}\n\n\n// NOTE: These type checking functions intentionally don't use `instanceof`\n// because it is fragile and can be easily faked with `Object.create()`.\nexport function isArray(ar) {\n  return Array.isArray(ar);\n}\n\nexport function isBoolean(arg) {\n  return typeof arg === 'boolean';\n}\n\nexport function isNull(arg) {\n  return arg === null;\n}\n\nexport function isNullOrUndefined(arg) {\n  return arg == null;\n}\n\nexport function isNumber(arg) {\n  return typeof arg === 'number';\n}\n\nexport function isString(arg) {\n  return typeof arg === 'string';\n}\n\nexport function isSymbol(arg) {\n  return typeof arg === 'symbol';\n}\n\nexport function isUndefined(arg) {\n  return arg === void 0;\n}\n\nexport function isRegExp(re) {\n  return isObject(re) && objectToString(re) === '[object RegExp]';\n}\n\nexport function isObject(arg) {\n  return typeof arg === 'object' && arg !== null;\n}\n\nexport function isDate(d) {\n  return isObject(d) && objectToString(d) === '[object Date]';\n}\n\nexport function isError(e) {\n  return isObject(e) &&\n      (objectToString(e) === '[object Error]' || e instanceof Error);\n}\n\nexport function isFunction(arg) {\n  return typeof arg === 'function';\n}\n\nexport function isPrimitive(arg) {\n  return arg === null ||\n         typeof arg === 'boolean' ||\n         typeof arg === 'number' ||\n         typeof arg === 'string' ||\n         typeof arg === 'symbol' ||  // ES6 symbol\n         typeof arg === 'undefined';\n}\n\nexport function isBuffer(maybeBuf) {\n  return Buffer.isBuffer(maybeBuf);\n}\n\nfunction objectToString(o) {\n  return Object.prototype.toString.call(o);\n}\n\n\nfunction pad(n) {\n  return n < 10 ? '0' + n.toString(10) : n.toString(10);\n}\n\n\nvar months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep',\n              'Oct', 'Nov', 'Dec'];\n\n// 26 Feb 16:19:34\nfunction timestamp() {\n  var d = new Date();\n  var time = [pad(d.getHours()),\n              pad(d.getMinutes()),\n              pad(d.getSeconds())].join(':');\n  return [d.getDate(), months[d.getMonth()], time].join(' ');\n}\n\n\n// log is just a thin wrapper to console.log that prepends a timestamp\nexport function log() {\n  console.log('%s - %s', timestamp(), format.apply(null, arguments));\n}\n\n\n/**\n * Inherit the prototype methods from one constructor into another.\n *\n * The Function.prototype.inherits from lang.js rewritten as a standalone\n * function (not on Function.prototype). NOTE: If this file is to be loaded\n * during bootstrapping this function needs to be rewritten using some native\n * functions as prototype setup using normal JavaScript does not work as\n * expected during bootstrapping (see mirror.js in r114903).\n *\n * @param {function} ctor Constructor function which needs to inherit the\n *     prototype.\n * @param {function} superCtor Constructor function to inherit prototype from.\n */\nimport inherits from './inherits';\nexport {inherits}\n\nexport function _extend(origin, add) {\n  // Don't do anything if add isn't an object\n  if (!add || !isObject(add)) return origin;\n\n  var keys = Object.keys(add);\n  var i = keys.length;\n  while (i--) {\n    origin[keys[i]] = add[keys[i]];\n  }\n  return origin;\n};\n\nfunction hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nexport default {\n  inherits: inherits,\n  _extend: _extend,\n  log: log,\n  isBuffer: isBuffer,\n  isPrimitive: isPrimitive,\n  isFunction: isFunction,\n  isError: isError,\n  isDate: isDate,\n  isObject: isObject,\n  isRegExp: isRegExp,\n  isUndefined: isUndefined,\n  isSymbol: isSymbol,\n  isString: isString,\n  isNumber: isNumber,\n  isNullOrUndefined: isNullOrUndefined,\n  isNull: isNull,\n  isBoolean: isBoolean,\n  isArray: isArray,\n  inspect: inspect,\n  deprecate: deprecate,\n  format: format,\n  debuglog: debuglog\n}\n", "\nfunction compare(a, b) {\n  if (a === b) {\n    return 0;\n  }\n\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n\n  if (x < y) {\n    return -1;\n  }\n  if (y < x) {\n    return 1;\n  }\n  return 0;\n}\nvar hasOwn = Object.prototype.hasOwnProperty;\n\nvar objectKeys = Object.keys || function (obj) {\n  var keys = [];\n  for (var key in obj) {\n    if (hasOwn.call(obj, key)) keys.push(key);\n  }\n  return keys;\n};\n// based on node assert, original notice:\n\n// http://wiki.commonjs.org/wiki/Unit_Testing/1.0\n//\n// THIS IS NOT TESTED NOR LIKELY TO WORK OUTSIDE V8!\n//\n// Originally from narwhal.js (http://narwhaljs.org)\n// Copyright (c) 2009 <PERSON> <280north.com>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the 'Software'), to\n// deal in the Software without restriction, including without limitation the\n// rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n// sell copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n// ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n// WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nimport {isBuffer} from 'buffer';\nimport {isPrimitive, inherits, isError, isFunction, isRegExp, isDate, inspect as utilInspect} from 'util';\nvar pSlice = Array.prototype.slice;\nvar _functionsHaveNames;\nfunction functionsHaveNames() {\n  if (typeof _functionsHaveNames !== 'undefined') {\n    return _functionsHaveNames;\n  }\n  return _functionsHaveNames = (function () {\n    return function foo() {}.name === 'foo';\n  }());\n}\nfunction pToString (obj) {\n  return Object.prototype.toString.call(obj);\n}\nfunction isView(arrbuf) {\n  if (isBuffer(arrbuf)) {\n    return false;\n  }\n  if (typeof global.ArrayBuffer !== 'function') {\n    return false;\n  }\n  if (typeof ArrayBuffer.isView === 'function') {\n    return ArrayBuffer.isView(arrbuf);\n  }\n  if (!arrbuf) {\n    return false;\n  }\n  if (arrbuf instanceof DataView) {\n    return true;\n  }\n  if (arrbuf.buffer && arrbuf.buffer instanceof ArrayBuffer) {\n    return true;\n  }\n  return false;\n}\n// 1. The assert module provides functions that throw\n// AssertionError's when particular conditions are not met. The\n// assert module must conform to the following interface.\n\nfunction assert(value, message) {\n  if (!value) fail(value, true, message, '==', ok);\n}\nexport default assert;\n\n// 2. The AssertionError is defined in assert.\n// new assert.AssertionError({ message: message,\n//                             actual: actual,\n//                             expected: expected })\n\nvar regex = /\\s*function\\s+([^\\(\\s]*)\\s*/;\n// based on https://github.com/ljharb/function.prototype.name/blob/adeeeec8bfcc6068b187d7d9fb3d5bb1d3a30899/implementation.js\nfunction getName(func) {\n  if (!isFunction(func)) {\n    return;\n  }\n  if (functionsHaveNames()) {\n    return func.name;\n  }\n  var str = func.toString();\n  var match = str.match(regex);\n  return match && match[1];\n}\nassert.AssertionError = AssertionError;\nexport function AssertionError(options) {\n  this.name = 'AssertionError';\n  this.actual = options.actual;\n  this.expected = options.expected;\n  this.operator = options.operator;\n  if (options.message) {\n    this.message = options.message;\n    this.generatedMessage = false;\n  } else {\n    this.message = getMessage(this);\n    this.generatedMessage = true;\n  }\n  var stackStartFunction = options.stackStartFunction || fail;\n  if (Error.captureStackTrace) {\n    Error.captureStackTrace(this, stackStartFunction);\n  } else {\n    // non v8 browsers so we can have a stacktrace\n    var err = new Error();\n    if (err.stack) {\n      var out = err.stack;\n\n      // try to strip useless frames\n      var fn_name = getName(stackStartFunction);\n      var idx = out.indexOf('\\n' + fn_name);\n      if (idx >= 0) {\n        // once we have located the function frame\n        // we need to strip out everything before it (and its line)\n        var next_line = out.indexOf('\\n', idx + 1);\n        out = out.substring(next_line + 1);\n      }\n\n      this.stack = out;\n    }\n  }\n}\n\n// assert.AssertionError instanceof Error\ninherits(AssertionError, Error);\n\nfunction truncate(s, n) {\n  if (typeof s === 'string') {\n    return s.length < n ? s : s.slice(0, n);\n  } else {\n    return s;\n  }\n}\nfunction inspect(something) {\n  if (functionsHaveNames() || !isFunction(something)) {\n    return utilInspect(something);\n  }\n  var rawname = getName(something);\n  var name = rawname ? ': ' + rawname : '';\n  return '[Function' +  name + ']';\n}\nfunction getMessage(self) {\n  return truncate(inspect(self.actual), 128) + ' ' +\n         self.operator + ' ' +\n         truncate(inspect(self.expected), 128);\n}\n\n// At present only the three keys mentioned above are used and\n// understood by the spec. Implementations or sub modules can pass\n// other keys to the AssertionError's constructor - they will be\n// ignored.\n\n// 3. All of the following functions must throw an AssertionError\n// when a corresponding condition is not met, with a message that\n// may be undefined if not provided.  All assertion methods provide\n// both the actual and expected values to the assertion error for\n// display purposes.\n\nexport function fail(actual, expected, message, operator, stackStartFunction) {\n  throw new AssertionError({\n    message: message,\n    actual: actual,\n    expected: expected,\n    operator: operator,\n    stackStartFunction: stackStartFunction\n  });\n}\n\n// EXTENSION! allows for well behaved errors defined elsewhere.\nassert.fail = fail;\n\n// 4. Pure assertion tests whether a value is truthy, as determined\n// by !!guard.\n// assert.ok(guard, message_opt);\n// This statement is equivalent to assert.equal(true, !!guard,\n// message_opt);. To test strictly for the value true, use\n// assert.strictEqual(true, guard, message_opt);.\n\nexport function ok(value, message) {\n  if (!value) fail(value, true, message, '==', ok);\n}\nassert.ok = ok;\nexport {ok as assert};\n\n// 5. The equality assertion tests shallow, coercive equality with\n// ==.\n// assert.equal(actual, expected, message_opt);\nassert.equal = equal;\nexport function equal(actual, expected, message) {\n  if (actual != expected) fail(actual, expected, message, '==', equal);\n}\n\n// 6. The non-equality assertion tests for whether two objects are not equal\n// with != assert.notEqual(actual, expected, message_opt);\nassert.notEqual = notEqual;\nexport function notEqual(actual, expected, message) {\n  if (actual == expected) {\n    fail(actual, expected, message, '!=', notEqual);\n  }\n}\n\n// 7. The equivalence assertion tests a deep equality relation.\n// assert.deepEqual(actual, expected, message_opt);\nassert.deepEqual = deepEqual;\nexport function deepEqual(actual, expected, message) {\n  if (!_deepEqual(actual, expected, false)) {\n    fail(actual, expected, message, 'deepEqual', deepEqual);\n  }\n}\nassert.deepStrictEqual = deepStrictEqual;\nexport function deepStrictEqual(actual, expected, message) {\n  if (!_deepEqual(actual, expected, true)) {\n    fail(actual, expected, message, 'deepStrictEqual', deepStrictEqual);\n  }\n}\n\nfunction _deepEqual(actual, expected, strict, memos) {\n  // 7.1. All identical values are equivalent, as determined by ===.\n  if (actual === expected) {\n    return true;\n  } else if (isBuffer(actual) && isBuffer(expected)) {\n    return compare(actual, expected) === 0;\n\n  // 7.2. If the expected value is a Date object, the actual value is\n  // equivalent if it is also a Date object that refers to the same time.\n  } else if (isDate(actual) && isDate(expected)) {\n    return actual.getTime() === expected.getTime();\n\n  // 7.3 If the expected value is a RegExp object, the actual value is\n  // equivalent if it is also a RegExp object with the same source and\n  // properties (`global`, `multiline`, `lastIndex`, `ignoreCase`).\n  } else if (isRegExp(actual) && isRegExp(expected)) {\n    return actual.source === expected.source &&\n           actual.global === expected.global &&\n           actual.multiline === expected.multiline &&\n           actual.lastIndex === expected.lastIndex &&\n           actual.ignoreCase === expected.ignoreCase;\n\n  // 7.4. Other pairs that do not both pass typeof value == 'object',\n  // equivalence is determined by ==.\n  } else if ((actual === null || typeof actual !== 'object') &&\n             (expected === null || typeof expected !== 'object')) {\n    return strict ? actual === expected : actual == expected;\n\n  // If both values are instances of typed arrays, wrap their underlying\n  // ArrayBuffers in a Buffer each to increase performance\n  // This optimization requires the arrays to have the same type as checked by\n  // Object.prototype.toString (aka pToString). Never perform binary\n  // comparisons for Float*Arrays, though, since e.g. +0 === -0 but their\n  // bit patterns are not identical.\n  } else if (isView(actual) && isView(expected) &&\n             pToString(actual) === pToString(expected) &&\n             !(actual instanceof Float32Array ||\n               actual instanceof Float64Array)) {\n    return compare(new Uint8Array(actual.buffer),\n                   new Uint8Array(expected.buffer)) === 0;\n\n  // 7.5 For all other Object pairs, including Array objects, equivalence is\n  // determined by having the same number of owned properties (as verified\n  // with Object.prototype.hasOwnProperty.call), the same set of keys\n  // (although not necessarily the same order), equivalent values for every\n  // corresponding key, and an identical 'prototype' property. Note: this\n  // accounts for both named and indexed properties on Arrays.\n  } else if (isBuffer(actual) !== isBuffer(expected)) {\n    return false;\n  } else {\n    memos = memos || {actual: [], expected: []};\n\n    var actualIndex = memos.actual.indexOf(actual);\n    if (actualIndex !== -1) {\n      if (actualIndex === memos.expected.indexOf(expected)) {\n        return true;\n      }\n    }\n\n    memos.actual.push(actual);\n    memos.expected.push(expected);\n\n    return objEquiv(actual, expected, strict, memos);\n  }\n}\n\nfunction isArguments(object) {\n  return Object.prototype.toString.call(object) == '[object Arguments]';\n}\n\nfunction objEquiv(a, b, strict, actualVisitedObjects) {\n  if (a === null || a === undefined || b === null || b === undefined)\n    return false;\n  // if one is a primitive, the other must be same\n  if (isPrimitive(a) || isPrimitive(b))\n    return a === b;\n  if (strict && Object.getPrototypeOf(a) !== Object.getPrototypeOf(b))\n    return false;\n  var aIsArgs = isArguments(a);\n  var bIsArgs = isArguments(b);\n  if ((aIsArgs && !bIsArgs) || (!aIsArgs && bIsArgs))\n    return false;\n  if (aIsArgs) {\n    a = pSlice.call(a);\n    b = pSlice.call(b);\n    return _deepEqual(a, b, strict);\n  }\n  var ka = objectKeys(a);\n  var kb = objectKeys(b);\n  var key, i;\n  // having the same number of owned properties (keys incorporates\n  // hasOwnProperty)\n  if (ka.length !== kb.length)\n    return false;\n  //the same set of keys (although not necessarily the same order),\n  ka.sort();\n  kb.sort();\n  //~~~cheap key test\n  for (i = ka.length - 1; i >= 0; i--) {\n    if (ka[i] !== kb[i])\n      return false;\n  }\n  //equivalent values for every corresponding key, and\n  //~~~possibly expensive deep test\n  for (i = ka.length - 1; i >= 0; i--) {\n    key = ka[i];\n    if (!_deepEqual(a[key], b[key], strict, actualVisitedObjects))\n      return false;\n  }\n  return true;\n}\n\n// 8. The non-equivalence assertion tests for any deep inequality.\n// assert.notDeepEqual(actual, expected, message_opt);\nassert.notDeepEqual = notDeepEqual;\nexport function notDeepEqual(actual, expected, message) {\n  if (_deepEqual(actual, expected, false)) {\n    fail(actual, expected, message, 'notDeepEqual', notDeepEqual);\n  }\n}\n\nassert.notDeepStrictEqual = notDeepStrictEqual;\nexport function notDeepStrictEqual(actual, expected, message) {\n  if (_deepEqual(actual, expected, true)) {\n    fail(actual, expected, message, 'notDeepStrictEqual', notDeepStrictEqual);\n  }\n}\n\n\n// 9. The strict equality assertion tests strict equality, as determined by ===.\n// assert.strictEqual(actual, expected, message_opt);\nassert.strictEqual = strictEqual;\nexport function strictEqual(actual, expected, message) {\n  if (actual !== expected) {\n    fail(actual, expected, message, '===', strictEqual);\n  }\n}\n\n// 10. The strict non-equality assertion tests for strict inequality, as\n// determined by !==.  assert.notStrictEqual(actual, expected, message_opt);\nassert.notStrictEqual = notStrictEqual;\nexport function notStrictEqual(actual, expected, message) {\n  if (actual === expected) {\n    fail(actual, expected, message, '!==', notStrictEqual);\n  }\n}\n\nfunction expectedException(actual, expected) {\n  if (!actual || !expected) {\n    return false;\n  }\n\n  if (Object.prototype.toString.call(expected) == '[object RegExp]') {\n    return expected.test(actual);\n  }\n\n  try {\n    if (actual instanceof expected) {\n      return true;\n    }\n  } catch (e) {\n    // Ignore.  The instanceof check doesn't work for arrow functions.\n  }\n\n  if (Error.isPrototypeOf(expected)) {\n    return false;\n  }\n\n  return expected.call({}, actual) === true;\n}\n\nfunction _tryBlock(block) {\n  var error;\n  try {\n    block();\n  } catch (e) {\n    error = e;\n  }\n  return error;\n}\n\nfunction _throws(shouldThrow, block, expected, message) {\n  var actual;\n\n  if (typeof block !== 'function') {\n    throw new TypeError('\"block\" argument must be a function');\n  }\n\n  if (typeof expected === 'string') {\n    message = expected;\n    expected = null;\n  }\n\n  actual = _tryBlock(block);\n\n  message = (expected && expected.name ? ' (' + expected.name + ').' : '.') +\n            (message ? ' ' + message : '.');\n\n  if (shouldThrow && !actual) {\n    fail(actual, expected, 'Missing expected exception' + message);\n  }\n\n  var userProvidedMessage = typeof message === 'string';\n  var isUnwantedException = !shouldThrow && isError(actual);\n  var isUnexpectedException = !shouldThrow && actual && !expected;\n\n  if ((isUnwantedException &&\n      userProvidedMessage &&\n      expectedException(actual, expected)) ||\n      isUnexpectedException) {\n    fail(actual, expected, 'Got unwanted exception' + message);\n  }\n\n  if ((shouldThrow && actual && expected &&\n      !expectedException(actual, expected)) || (!shouldThrow && actual)) {\n    throw actual;\n  }\n}\n\n// 11. Expected to throw an error:\n// assert.throws(block, Error_opt, message_opt);\nassert.throws = throws;\nexport function throws(block, /*optional*/error, /*optional*/message) {\n  _throws(true, block, error, message);\n}\n\n// EXTENSION! This is annoying to write outside this module.\nassert.doesNotThrow = doesNotThrow;\nexport function doesNotThrow(block, /*optional*/error, /*optional*/message) {\n  _throws(false, block, error, message);\n}\n\nassert.ifError = ifError;\nexport function ifError(err) {\n  if (err) throw err;\n}\n", "/* The MIT License (MIT)\n *\n * Copyright 2015-2018 <PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * Support for translating between Buffer instances and JavaScript\n * native types.\n *\n * {@link module:Layout~Layout|Layout} is the basis of a class\n * hierarchy that associates property names with sequences of encoded\n * bytes.\n *\n * Layouts are supported for these scalar (numeric) types:\n * * {@link module:Layout~UInt|Unsigned integers in little-endian\n *   format} with {@link module:Layout.u8|8-bit}, {@link\n *   module:Layout.u16|16-bit}, {@link module:Layout.u24|24-bit},\n *   {@link module:Layout.u32|32-bit}, {@link\n *   module:Layout.u40|40-bit}, and {@link module:Layout.u48|48-bit}\n *   representation ranges;\n * * {@link module:Layout~UIntBE|Unsigned integers in big-endian\n *   format} with {@link module:Layout.u16be|16-bit}, {@link\n *   module:Layout.u24be|24-bit}, {@link module:Layout.u32be|32-bit},\n *   {@link module:Layout.u40be|40-bit}, and {@link\n *   module:Layout.u48be|48-bit} representation ranges;\n * * {@link module:Layout~Int|Signed integers in little-endian\n *   format} with {@link module:Layout.s8|8-bit}, {@link\n *   module:Layout.s16|16-bit}, {@link module:Layout.s24|24-bit},\n *   {@link module:Layout.s32|32-bit}, {@link\n *   module:Layout.s40|40-bit}, and {@link module:Layout.s48|48-bit}\n *   representation ranges;\n * * {@link module:Layout~IntBE|Signed integers in big-endian format}\n *   with {@link module:Layout.s16be|16-bit}, {@link\n *   module:Layout.s24be|24-bit}, {@link module:Layout.s32be|32-bit},\n *   {@link module:Layout.s40be|40-bit}, and {@link\n *   module:Layout.s48be|48-bit} representation ranges;\n * * 64-bit integral values that decode to an exact (if magnitude is\n *   less than 2^53) or nearby integral Number in {@link\n *   module:Layout.nu64|unsigned little-endian}, {@link\n *   module:Layout.nu64be|unsigned big-endian}, {@link\n *   module:Layout.ns64|signed little-endian}, and {@link\n *   module:Layout.ns64be|unsigned big-endian} encodings;\n * * 32-bit floating point values with {@link\n *   module:Layout.f32|little-endian} and {@link\n *   module:Layout.f32be|big-endian} representations;\n * * 64-bit floating point values with {@link\n *   module:Layout.f64|little-endian} and {@link\n *   module:Layout.f64be|big-endian} representations;\n * * {@link module:Layout.const|Constants} that take no space in the\n *   encoded expression.\n *\n * and for these aggregate types:\n * * {@link module:Layout.seq|Sequence}s of instances of a {@link\n *   module:Layout~Layout|Layout}, with JavaScript representation as\n *   an Array and constant or data-dependent {@link\n *   module:Layout~Sequence#count|length};\n * * {@link module:Layout.struct|Structure}s that aggregate a\n *   heterogeneous sequence of {@link module:Layout~Layout|Layout}\n *   instances, with JavaScript representation as an Object;\n * * {@link module:Layout.union|Union}s that support multiple {@link\n *   module:Layout~VariantLayout|variant layouts} over a fixed\n *   (padded) or variable (not padded) span of bytes, using an\n *   unsigned integer at the start of the data or a separate {@link\n *   module:Layout.unionLayoutDiscriminator|layout element} to\n *   determine which layout to use when interpreting the buffer\n *   contents;\n * * {@link module:Layout.bits|BitStructure}s that contain a sequence\n *   of individual {@link\n *   module:Layout~BitStructure#addField|BitField}s packed into an 8,\n *   16, 24, or 32-bit unsigned integer starting at the least- or\n *   most-significant bit;\n * * {@link module:Layout.cstr|C strings} of varying length;\n * * {@link module:Layout.blob|Blobs} of fixed- or variable-{@link\n *   module:Layout~Blob#length|length} raw data.\n *\n * All {@link module:Layout~Layout|Layout} instances are immutable\n * after construction, to prevent internal state from becoming\n * inconsistent.\n *\n * @local Layout\n * @local ExternalLayout\n * @local GreedyCount\n * @local OffsetLayout\n * @local UInt\n * @local UIntBE\n * @local Int\n * @local IntBE\n * @local NearUInt64\n * @local NearUInt64BE\n * @local NearInt64\n * @local NearInt64BE\n * @local Float\n * @local FloatBE\n * @local Double\n * @local DoubleBE\n * @local Sequence\n * @local Structure\n * @local UnionDiscriminator\n * @local UnionLayoutDiscriminator\n * @local Union\n * @local VariantLayout\n * @local BitStructure\n * @local BitField\n * @local Boolean\n * @local Blob\n * @local CString\n * @local Constant\n * @local bindConstructorLayout\n * @module Layout\n * @license MIT\n * <AUTHOR> A. Bigot\n * @see {@link https://github.com/pabigot/buffer-layout|buffer-layout on GitHub}\n */\n\n'use strict';\n\n/**\n * Base class for layout objects.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support the {@link\n * Layout#encode|encode} or {@link Layout#decode|decode} functions.\n *\n * @param {Number} span - Initializer for {@link Layout#span|span}.  The\n * parameter must be an integer; a negative value signifies that the\n * span is {@link Layout#getSpan|value-specific}.\n *\n * @param {string} [property] - Initializer for {@link\n * Layout#property|property}.\n *\n * @abstract\n */\nclass Layout {\n  constructor(span, property) {\n    if (!Number.isInteger(span)) {\n      throw new TypeError('span must be an integer');\n    }\n\n    /** The span of the layout in bytes.\n     *\n     * Positive values are generally expected.\n     *\n     * Zero will only appear in {@link Constant}s and in {@link\n     * Sequence}s where the {@link Sequence#count|count} is zero.\n     *\n     * A negative value indicates that the span is value-specific, and\n     * must be obtained using {@link Layout#getSpan|getSpan}. */\n    this.span = span;\n\n    /** The property name used when this layout is represented in an\n     * Object.\n     *\n     * Used only for layouts that {@link Layout#decode|decode} to Object\n     * instances.  If left undefined the span of the unnamed layout will\n     * be treated as padding: it will not be mutated by {@link\n     * Layout#encode|encode} nor represented as a property in the\n     * decoded Object. */\n    this.property = property;\n  }\n\n  /** Function to create an Object into which decoded properties will\n   * be written.\n   *\n   * Used only for layouts that {@link Layout#decode|decode} to Object\n   * instances, which means:\n   * * {@link Structure}\n   * * {@link Union}\n   * * {@link VariantLayout}\n   * * {@link BitStructure}\n   *\n   * If left undefined the JavaScript representation of these layouts\n   * will be Object instances.\n   *\n   * See {@link bindConstructorLayout}.\n   */\n  makeDestinationObject() {\n    return {};\n  }\n\n  /**\n   * Decode from a Buffer into an JavaScript value.\n   *\n   * @param {Buffer} b - the buffer from which encoded data is read.\n   *\n   * @param {Number} [offset] - the offset at which the encoded data\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @returns {(Number|Array|Object)} - the value of the decoded data.\n   *\n   * @abstract\n   */\n  decode(b, offset) {\n    throw new Error('Layout is abstract');\n  }\n\n  /**\n   * Encode a JavaScript value into a Buffer.\n   *\n   * @param {(Number|Array|Object)} src - the value to be encoded into\n   * the buffer.  The type accepted depends on the (sub-)type of {@link\n   * Layout}.\n   *\n   * @param {Buffer} b - the buffer into which encoded data will be\n   * written.\n   *\n   * @param {Number} [offset] - the offset at which the encoded data\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @returns {Number} - the number of bytes encoded, including the\n   * space skipped for internal padding, but excluding data such as\n   * {@link Sequence#count|lengths} when stored {@link\n   * ExternalLayout|externally}.  This is the adjustment to `offset`\n   * producing the offset where data for the next layout would be\n   * written.\n   *\n   * @abstract\n   */\n  encode(src, b, offset) {\n    throw new Error('Layout is abstract');\n  }\n\n  /**\n   * Calculate the span of a specific instance of a layout.\n   *\n   * @param {Buffer} b - the buffer that contains an encoded instance.\n   *\n   * @param {Number} [offset] - the offset at which the encoded instance\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @return {Number} - the number of bytes covered by the layout\n   * instance.  If this method is not overridden in a subclass the\n   * definition-time constant {@link Layout#span|span} will be\n   * returned.\n   *\n   * @throws {RangeError} - if the length of the value cannot be\n   * determined.\n   */\n  getSpan(b, offset) {\n    if (0 > this.span) {\n      throw new RangeError('indeterminate span');\n    }\n    return this.span;\n  }\n\n  /**\n   * Replicate the layout using a new property.\n   *\n   * This function must be used to get a structurally-equivalent layout\n   * with a different name since all {@link Layout} instances are\n   * immutable.\n   *\n   * **NOTE** This is a shallow copy.  All fields except {@link\n   * Layout#property|property} are strictly equal to the origin layout.\n   *\n   * @param {String} property - the value for {@link\n   * Layout#property|property} in the replica.\n   *\n   * @returns {Layout} - the copy with {@link Layout#property|property}\n   * set to `property`.\n   */\n  replicate(property) {\n    const rv = Object.create(this.constructor.prototype);\n    Object.assign(rv, this);\n    rv.property = property;\n    return rv;\n  }\n\n  /**\n   * Create an object from layout properties and an array of values.\n   *\n   * **NOTE** This function returns `undefined` if invoked on a layout\n   * that does not return its value as an Object.  Objects are\n   * returned for things that are a {@link Structure}, which includes\n   * {@link VariantLayout|variant layouts} if they are structures, and\n   * excludes {@link Union}s.  If you want this feature for a union\n   * you must use {@link Union.getVariant|getVariant} to select the\n   * desired layout.\n   *\n   * @param {Array} values - an array of values that correspond to the\n   * default order for properties.  As with {@link Layout#decode|decode}\n   * layout elements that have no property name are skipped when\n   * iterating over the array values.  Only the top-level properties are\n   * assigned; arguments are not assigned to properties of contained\n   * layouts.  Any unused values are ignored.\n   *\n   * @return {(Object|undefined)}\n   */\n  fromArray(values) {\n    return undefined;\n  }\n}\nexports.Layout = Layout;\n\n/* Provide text that carries a name (such as for a function that will\n * be throwing an error) annotated with the property of a given layout\n * (such as one for which the value was unacceptable).\n *\n * @ignore */\nfunction nameWithProperty(name, lo) {\n  if (lo.property) {\n    return name + '[' + lo.property + ']';\n  }\n  return name;\n}\nexports.nameWithProperty = nameWithProperty;\n\n/**\n * Augment a class so that instances can be encoded/decoded using a\n * given layout.\n *\n * Calling this function couples `Class` with `layout` in several ways:\n *\n * * `Class.layout_` becomes a static member property equal to `layout`;\n * * `layout.boundConstructor_` becomes a static member property equal\n *    to `Class`;\n * * The {@link Layout#makeDestinationObject|makeDestinationObject()}\n *   property of `layout` is set to a function that returns a `new\n *   Class()`;\n * * `Class.decode(b, offset)` becomes a static member function that\n *   delegates to {@link Layout#decode|layout.decode}.  The\n *   synthesized function may be captured and extended.\n * * `Class.prototype.encode(b, offset)` provides an instance member\n *   function that delegates to {@link Layout#encode|layout.encode}\n *   with `src` set to `this`.  The synthesized function may be\n *   captured and extended, but when the extension is invoked `this`\n *   must be explicitly bound to the instance.\n *\n * @param {class} Class - a JavaScript class with a nullary\n * constructor.\n *\n * @param {Layout} layout - the {@link Layout} instance used to encode\n * instances of `Class`.\n */\nfunction bindConstructorLayout(Class, layout) {\n  if ('function' !== typeof Class) {\n    throw new TypeError('Class must be constructor');\n  }\n  if (Class.hasOwnProperty('layout_')) {\n    throw new Error('Class is already bound to a layout');\n  }\n  if (!(layout && (layout instanceof Layout))) {\n    throw new TypeError('layout must be a Layout');\n  }\n  if (layout.hasOwnProperty('boundConstructor_')) {\n    throw new Error('layout is already bound to a constructor');\n  }\n  Class.layout_ = layout;\n  layout.boundConstructor_ = Class;\n  layout.makeDestinationObject = (() => new Class());\n  Object.defineProperty(Class.prototype, 'encode', {\n    value: function(b, offset) {\n      return layout.encode(this, b, offset);\n    },\n    writable: true,\n  });\n  Object.defineProperty(Class, 'decode', {\n    value: function(b, offset) {\n      return layout.decode(b, offset);\n    },\n    writable: true,\n  });\n}\nexports.bindConstructorLayout = bindConstructorLayout;\n\n/**\n * An object that behaves like a layout but does not consume space\n * within its containing layout.\n *\n * This is primarily used to obtain metadata about a member, such as a\n * {@link OffsetLayout} that can provide data about a {@link\n * Layout#getSpan|value-specific span}.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support {@link\n * ExternalLayout#isCount|isCount} or other {@link Layout} functions.\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @abstract\n * @augments {Layout}\n */\nclass ExternalLayout extends Layout {\n  /**\n   * Return `true` iff the external layout decodes to an unsigned\n   * integer layout.\n   *\n   * In that case it can be used as the source of {@link\n   * Sequence#count|Sequence counts}, {@link Blob#length|Blob lengths},\n   * or as {@link UnionLayoutDiscriminator#layout|external union\n   * discriminators}.\n   *\n   * @abstract\n   */\n  isCount() {\n    throw new Error('ExternalLayout is abstract');\n  }\n}\n\n/**\n * An {@link ExternalLayout} that determines its {@link\n * Layout#decode|value} based on offset into and length of the buffer\n * on which it is invoked.\n *\n * *Factory*: {@link module:Layout.greedy|greedy}\n *\n * @param {Number} [elementSpan] - initializer for {@link\n * GreedyCount#elementSpan|elementSpan}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {ExternalLayout}\n */\nclass GreedyCount extends ExternalLayout {\n  constructor(elementSpan, property) {\n    if (undefined === elementSpan) {\n      elementSpan = 1;\n    }\n    if ((!Number.isInteger(elementSpan)) || (0 >= elementSpan)) {\n      throw new TypeError('elementSpan must be a (positive) integer');\n    }\n    super(-1, property);\n\n    /** The layout for individual elements of the sequence.  The value\n     * must be a positive integer.  If not provided, the value will be\n     * 1. */\n    this.elementSpan = elementSpan;\n  }\n\n  /** @override */\n  isCount() {\n    return true;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const rem = b.length - offset;\n    return Math.floor(rem / this.elementSpan);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    return 0;\n  }\n}\n\n/**\n * An {@link ExternalLayout} that supports accessing a {@link Layout}\n * at a fixed offset from the start of another Layout.  The offset may\n * be before, within, or after the base layout.\n *\n * *Factory*: {@link module:Layout.offset|offset}\n *\n * @param {Layout} layout - initializer for {@link\n * OffsetLayout#layout|layout}, modulo `property`.\n *\n * @param {Number} [offset] - Initializes {@link\n * OffsetLayout#offset|offset}.  Defaults to zero.\n *\n * @param {string} [property] - Optional new property name for a\n * {@link Layout#replicate| replica} of `layout` to be used as {@link\n * OffsetLayout#layout|layout}.  If not provided the `layout` is used\n * unchanged.\n *\n * @augments {Layout}\n */\nclass OffsetLayout extends ExternalLayout {\n  constructor(layout, offset, property) {\n    if (!(layout instanceof Layout)) {\n      throw new TypeError('layout must be a Layout');\n    }\n\n    if (undefined === offset) {\n      offset = 0;\n    } else if (!Number.isInteger(offset)) {\n      throw new TypeError('offset must be integer or undefined');\n    }\n\n    super(layout.span, property || layout.property);\n\n    /** The subordinated layout. */\n    this.layout = layout;\n\n    /** The location of {@link OffsetLayout#layout} relative to the\n     * start of another layout.\n     *\n     * The value may be positive or negative, but an error will thrown\n     * if at the point of use it goes outside the span of the Buffer\n     * being accessed.  */\n    this.offset = offset;\n  }\n\n  /** @override */\n  isCount() {\n    return ((this.layout instanceof UInt)\n            || (this.layout instanceof UIntBE));\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return this.layout.decode(b, offset + this.offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return this.layout.encode(src, b, offset + this.offset);\n  }\n}\n\n/**\n * Represent an unsigned integer in little-endian format.\n *\n * *Factory*: {@link module:Layout.u8|u8}, {@link\n *  module:Layout.u16|u16}, {@link module:Layout.u24|u24}, {@link\n *  module:Layout.u32|u32}, {@link module:Layout.u40|u40}, {@link\n *  module:Layout.u48|u48}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UInt extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readUIntLE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeUIntLE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent an unsigned integer in big-endian format.\n *\n * *Factory*: {@link module:Layout.u8be|u8be}, {@link\n * module:Layout.u16be|u16be}, {@link module:Layout.u24be|u24be},\n * {@link module:Layout.u32be|u32be}, {@link\n * module:Layout.u40be|u40be}, {@link module:Layout.u48be|u48be}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UIntBE extends Layout {\n  constructor(span, property) {\n    super( span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readUIntBE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeUIntBE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent a signed integer in little-endian format.\n *\n * *Factory*: {@link module:Layout.s8|s8}, {@link\n *  module:Layout.s16|s16}, {@link module:Layout.s24|s24}, {@link\n *  module:Layout.s32|s32}, {@link module:Layout.s40|s40}, {@link\n *  module:Layout.s48|s48}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Int extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readIntLE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeIntLE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent a signed integer in big-endian format.\n *\n * *Factory*: {@link module:Layout.s8be|s8be}, {@link\n * module:Layout.s16be|s16be}, {@link module:Layout.s24be|s24be},\n * {@link module:Layout.s32be|s32be}, {@link\n * module:Layout.s40be|s40be}, {@link module:Layout.s48be|s48be}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass IntBE extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readIntBE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeIntBE(src, offset, this.span);\n    return this.span;\n  }\n}\n\nconst V2E32 = Math.pow(2, 32);\n\n/* True modulus high and low 32-bit words, where low word is always\n * non-negative. */\nfunction divmodInt64(src) {\n  const hi32 = Math.floor(src / V2E32);\n  const lo32 = src - (hi32 * V2E32);\n  return {hi32, lo32};\n}\n/* Reconstruct Number from quotient and non-negative remainder */\nfunction roundedInt64(hi32, lo32) {\n  return hi32 * V2E32 + lo32;\n}\n\n/**\n * Represent an unsigned 64-bit integer in little-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.nu64|nu64}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearUInt64 extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const lo32 = b.readUInt32LE(offset);\n    const hi32 = b.readUInt32LE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32LE(split.lo32, offset);\n    b.writeUInt32LE(split.hi32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent an unsigned 64-bit integer in big-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.nu64be|nu64be}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearUInt64BE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const hi32 = b.readUInt32BE(offset);\n    const lo32 = b.readUInt32BE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32BE(split.hi32, offset);\n    b.writeUInt32BE(split.lo32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a signed 64-bit integer in little-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.ns64|ns64}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearInt64 extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const lo32 = b.readUInt32LE(offset);\n    const hi32 = b.readInt32LE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32LE(split.lo32, offset);\n    b.writeInt32LE(split.hi32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a signed 64-bit integer in big-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.ns64be|ns64be}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearInt64BE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const hi32 = b.readInt32BE(offset);\n    const lo32 = b.readUInt32BE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeInt32BE(split.hi32, offset);\n    b.writeUInt32BE(split.lo32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a 32-bit floating point number in little-endian format.\n *\n * *Factory*: {@link module:Layout.f32|f32}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Float extends Layout {\n  constructor(property) {\n    super(4, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readFloatLE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeFloatLE(src, offset);\n    return 4;\n  }\n}\n\n/**\n * Represent a 32-bit floating point number in big-endian format.\n *\n * *Factory*: {@link module:Layout.f32be|f32be}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass FloatBE extends Layout {\n  constructor(property) {\n    super(4, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readFloatBE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeFloatBE(src, offset);\n    return 4;\n  }\n}\n\n/**\n * Represent a 64-bit floating point number in little-endian format.\n *\n * *Factory*: {@link module:Layout.f64|f64}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Double extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readDoubleLE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeDoubleLE(src, offset);\n    return 8;\n  }\n}\n\n/**\n * Represent a 64-bit floating point number in big-endian format.\n *\n * *Factory*: {@link module:Layout.f64be|f64be}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass DoubleBE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readDoubleBE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeDoubleBE(src, offset);\n    return 8;\n  }\n}\n\n/**\n * Represent a contiguous sequence of a specific layout as an Array.\n *\n * *Factory*: {@link module:Layout.seq|seq}\n *\n * @param {Layout} elementLayout - initializer for {@link\n * Sequence#elementLayout|elementLayout}.\n *\n * @param {(Number|ExternalLayout)} count - initializer for {@link\n * Sequence#count|count}.  The parameter must be either a positive\n * integer or an instance of {@link ExternalLayout}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Sequence extends Layout {\n  constructor(elementLayout, count, property) {\n    if (!(elementLayout instanceof Layout)) {\n      throw new TypeError('elementLayout must be a Layout');\n    }\n    if (!(((count instanceof ExternalLayout) && count.isCount())\n          || (Number.isInteger(count) && (0 <= count)))) {\n      throw new TypeError('count must be non-negative integer '\n                          + 'or an unsigned integer ExternalLayout');\n    }\n    let span = -1;\n    if ((!(count instanceof ExternalLayout))\n        && (0 < elementLayout.span)) {\n      span = count * elementLayout.span;\n    }\n\n    super(span, property);\n\n    /** The layout for individual elements of the sequence. */\n    this.elementLayout = elementLayout;\n\n    /** The number of elements in the sequence.\n     *\n     * This will be either a non-negative integer or an instance of\n     * {@link ExternalLayout} for which {@link\n     * ExternalLayout#isCount|isCount()} is `true`. */\n    this.count = count;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = 0;\n    let count = this.count;\n    if (count instanceof ExternalLayout) {\n      count = count.decode(b, offset);\n    }\n    if (0 < this.elementLayout.span) {\n      span = count * this.elementLayout.span;\n    } else {\n      let idx = 0;\n      while (idx < count) {\n        span += this.elementLayout.getSpan(b, offset + span);\n        ++idx;\n      }\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const rv = [];\n    let i = 0;\n    let count = this.count;\n    if (count instanceof ExternalLayout) {\n      count = count.decode(b, offset);\n    }\n    while (i < count) {\n      rv.push(this.elementLayout.decode(b, offset));\n      offset += this.elementLayout.getSpan(b, offset);\n      i += 1;\n    }\n    return rv;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Sequence}.\n   *\n   * **NOTE** If `src` is shorter than {@link Sequence#count|count} then\n   * the unused space in the buffer is left unchanged.  If `src` is\n   * longer than {@link Sequence#count|count} the unneeded elements are\n   * ignored.\n   *\n   * **NOTE** If {@link Layout#count|count} is an instance of {@link\n   * ExternalLayout} then the length of `src` will be encoded as the\n   * count after `src` is encoded. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const elo = this.elementLayout;\n    const span = src.reduce((span, v) => {\n      return span + elo.encode(v, b, offset + span);\n    }, 0);\n    if (this.count instanceof ExternalLayout) {\n      this.count.encode(src.length, b, offset);\n    }\n    return span;\n  }\n}\n\n/**\n * Represent a contiguous sequence of arbitrary layout elements as an\n * Object.\n *\n * *Factory*: {@link module:Layout.struct|struct}\n *\n * **NOTE** The {@link Layout#span|span} of the structure is variable\n * if any layout in {@link Structure#fields|fields} has a variable\n * span.  When {@link Layout#encode|encoding} we must have a value for\n * all variable-length fields, or we wouldn't be able to figure out\n * how much space to use for storage.  We can only identify the value\n * for a field when it has a {@link Layout#property|property}.  As\n * such, although a structure may contain both unnamed fields and\n * variable-length fields, it cannot contain an unnamed\n * variable-length field.\n *\n * @param {Layout[]} fields - initializer for {@link\n * Structure#fields|fields}.  An error is raised if this contains a\n * variable-length field for which a {@link Layout#property|property}\n * is not defined.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @param {Boolean} [decodePrefixes] - initializer for {@link\n * Structure#decodePrefixes|property}.\n *\n * @throws {Error} - if `fields` contains an unnamed variable-length\n * layout.\n *\n * @augments {Layout}\n */\nclass Structure extends Layout {\n  constructor(fields, property, decodePrefixes) {\n    if (!(Array.isArray(fields)\n          && fields.reduce((acc, v) => acc && (v instanceof Layout), true))) {\n      throw new TypeError('fields must be array of Layout instances');\n    }\n    if (('boolean' === typeof property)\n        && (undefined === decodePrefixes)) {\n      decodePrefixes = property;\n      property = undefined;\n    }\n\n    /* Verify absence of unnamed variable-length fields. */\n    for (const fd of fields) {\n      if ((0 > fd.span)\n          && (undefined === fd.property)) {\n        throw new Error('fields cannot contain unnamed variable-length layout');\n      }\n    }\n\n    let span = -1;\n    try {\n      span = fields.reduce((span, fd) => span + fd.getSpan(), 0);\n    } catch (e) {\n    }\n    super(span, property);\n\n    /** The sequence of {@link Layout} values that comprise the\n     * structure.\n     *\n     * The individual elements need not be the same type, and may be\n     * either scalar or aggregate layouts.  If a member layout leaves\n     * its {@link Layout#property|property} undefined the\n     * corresponding region of the buffer associated with the element\n     * will not be mutated.\n     *\n     * @type {Layout[]} */\n    this.fields = fields;\n\n    /** Control behavior of {@link Layout#decode|decode()} given short\n     * buffers.\n     *\n     * In some situations a structure many be extended with additional\n     * fields over time, with older installations providing only a\n     * prefix of the full structure.  If this property is `true`\n     * decoding will accept those buffers and leave subsequent fields\n     * undefined, as long as the buffer ends at a field boundary.\n     * Defaults to `false`. */\n    this.decodePrefixes = !!decodePrefixes;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = 0;\n    try {\n      span = this.fields.reduce((span, fd) => {\n        const fsp = fd.getSpan(b, offset);\n        offset += fsp;\n        return span + fsp;\n      }, 0);\n    } catch (e) {\n      throw new RangeError('indeterminate span');\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const dest = this.makeDestinationObject();\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        dest[fd.property] = fd.decode(b, offset);\n      }\n      offset += fd.getSpan(b, offset);\n      if (this.decodePrefixes\n          && (b.length === offset)) {\n        break;\n      }\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Structure}.\n   *\n   * If `src` is missing a property for a member with a defined {@link\n   * Layout#property|property} the corresponding region of the buffer is\n   * left unmodified. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const firstOffset = offset;\n    let lastOffset = 0;\n    let lastWrote = 0;\n    for (const fd of this.fields) {\n      let span = fd.span;\n      lastWrote = (0 < span) ? span : 0;\n      if (undefined !== fd.property) {\n        const fv = src[fd.property];\n        if (undefined !== fv) {\n          lastWrote = fd.encode(fv, b, offset);\n          if (0 > span) {\n            /* Read the as-encoded span, which is not necessarily the\n             * same as what we wrote. */\n            span = fd.getSpan(b, offset);\n          }\n        }\n      }\n      lastOffset = offset;\n      offset += span;\n    }\n    /* Use (lastOffset + lastWrote) instead of offset because the last\n     * item may have had a dynamic length and we don't want to include\n     * the padding between it and the end of the space reserved for\n     * it. */\n    return (lastOffset + lastWrote) - firstOffset;\n  }\n\n  /** @override */\n  fromArray(values) {\n    const dest = this.makeDestinationObject();\n    for (const fd of this.fields) {\n      if ((undefined !== fd.property)\n          && (0 < values.length)) {\n        dest[fd.property] = values.shift();\n      }\n    }\n    return dest;\n  }\n\n  /**\n   * Get access to the layout of a given property.\n   *\n   * @param {String} property - the structure member of interest.\n   *\n   * @return {Layout} - the layout associated with `property`, or\n   * undefined if there is no such property.\n   */\n  layoutFor(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return fd;\n      }\n    }\n  }\n\n  /**\n   * Get the offset of a structure member.\n   *\n   * @param {String} property - the structure member of interest.\n   *\n   * @return {Number} - the offset in bytes to the start of `property`\n   * within the structure, or undefined if `property` is not a field\n   * within the structure.  If the property is a member but follows a\n   * variable-length structure member a negative number will be\n   * returned.\n   */\n  offsetOf(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    let offset = 0;\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return offset;\n      }\n      if (0 > fd.span) {\n        offset = -1;\n      } else if (0 <= offset) {\n        offset += fd.span;\n      }\n    }\n  }\n}\n\n/**\n * An object that can provide a {@link\n * Union#discriminator|discriminator} API for {@link Union}.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support the {@link\n * UnionDiscriminator#encode|encode} or {@link\n * UnionDiscriminator#decode|decode} functions.\n *\n * @param {string} [property] - Default for {@link\n * UnionDiscriminator#property|property}.\n *\n * @abstract\n */\nclass UnionDiscriminator {\n  constructor(property) {\n    /** The {@link Layout#property|property} to be used when the\n     * discriminator is referenced in isolation (generally when {@link\n     * Union#decode|Union decode} cannot delegate to a specific\n     * variant). */\n    this.property = property;\n  }\n\n  /** Analog to {@link Layout#decode|Layout decode} for union discriminators.\n   *\n   * The implementation of this method need not reference the buffer if\n   * variant information is available through other means. */\n  decode() {\n    throw new Error('UnionDiscriminator is abstract');\n  }\n\n  /** Analog to {@link Layout#decode|Layout encode} for union discriminators.\n   *\n   * The implementation of this method need not store the value if\n   * variant information is maintained through other means. */\n  encode() {\n    throw new Error('UnionDiscriminator is abstract');\n  }\n}\n\n/**\n * An object that can provide a {@link\n * UnionDiscriminator|discriminator API} for {@link Union} using an\n * unsigned integral {@link Layout} instance located either inside or\n * outside the union.\n *\n * @param {ExternalLayout} layout - initializes {@link\n * UnionLayoutDiscriminator#layout|layout}.  Must satisfy {@link\n * ExternalLayout#isCount|isCount()}.\n *\n * @param {string} [property] - Default for {@link\n * UnionDiscriminator#property|property}, superseding the property\n * from `layout`, but defaulting to `variant` if neither `property`\n * nor layout provide a property name.\n *\n * @augments {UnionDiscriminator}\n */\nclass UnionLayoutDiscriminator extends UnionDiscriminator {\n  constructor(layout, property) {\n    if (!((layout instanceof ExternalLayout)\n          && layout.isCount())) {\n      throw new TypeError('layout must be an unsigned integer ExternalLayout');\n    }\n\n    super(property || layout.property || 'variant');\n\n    /** The {@link ExternalLayout} used to access the discriminator\n     * value. */\n    this.layout = layout;\n  }\n\n  /** Delegate decoding to {@link UnionLayoutDiscriminator#layout|layout}. */\n  decode(b, offset) {\n    return this.layout.decode(b, offset);\n  }\n\n  /** Delegate encoding to {@link UnionLayoutDiscriminator#layout|layout}. */\n  encode(src, b, offset) {\n    return this.layout.encode(src, b, offset);\n  }\n}\n\n/**\n * Represent any number of span-compatible layouts.\n *\n * *Factory*: {@link module:Layout.union|union}\n *\n * If the union has a {@link Union#defaultLayout|default layout} that\n * layout must have a non-negative {@link Layout#span|span}.  The span\n * of a fixed-span union includes its {@link\n * Union#discriminator|discriminator} if the variant is a {@link\n * Union#usesPrefixDiscriminator|prefix of the union}, plus the span\n * of its {@link Union#defaultLayout|default layout}.\n *\n * If the union does not have a default layout then the encoded span\n * of the union depends on the encoded span of its variant (which may\n * be fixed or variable).\n *\n * {@link VariantLayout#layout|Variant layout}s are added through\n * {@link Union#addVariant|addVariant}.  If the union has a default\n * layout, the span of the {@link VariantLayout#layout|layout\n * contained by the variant} must not exceed the span of the {@link\n * Union#defaultLayout|default layout} (minus the span of a {@link\n * Union#usesPrefixDiscriminator|prefix disriminator}, if used).  The\n * span of the variant will equal the span of the union itself.\n *\n * The variant for a buffer can only be identified from the {@link\n * Union#discriminator|discriminator} {@link\n * UnionDiscriminator#property|property} (in the case of the {@link\n * Union#defaultLayout|default layout}), or by using {@link\n * Union#getVariant|getVariant} and examining the resulting {@link\n * VariantLayout} instance.\n *\n * A variant compatible with a JavaScript object can be identified\n * using {@link Union#getSourceVariant|getSourceVariant}.\n *\n * @param {(UnionDiscriminator|ExternalLayout|Layout)} discr - How to\n * identify the layout used to interpret the union contents.  The\n * parameter must be an instance of {@link UnionDiscriminator}, an\n * {@link ExternalLayout} that satisfies {@link\n * ExternalLayout#isCount|isCount()}, or {@link UInt} (or {@link\n * UIntBE}).  When a non-external layout element is passed the layout\n * appears at the start of the union.  In all cases the (synthesized)\n * {@link UnionDiscriminator} instance is recorded as {@link\n * Union#discriminator|discriminator}.\n *\n * @param {(Layout|null)} defaultLayout - initializer for {@link\n * Union#defaultLayout|defaultLayout}.  If absent defaults to `null`.\n * If `null` there is no default layout: the union has data-dependent\n * length and attempts to decode or encode unrecognized variants will\n * throw an exception.  A {@link Layout} instance must have a\n * non-negative {@link Layout#span|span}, and if it lacks a {@link\n * Layout#property|property} the {@link\n * Union#defaultLayout|defaultLayout} will be a {@link\n * Layout#replicate|replica} with property `content`.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Union extends Layout {\n  constructor(discr, defaultLayout, property) {\n    const upv = ((discr instanceof UInt)\n               || (discr instanceof UIntBE));\n    if (upv) {\n      discr = new UnionLayoutDiscriminator(new OffsetLayout(discr));\n    } else if ((discr instanceof ExternalLayout)\n               && discr.isCount()) {\n      discr = new UnionLayoutDiscriminator(discr);\n    } else if (!(discr instanceof UnionDiscriminator)) {\n      throw new TypeError('discr must be a UnionDiscriminator '\n                          + 'or an unsigned integer layout');\n    }\n    if (undefined === defaultLayout) {\n      defaultLayout = null;\n    }\n    if (!((null === defaultLayout)\n          || (defaultLayout instanceof Layout))) {\n      throw new TypeError('defaultLayout must be null or a Layout');\n    }\n    if (null !== defaultLayout) {\n      if (0 > defaultLayout.span) {\n        throw new Error('defaultLayout must have constant span');\n      }\n      if (undefined === defaultLayout.property) {\n        defaultLayout = defaultLayout.replicate('content');\n      }\n    }\n\n    /* The union span can be estimated only if there's a default\n     * layout.  The union spans its default layout, plus any prefix\n     * variant layout.  By construction both layouts, if present, have\n     * non-negative span. */\n    let span = -1;\n    if (defaultLayout) {\n      span = defaultLayout.span;\n      if ((0 <= span) && upv) {\n        span += discr.layout.span;\n      }\n    }\n    super(span, property);\n\n    /** The interface for the discriminator value in isolation.\n     *\n     * This a {@link UnionDiscriminator} either passed to the\n     * constructor or synthesized from the `discr` constructor\n     * argument.  {@link\n     * Union#usesPrefixDiscriminator|usesPrefixDiscriminator} will be\n     * `true` iff the `discr` parameter was a non-offset {@link\n     * Layout} instance. */\n    this.discriminator = discr;\n\n    /** `true` if the {@link Union#discriminator|discriminator} is the\n     * first field in the union.\n     *\n     * If `false` the discriminator is obtained from somewhere\n     * else. */\n    this.usesPrefixDiscriminator = upv;\n\n    /** The layout for non-discriminator content when the value of the\n     * discriminator is not recognized.\n     *\n     * This is the value passed to the constructor.  It is\n     * structurally equivalent to the second component of {@link\n     * Union#layout|layout} but may have a different property\n     * name. */\n    this.defaultLayout = defaultLayout;\n\n    /** A registry of allowed variants.\n     *\n     * The keys are unsigned integers which should be compatible with\n     * {@link Union.discriminator|discriminator}.  The property value\n     * is the corresponding {@link VariantLayout} instances assigned\n     * to this union by {@link Union#addVariant|addVariant}.\n     *\n     * **NOTE** The registry remains mutable so that variants can be\n     * {@link Union#addVariant|added} at any time.  Users should not\n     * manipulate the content of this property. */\n    this.registry = {};\n\n    /* Private variable used when invoking getSourceVariant */\n    let boundGetSourceVariant = this.defaultGetSourceVariant.bind(this);\n\n    /** Function to infer the variant selected by a source object.\n     *\n     * Defaults to {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant} but may\n     * be overridden using {@link\n     * Union#configGetSourceVariant|configGetSourceVariant}.\n     *\n     * @param {Object} src - as with {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant}.\n     *\n     * @returns {(undefined|VariantLayout)} The default variant\n     * (`undefined`) or first registered variant that uses a property\n     * available in `src`. */\n    this.getSourceVariant = function(src) {\n      return boundGetSourceVariant(src);\n    };\n\n    /** Function to override the implementation of {@link\n     * Union#getSourceVariant|getSourceVariant}.\n     *\n     * Use this if the desired variant cannot be identified using the\n     * algorithm of {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant}.\n     *\n     * **NOTE** The provided function will be invoked bound to this\n     * Union instance, providing local access to {@link\n     * Union#registry|registry}.\n     *\n     * @param {Function} gsv - a function that follows the API of\n     * {@link Union#defaultGetSourceVariant|defaultGetSourceVariant}. */\n    this.configGetSourceVariant = function(gsv) {\n      boundGetSourceVariant = gsv.bind(this);\n    };\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Default layouts always have non-negative span, so we don't have\n     * one and we have to recognize the variant which will in turn\n     * determine the span. */\n    const vlo = this.getVariant(b, offset);\n    if (!vlo) {\n      throw new Error('unable to determine span for unrecognized variant');\n    }\n    return vlo.getSpan(b, offset);\n  }\n\n  /**\n   * Method to infer a registered Union variant compatible with `src`.\n   *\n   * The first satisified rule in the following sequence defines the\n   * return value:\n   * * If `src` has properties matching the Union discriminator and\n   *   the default layout, `undefined` is returned regardless of the\n   *   value of the discriminator property (this ensures the default\n   *   layout will be used);\n   * * If `src` has a property matching the Union discriminator, the\n   *   value of the discriminator identifies a registered variant, and\n   *   either (a) the variant has no layout, or (b) `src` has the\n   *   variant's property, then the variant is returned (because the\n   *   source satisfies the constraints of the variant it identifies);\n   * * If `src` does not have a property matching the Union\n   *   discriminator, but does have a property matching a registered\n   *   variant, then the variant is returned (because the source\n   *   matches a variant without an explicit conflict);\n   * * An error is thrown (because we either can't identify a variant,\n   *   or we were explicitly told the variant but can't satisfy it).\n   *\n   * @param {Object} src - an object presumed to be compatible with\n   * the content of the Union.\n   *\n   * @return {(undefined|VariantLayout)} - as described above.\n   *\n   * @throws {Error} - if `src` cannot be associated with a default or\n   * registered variant.\n   */\n  defaultGetSourceVariant(src) {\n    if (src.hasOwnProperty(this.discriminator.property)) {\n      if (this.defaultLayout\n          && src.hasOwnProperty(this.defaultLayout.property)) {\n        return undefined;\n      }\n      const vlo = this.registry[src[this.discriminator.property]];\n      if (vlo\n          && ((!vlo.layout)\n              || src.hasOwnProperty(vlo.property))) {\n        return vlo;\n      }\n    } else {\n      for (const tag in this.registry) {\n        const vlo = this.registry[tag];\n        if (src.hasOwnProperty(vlo.property)) {\n          return vlo;\n        }\n      }\n    }\n    throw new Error('unable to infer src variant');\n  }\n\n  /** Implement {@link Layout#decode|decode} for {@link Union}.\n   *\n   * If the variant is {@link Union#addVariant|registered} the return\n   * value is an instance of that variant, with no explicit\n   * discriminator.  Otherwise the {@link Union#defaultLayout|default\n   * layout} is used to decode the content. */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let dest;\n    const dlo = this.discriminator;\n    const discr = dlo.decode(b, offset);\n    let clo = this.registry[discr];\n    if (undefined === clo) {\n      let contentOffset = 0;\n      clo = this.defaultLayout;\n      if (this.usesPrefixDiscriminator) {\n        contentOffset = dlo.layout.span;\n      }\n      dest = this.makeDestinationObject();\n      dest[dlo.property] = discr;\n      dest[clo.property] = this.defaultLayout.decode(b, offset + contentOffset);\n    } else {\n      dest = clo.decode(b, offset);\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Union}.\n   *\n   * This API assumes the `src` object is consistent with the union's\n   * {@link Union#defaultLayout|default layout}.  To encode variants\n   * use the appropriate variant-specific {@link VariantLayout#encode}\n   * method. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const vlo = this.getSourceVariant(src);\n    if (undefined === vlo) {\n      const dlo = this.discriminator;\n      const clo = this.defaultLayout;\n      let contentOffset = 0;\n      if (this.usesPrefixDiscriminator) {\n        contentOffset = dlo.layout.span;\n      }\n      dlo.encode(src[dlo.property], b, offset);\n      return contentOffset + clo.encode(src[clo.property], b,\n                                        offset + contentOffset);\n    }\n    return vlo.encode(src, b, offset);\n  }\n\n  /** Register a new variant structure within a union.  The newly\n   * created variant is returned.\n   *\n   * @param {Number} variant - initializer for {@link\n   * VariantLayout#variant|variant}.\n   *\n   * @param {Layout} layout - initializer for {@link\n   * VariantLayout#layout|layout}.\n   *\n   * @param {String} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {VariantLayout} */\n  addVariant(variant, layout, property) {\n    const rv = new VariantLayout(this, variant, layout, property);\n    this.registry[variant] = rv;\n    return rv;\n  }\n\n  /**\n   * Get the layout associated with a registered variant.\n   *\n   * If `vb` does not produce a registered variant the function returns\n   * `undefined`.\n   *\n   * @param {(Number|Buffer)} vb - either the variant number, or a\n   * buffer from which the discriminator is to be read.\n   *\n   * @param {Number} offset - offset into `vb` for the start of the\n   * union.  Used only when `vb` is an instance of {Buffer}.\n   *\n   * @return {({VariantLayout}|undefined)}\n   */\n  getVariant(vb, offset) {\n    let variant = vb;\n    if (Buffer.isBuffer(vb)) {\n      if (undefined === offset) {\n        offset = 0;\n      }\n      variant = this.discriminator.decode(vb, offset);\n    }\n    return this.registry[variant];\n  }\n}\n\n/**\n * Represent a specific variant within a containing union.\n *\n * **NOTE** The {@link Layout#span|span} of the variant may include\n * the span of the {@link Union#discriminator|discriminator} used to\n * identify it, but values read and written using the variant strictly\n * conform to the content of {@link VariantLayout#layout|layout}.\n *\n * **NOTE** User code should not invoke this constructor directly.  Use\n * the union {@link Union#addVariant|addVariant} helper method.\n *\n * @param {Union} union - initializer for {@link\n * VariantLayout#union|union}.\n *\n * @param {Number} variant - initializer for {@link\n * VariantLayout#variant|variant}.\n *\n * @param {Layout} [layout] - initializer for {@link\n * VariantLayout#layout|layout}.  If absent the variant carries no\n * data.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.  Unlike many other layouts, variant\n * layouts normally include a property name so they can be identified\n * within their containing {@link Union}.  The property identifier may\n * be absent only if `layout` is is absent.\n *\n * @augments {Layout}\n */\nclass VariantLayout extends Layout {\n  constructor(union, variant, layout, property) {\n    if (!(union instanceof Union)) {\n      throw new TypeError('union must be a Union');\n    }\n    if ((!Number.isInteger(variant)) || (0 > variant)) {\n      throw new TypeError('variant must be a (non-negative) integer');\n    }\n    if (('string' === typeof layout)\n        && (undefined === property)) {\n      property = layout;\n      layout = null;\n    }\n    if (layout) {\n      if (!(layout instanceof Layout)) {\n        throw new TypeError('layout must be a Layout');\n      }\n      if ((null !== union.defaultLayout)\n          && (0 <= layout.span)\n          && (layout.span > union.defaultLayout.span)) {\n        throw new Error('variant span exceeds span of containing union');\n      }\n      if ('string' !== typeof property) {\n        throw new TypeError('variant must have a String property');\n      }\n    }\n    let span = union.span;\n    if (0 > union.span) {\n      span = layout ? layout.span : 0;\n      if ((0 <= span) && union.usesPrefixDiscriminator) {\n        span += union.discriminator.layout.span;\n      }\n    }\n    super(span, property);\n\n    /** The {@link Union} to which this variant belongs. */\n    this.union = union;\n\n    /** The unsigned integral value identifying this variant within\n     * the {@link Union#discriminator|discriminator} of the containing\n     * union. */\n    this.variant = variant;\n\n    /** The {@link Layout} to be used when reading/writing the\n     * non-discriminator part of the {@link\n     * VariantLayout#union|union}.  If `null` the variant carries no\n     * data. */\n    this.layout = layout || null;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      /* Will be equal to the containing union span if that is not\n       * variable. */\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    /* Span is defined solely by the variant (and prefix discriminator) */\n    return contentOffset + this.layout.getSpan(b, offset + contentOffset);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    const dest = this.makeDestinationObject();\n    if (undefined === offset) {\n      offset = 0;\n    }\n    if (this !== this.union.getVariant(b, offset)) {\n      throw new Error('variant mismatch');\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    if (this.layout) {\n      dest[this.property] = this.layout.decode(b, offset + contentOffset);\n    } else if (this.property) {\n      dest[this.property] = true;\n    } else if (this.union.usesPrefixDiscriminator) {\n      dest[this.union.discriminator.property] = this.variant;\n    }\n    return dest;\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    if (this.layout\n        && (!src.hasOwnProperty(this.property))) {\n      throw new TypeError('variant lacks property ' + this.property);\n    }\n    this.union.discriminator.encode(this.variant, b, offset);\n    let span = contentOffset;\n    if (this.layout) {\n      this.layout.encode(src[this.property], b, offset + contentOffset);\n      span += this.layout.getSpan(b, offset + contentOffset);\n      if ((0 <= this.union.span)\n          && (span > this.union.span)) {\n        throw new Error('encoded variant overruns containing union');\n      }\n    }\n    return span;\n  }\n\n  /** Delegate {@link Layout#fromArray|fromArray} to {@link\n   * VariantLayout#layout|layout}. */\n  fromArray(values) {\n    if (this.layout) {\n      return this.layout.fromArray(values);\n    }\n  }\n}\n\n/** JavaScript chose to define bitwise operations as operating on\n * signed 32-bit values in 2's complement form, meaning any integer\n * with bit 31 set is going to look negative.  For right shifts that's\n * not a problem, because `>>>` is a logical shift, but for every\n * other bitwise operator we have to compensate for possible negative\n * results. */\nfunction fixBitwiseResult(v) {\n  if (0 > v) {\n    v += 0x100000000;\n  }\n  return v;\n}\n\n/**\n * Contain a sequence of bit fields as an unsigned integer.\n *\n * *Factory*: {@link module:Layout.bits|bits}\n *\n * This is a container element; within it there are {@link BitField}\n * instances that provide the extracted properties.  The container\n * simply defines the aggregate representation and its bit ordering.\n * The representation is an object containing properties with numeric\n * or {@link Boolean} values.\n *\n * {@link BitField}s are added with the {@link\n * BitStructure#addField|addField} and {@link\n * BitStructure#addBoolean|addBoolean} methods.\n\n * @param {Layout} word - initializer for {@link\n * BitStructure#word|word}.  The parameter must be an instance of\n * {@link UInt} (or {@link UIntBE}) that is no more than 4 bytes wide.\n *\n * @param {bool} [msb] - `true` if the bit numbering starts at the\n * most significant bit of the containing word; `false` (default) if\n * it starts at the least significant bit of the containing word.  If\n * the parameter at this position is a string and `property` is\n * `undefined` the value of this argument will instead be used as the\n * value of `property`.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass BitStructure extends Layout {\n  constructor(word, msb, property) {\n    if (!((word instanceof UInt)\n          || (word instanceof UIntBE))) {\n      throw new TypeError('word must be a UInt or UIntBE layout');\n    }\n    if (('string' === typeof msb)\n        && (undefined === property)) {\n      property = msb;\n      msb = undefined;\n    }\n    if (4 < word.span) {\n      throw new RangeError('word cannot exceed 32 bits');\n    }\n    super(word.span, property);\n\n    /** The layout used for the packed value.  {@link BitField}\n     * instances are packed sequentially depending on {@link\n     * BitStructure#msb|msb}. */\n    this.word = word;\n\n    /** Whether the bit sequences are packed starting at the most\n     * significant bit growing down (`true`), or the least significant\n     * bit growing up (`false`).\n     *\n     * **NOTE** Regardless of this value, the least significant bit of\n     * any {@link BitField} value is the least significant bit of the\n     * corresponding section of the packed value. */\n    this.msb = !!msb;\n\n    /** The sequence of {@link BitField} layouts that comprise the\n     * packed structure.\n     *\n     * **NOTE** The array remains mutable to allow fields to be {@link\n     * BitStructure#addField|added} after construction.  Users should\n     * not manipulate the content of this property.*/\n    this.fields = [];\n\n    /* Storage for the value.  Capture a variable instead of using an\n     * instance property because we don't want anything to change the\n     * value without going through the mutator. */\n    let value = 0;\n    this._packedSetValue = function(v) {\n      value = fixBitwiseResult(v);\n      return this;\n    };\n    this._packedGetValue = function() {\n      return value;\n    };\n  }\n\n  /** @override */\n  decode(b, offset) {\n    const dest = this.makeDestinationObject();\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const value = this.word.decode(b, offset);\n    this._packedSetValue(value);\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        dest[fd.property] = fd.decode(value);\n      }\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link BitStructure}.\n   *\n   * If `src` is missing a property for a member with a defined {@link\n   * Layout#property|property} the corresponding region of the packed\n   * value is left unmodified.  Unused bits are also left unmodified. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const value = this.word.decode(b, offset);\n    this._packedSetValue(value);\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        const fv = src[fd.property];\n        if (undefined !== fv) {\n          fd.encode(fv);\n        }\n      }\n    }\n    return this.word.encode(this._packedGetValue(), b, offset);\n  }\n\n  /** Register a new bitfield with a containing bit structure.  The\n   * resulting bitfield is returned.\n   *\n   * @param {Number} bits - initializer for {@link BitField#bits|bits}.\n   *\n   * @param {string} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {BitField} */\n  addField(bits, property) {\n    const bf = new BitField(this, bits, property);\n    this.fields.push(bf);\n    return bf;\n  }\n\n  /** As with {@link BitStructure#addField|addField} for single-bit\n   * fields with `boolean` value representation.\n   *\n   * @param {string} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {Boolean} */\n  addBoolean(property) {\n    // This is my Boolean, not the Javascript one.\n    // eslint-disable-next-line no-new-wrappers\n    const bf = new Boolean(this, property);\n    this.fields.push(bf);\n    return bf;\n  }\n\n  /**\n   * Get access to the bit field for a given property.\n   *\n   * @param {String} property - the bit field of interest.\n   *\n   * @return {BitField} - the field associated with `property`, or\n   * undefined if there is no such property.\n   */\n  fieldFor(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return fd;\n      }\n    }\n  }\n}\n\n/**\n * Represent a sequence of bits within a {@link BitStructure}.\n *\n * All bit field values are represented as unsigned integers.\n *\n * **NOTE** User code should not invoke this constructor directly.\n * Use the container {@link BitStructure#addField|addField} helper\n * method.\n *\n * **NOTE** BitField instances are not instances of {@link Layout}\n * since {@link Layout#span|span} measures 8-bit units.\n *\n * @param {BitStructure} container - initializer for {@link\n * BitField#container|container}.\n *\n * @param {Number} bits - initializer for {@link BitField#bits|bits}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n */\nclass BitField {\n  constructor(container, bits, property) {\n    if (!(container instanceof BitStructure)) {\n      throw new TypeError('container must be a BitStructure');\n    }\n    if ((!Number.isInteger(bits)) || (0 >= bits)) {\n      throw new TypeError('bits must be positive integer');\n    }\n    const totalBits = 8 * container.span;\n    const usedBits = container.fields.reduce((sum, fd) => sum + fd.bits, 0);\n    if ((bits + usedBits) > totalBits) {\n      throw new Error('bits too long for span remainder ('\n                      + (totalBits - usedBits) + ' of '\n                      + totalBits + ' remain)');\n    }\n\n    /** The {@link BitStructure} instance to which this bit field\n     * belongs. */\n    this.container = container;\n\n    /** The span of this value in bits. */\n    this.bits = bits;\n\n    /** A mask of {@link BitField#bits|bits} bits isolating value bits\n     * that fit within the field.\n     *\n     * That is, it masks a value that has not yet been shifted into\n     * position within its containing packed integer. */\n    this.valueMask = (1 << bits) - 1;\n    if (32 === bits) { // shifted value out of range\n      this.valueMask = 0xFFFFFFFF;\n    }\n\n    /** The offset of the value within the containing packed unsigned\n     * integer.  The least significant bit of the packed value is at\n     * offset zero, regardless of bit ordering used. */\n    this.start = usedBits;\n    if (this.container.msb) {\n      this.start = totalBits - usedBits - bits;\n    }\n\n    /** A mask of {@link BitField#bits|bits} isolating the field value\n     * within the containing packed unsigned integer. */\n    this.wordMask = fixBitwiseResult(this.valueMask << this.start);\n\n    /** The property name used when this bitfield is represented in an\n     * Object.\n     *\n     * Intended to be functionally equivalent to {@link\n     * Layout#property}.\n     *\n     * If left undefined the corresponding span of bits will be\n     * treated as padding: it will not be mutated by {@link\n     * Layout#encode|encode} nor represented as a property in the\n     * decoded Object. */\n    this.property = property;\n  }\n\n  /** Store a value into the corresponding subsequence of the containing\n   * bit field. */\n  decode() {\n    const word = this.container._packedGetValue();\n    const wordValue = fixBitwiseResult(word & this.wordMask);\n    const value = wordValue >>> this.start;\n    return value;\n  }\n\n  /** Store a value into the corresponding subsequence of the containing\n   * bit field.\n   *\n   * **NOTE** This is not a specialization of {@link\n   * Layout#encode|Layout.encode} and there is no return value. */\n  encode(value) {\n    if ((!Number.isInteger(value))\n        || (value !== fixBitwiseResult(value & this.valueMask))) {\n      throw new TypeError(nameWithProperty('BitField.encode', this)\n                          + ' value must be integer not exceeding ' + this.valueMask);\n    }\n    const word = this.container._packedGetValue();\n    const wordValue = fixBitwiseResult(value << this.start);\n    this.container._packedSetValue(fixBitwiseResult(word & ~this.wordMask)\n                                   | wordValue);\n  };\n}\n\n/**\n * Represent a single bit within a {@link BitStructure} as a\n * JavaScript boolean.\n *\n * **NOTE** User code should not invoke this constructor directly.\n * Use the container {@link BitStructure#addBoolean|addBoolean} helper\n * method.\n *\n * @param {BitStructure} container - initializer for {@link\n * BitField#container|container}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {BitField}\n */\n/* eslint-disable no-extend-native */\nclass Boolean extends BitField {\n  constructor(container, property) {\n    super(container, 1, property);\n  }\n\n  /** Override {@link BitField#decode|decode} for {@link Boolean|Boolean}.\n   *\n   * @returns {boolean} */\n  decode(b, offset) {\n    return !!BitField.prototype.decode.call(this, b, offset);\n  }\n\n  /** @override */\n  encode(value) {\n    if ('boolean' === typeof value) {\n      // BitField requires integer values\n      value = +value;\n    }\n    return BitField.prototype.encode.call(this, value);\n  }\n}\n/* eslint-enable no-extend-native */\n\n/**\n * Contain a fixed-length block of arbitrary data, represented as a\n * Buffer.\n *\n * *Factory*: {@link module:Layout.blob|blob}\n *\n * @param {(Number|ExternalLayout)} length - initializes {@link\n * Blob#length|length}.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Blob extends Layout {\n  constructor(length, property) {\n    if (!(((length instanceof ExternalLayout) && length.isCount())\n          || (Number.isInteger(length) && (0 <= length)))) {\n      throw new TypeError('length must be positive integer '\n                          + 'or an unsigned integer ExternalLayout');\n    }\n\n    let span = -1;\n    if (!(length instanceof ExternalLayout)) {\n      span = length;\n    }\n    super(span, property);\n\n    /** The number of bytes in the blob.\n     *\n     * This may be a non-negative integer, or an instance of {@link\n     * ExternalLayout} that satisfies {@link\n     * ExternalLayout#isCount|isCount()}. */\n    this.length = length;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    let span = this.span;\n    if (0 > span) {\n      span = this.length.decode(b, offset);\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.span;\n    if (0 > span) {\n      span = this.length.decode(b, offset);\n    }\n    return b.slice(offset, offset + span);\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Blob}.\n   *\n   * **NOTE** If {@link Layout#count|count} is an instance of {@link\n   * ExternalLayout} then the length of `src` will be encoded as the\n   * count after `src` is encoded. */\n  encode(src, b, offset) {\n    let span = this.length;\n    if (this.length instanceof ExternalLayout) {\n      span = src.length;\n    }\n    if (!(Buffer.isBuffer(src)\n          && (span === src.length))) {\n      throw new TypeError(nameWithProperty('Blob.encode', this)\n                          + ' requires (length ' + span + ') Buffer as src');\n    }\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    b.write(src.toString('hex'), offset, span, 'hex');\n    if (this.length instanceof ExternalLayout) {\n      this.length.encode(span, b, offset);\n    }\n    return span;\n  }\n}\n\n/**\n * Contain a `NUL`-terminated UTF8 string.\n *\n * *Factory*: {@link module:Layout.cstr|cstr}\n *\n * **NOTE** Any UTF8 string that incorporates a zero-valued byte will\n * not be correctly decoded by this layout.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass CString extends Layout {\n  constructor(property) {\n    super(-1, property);\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (!Buffer.isBuffer(b)) {\n      throw new TypeError('b must be a Buffer');\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let idx = offset;\n    while ((idx < b.length) && (0 !== b[idx])) {\n      idx += 1;\n    }\n    return 1 + idx - offset;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.getSpan(b, offset);\n    return b.slice(offset, offset + span - 1).toString('utf-8');\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Must force this to a string, lest it be a number and the\n     * \"utf8-encoding\" below actually allocate a buffer of length\n     * src */\n    if ('string' !== typeof src) {\n      src = src.toString();\n    }\n    const srcb = new Buffer(src, 'utf8');\n    const span = srcb.length;\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    srcb.copy(b, offset);\n    b[offset + span] = 0;\n    return span + 1;\n  }\n}\n\n/**\n * Contain a UTF8 string with implicit length.\n *\n * *Factory*: {@link module:Layout.utf8|utf8}\n *\n * **NOTE** Because the length is implicit in the size of the buffer\n * this layout should be used only in isolation, or in a situation\n * where the length can be expressed by operating on a slice of the\n * containing buffer.\n *\n * @param {Number} [maxSpan] - the maximum length allowed for encoded\n * string content.  If not provided there is no bound on the allowed\n * content.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UTF8 extends Layout {\n  constructor(maxSpan, property) {\n    if (('string' === typeof maxSpan)\n        && (undefined === property)) {\n      property = maxSpan;\n      maxSpan = undefined;\n    }\n    if (undefined === maxSpan) {\n      maxSpan = -1;\n    } else if (!Number.isInteger(maxSpan)) {\n      throw new TypeError('maxSpan must be an integer');\n    }\n\n    super(-1, property);\n\n    /** The maximum span of the layout in bytes.\n     *\n     * Positive values are generally expected.  Zero is abnormal.\n     * Attempts to encode or decode a value that exceeds this length\n     * will throw a `RangeError`.\n     *\n     * A negative value indicates that there is no bound on the length\n     * of the content. */\n    this.maxSpan = maxSpan;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (!Buffer.isBuffer(b)) {\n      throw new TypeError('b must be a Buffer');\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.length - offset;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.getSpan(b, offset);\n    if ((0 <= this.maxSpan)\n        && (this.maxSpan < span)) {\n      throw new RangeError('text length exceeds maxSpan');\n    }\n    return b.slice(offset, offset + span).toString('utf-8');\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Must force this to a string, lest it be a number and the\n     * \"utf8-encoding\" below actually allocate a buffer of length\n     * src */\n    if ('string' !== typeof src) {\n      src = src.toString();\n    }\n    const srcb = new Buffer(src, 'utf8');\n    const span = srcb.length;\n    if ((0 <= this.maxSpan)\n        && (this.maxSpan < span)) {\n      throw new RangeError('text length exceeds maxSpan');\n    }\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    srcb.copy(b, offset);\n    return span;\n  }\n}\n\n/**\n * Contain a constant value.\n *\n * This layout may be used in cases where a JavaScript value can be\n * inferred without an expression in the binary encoding.  An example\n * would be a {@link VariantLayout|variant layout} where the content\n * is implied by the union {@link Union#discriminator|discriminator}.\n *\n * @param {Object|Number|String} value - initializer for {@link\n * Constant#value|value}.  If the value is an object (or array) and\n * the application intends the object to remain unchanged regardless\n * of what is done to values decoded by this layout, the value should\n * be frozen prior passing it to this constructor.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Constant extends Layout {\n  constructor(value, property) {\n    super(0, property);\n\n    /** The value produced by this constant when the layout is {@link\n     * Constant#decode|decoded}.\n     *\n     * Any JavaScript value including `null` and `undefined` is\n     * permitted.\n     *\n     * **WARNING** If `value` passed in the constructor was not\n     * frozen, it is possible for users of decoded values to change\n     * the content of the value. */\n    this.value = value;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    return this.value;\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    /* Constants take no space */\n    return 0;\n  }\n}\n\nexports.ExternalLayout = ExternalLayout;\nexports.GreedyCount = GreedyCount;\nexports.OffsetLayout = OffsetLayout;\nexports.UInt = UInt;\nexports.UIntBE = UIntBE;\nexports.Int = Int;\nexports.IntBE = IntBE;\nexports.Float = Float;\nexports.FloatBE = FloatBE;\nexports.Double = Double;\nexports.DoubleBE = DoubleBE;\nexports.Sequence = Sequence;\nexports.Structure = Structure;\nexports.UnionDiscriminator = UnionDiscriminator;\nexports.UnionLayoutDiscriminator = UnionLayoutDiscriminator;\nexports.Union = Union;\nexports.VariantLayout = VariantLayout;\nexports.BitStructure = BitStructure;\nexports.BitField = BitField;\nexports.Boolean = Boolean;\nexports.Blob = Blob;\nexports.CString = CString;\nexports.UTF8 = UTF8;\nexports.Constant = Constant;\n\n/** Factory for {@link GreedyCount}. */\nexports.greedy = ((elementSpan, property) => new GreedyCount(elementSpan, property));\n\n/** Factory for {@link OffsetLayout}. */\nexports.offset = ((layout, offset, property) => new OffsetLayout(layout, offset, property));\n\n/** Factory for {@link UInt|unsigned int layouts} spanning one\n * byte. */\nexports.u8 = (property => new UInt(1, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning two bytes. */\nexports.u16 = (property => new UInt(2, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning three bytes. */\nexports.u24 = (property => new UInt(3, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning four bytes. */\nexports.u32 = (property => new UInt(4, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning five bytes. */\nexports.u40 = (property => new UInt(5, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning six bytes. */\nexports.u48 = (property => new UInt(6, property));\n\n/** Factory for {@link NearUInt64|little-endian unsigned int\n * layouts} interpreted as Numbers. */\nexports.nu64 = (property => new NearUInt64(property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning two bytes. */\nexports.u16be = (property => new UIntBE(2, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning three bytes. */\nexports.u24be = (property => new UIntBE(3, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning four bytes. */\nexports.u32be = (property => new UIntBE(4, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning five bytes. */\nexports.u40be = (property => new UIntBE(5, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning six bytes. */\nexports.u48be = (property => new UIntBE(6, property));\n\n/** Factory for {@link NearUInt64BE|big-endian unsigned int\n * layouts} interpreted as Numbers. */\nexports.nu64be = (property => new NearUInt64BE(property));\n\n/** Factory for {@link Int|signed int layouts} spanning one\n * byte. */\nexports.s8 = (property => new Int(1, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning two bytes. */\nexports.s16 = (property => new Int(2, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning three bytes. */\nexports.s24 = (property => new Int(3, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning four bytes. */\nexports.s32 = (property => new Int(4, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning five bytes. */\nexports.s40 = (property => new Int(5, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning six bytes. */\nexports.s48 = (property => new Int(6, property));\n\n/** Factory for {@link NearInt64|little-endian signed int layouts}\n * interpreted as Numbers. */\nexports.ns64 = (property => new NearInt64(property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning two bytes. */\nexports.s16be = (property => new IntBE(2, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning three bytes. */\nexports.s24be = (property => new IntBE(3, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning four bytes. */\nexports.s32be = (property => new IntBE(4, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning five bytes. */\nexports.s40be = (property => new IntBE(5, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning six bytes. */\nexports.s48be = (property => new IntBE(6, property));\n\n/** Factory for {@link NearInt64BE|big-endian signed int layouts}\n * interpreted as Numbers. */\nexports.ns64be = (property => new NearInt64BE(property));\n\n/** Factory for {@link Float|little-endian 32-bit floating point} values. */\nexports.f32 = (property => new Float(property));\n\n/** Factory for {@link FloatBE|big-endian 32-bit floating point} values. */\nexports.f32be = (property => new FloatBE(property));\n\n/** Factory for {@link Double|little-endian 64-bit floating point} values. */\nexports.f64 = (property => new Double(property));\n\n/** Factory for {@link DoubleBE|big-endian 64-bit floating point} values. */\nexports.f64be = (property => new DoubleBE(property));\n\n/** Factory for {@link Structure} values. */\nexports.struct = ((fields, property, decodePrefixes) => new Structure(fields, property, decodePrefixes));\n\n/** Factory for {@link BitStructure} values. */\nexports.bits = ((word, msb, property) => new BitStructure(word, msb, property));\n\n/** Factory for {@link Sequence} values. */\nexports.seq = ((elementLayout, count, property) => new Sequence(elementLayout, count, property));\n\n/** Factory for {@link Union} values. */\nexports.union = ((discr, defaultLayout, property) => new Union(discr, defaultLayout, property));\n\n/** Factory for {@link UnionLayoutDiscriminator} values. */\nexports.unionLayoutDiscriminator = ((layout, property) => new UnionLayoutDiscriminator(layout, property));\n\n/** Factory for {@link Blob} values. */\nexports.blob = ((length, property) => new Blob(length, property));\n\n/** Factory for {@link CString} values. */\nexports.cstr = (property => new CString(property));\n\n/** Factory for {@link UTF8} values. */\nexports.utf8 = ((maxSpan, property) => new UTF8(maxSpan, property));\n\n/** Factory for {@link Constant} values. */\nexports.const = ((value, property) => new Constant(value, property));\n", "// @flow\n\nimport * as BufferLayout from 'buffer-layout';\n\n/**\n * Layout for a public key\n */\nexport const publicKey = (property: string = 'publicKey'): Object => {\n  return BufferLayout.blob(32, property);\n};\n\n/**\n * Layout for a 64bit unsigned value\n */\nexport const uint64 = (property: string = 'uint64'): Object => {\n  return BufferLayout.blob(8, property);\n};\n", "// @flow\n\nimport {sendAndConfirmTransaction as realSendAndConfirmTransaction} from '@solana/web3.js';\nimport type {\n  Connection,\n  Signer,\n  Transaction,\n  TransactionSignature,\n} from '@solana/web3.js';\n\nexport function sendAndConfirmTransaction(\n  title: string,\n  connection: Connection,\n  transaction: Transaction,\n  ...signers: Array<Signer>\n): Promise<TransactionSignature> {\n  return realSendAndConfirmTransaction(connection, transaction, signers, {\n    skipPreflight: false,\n  });\n}\n", "/**\n * @flow\n */\n\nimport {Buffer} from 'buffer';\nimport assert from 'assert';\nimport BN from 'bn.js';\nimport * as BufferLayout from 'buffer-layout';\nimport {\n  Keypair,\n  PublicKey,\n  SystemProgram,\n  Transaction,\n  TransactionInstruction,\n  SYSVAR_RENT_PUBKEY,\n} from '@solana/web3.js';\nimport type {\n  Connection,\n  Commitment,\n  Signer,\n  TransactionSignature,\n} from '@solana/web3.js';\n\nimport * as Layout from './layout';\nimport {sendAndConfirmTransaction} from './util/send-and-confirm-transaction';\n\nexport const TOKEN_PROGRAM_ID: PublicKey = new PublicKey(\n  'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',\n);\n\nexport const ASSOCIATED_TOKEN_PROGRAM_ID: PublicKey = new PublicKey(\n  'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',\n);\n\nconst FAILED_TO_FIND_ACCOUNT = 'Failed to find account';\nconst INVALID_ACCOUNT_OWNER = 'Invalid account owner';\n\n/**\n * Unfortunately, BufferLayout.encode uses an `instanceof` check for `Buffer`\n * which fails when using `publicKey.toBuffer()` directly because the bundled `Buffer`\n * class in `@solana/web3.js` is different from the bundled `Buffer` class in this package\n */\nfunction pubkeyToBuffer(publicKey: PublicKey): typeof Buffer {\n  return Buffer.from(publicKey.toBuffer());\n}\n\n/**\n * 64-bit value\n */\nexport class u64 extends BN {\n  /**\n   * Convert to Buffer representation\n   */\n  toBuffer(): typeof Buffer {\n    const a = super.toArray().reverse();\n    const b = Buffer.from(a);\n    if (b.length === 8) {\n      return b;\n    }\n    assert(b.length < 8, 'u64 too large');\n\n    const zeroPad = Buffer.alloc(8);\n    b.copy(zeroPad);\n    return zeroPad;\n  }\n\n  /**\n   * Construct a u64 from Buffer representation\n   */\n  static fromBuffer(buffer: typeof Buffer): u64 {\n    assert(buffer.length === 8, `Invalid buffer length: ${buffer.length}`);\n    return new u64(\n      [...buffer]\n        .reverse()\n        .map(i => `00${i.toString(16)}`.slice(-2))\n        .join(''),\n      16,\n    );\n  }\n}\n\nfunction isAccount(accountOrPublicKey: any): boolean {\n  return 'publicKey' in accountOrPublicKey;\n}\n\ntype AuthorityType =\n  | 'MintTokens'\n  | 'FreezeAccount'\n  | 'AccountOwner'\n  | 'CloseAccount';\n\nconst AuthorityTypeCodes = {\n  MintTokens: 0,\n  FreezeAccount: 1,\n  AccountOwner: 2,\n  CloseAccount: 3,\n};\n\n// The address of the special mint for wrapped native token.\nexport const NATIVE_MINT: PublicKey = new PublicKey(\n  'So11111111111111111111111111111111111111112',\n);\n\n/**\n * Information about the mint\n */\ntype MintInfo = {|\n  /**\n   * Optional authority used to mint new tokens. The mint authority may only be provided during\n   * mint creation. If no mint authority is present then the mint has a fixed supply and no\n   * further tokens may be minted.\n   */\n  mintAuthority: null | PublicKey,\n\n  /**\n   * Total supply of tokens\n   */\n  supply: u64,\n\n  /**\n   * Number of base 10 digits to the right of the decimal place\n   */\n  decimals: number,\n\n  /**\n   * Is this mint initialized\n   */\n  isInitialized: boolean,\n\n  /**\n   * Optional authority to freeze token accounts\n   */\n  freezeAuthority: null | PublicKey,\n|};\n\nexport const MintLayout: typeof BufferLayout.Structure = BufferLayout.struct([\n  BufferLayout.u32('mintAuthorityOption'),\n  Layout.publicKey('mintAuthority'),\n  Layout.uint64('supply'),\n  BufferLayout.u8('decimals'),\n  BufferLayout.u8('isInitialized'),\n  BufferLayout.u32('freezeAuthorityOption'),\n  Layout.publicKey('freezeAuthority'),\n]);\n\n/**\n * Information about an account\n */\ntype AccountInfo = {|\n  /**\n   * The address of this account\n   */\n  address: PublicKey,\n\n  /**\n   * The mint associated with this account\n   */\n  mint: PublicKey,\n\n  /**\n   * Owner of this account\n   */\n  owner: PublicKey,\n\n  /**\n   * Amount of tokens this account holds\n   */\n  amount: u64,\n\n  /**\n   * The delegate for this account\n   */\n  delegate: null | PublicKey,\n\n  /**\n   * The amount of tokens the delegate authorized to the delegate\n   */\n  delegatedAmount: u64,\n\n  /**\n   * Is this account initialized\n   */\n  isInitialized: boolean,\n\n  /**\n   * Is this account frozen\n   */\n  isFrozen: boolean,\n\n  /**\n   * Is this a native token account\n   */\n  isNative: boolean,\n\n  /**\n   * If this account is a native token, it must be rent-exempt. This\n   * value logs the rent-exempt reserve which must remain in the balance\n   * until the account is closed.\n   */\n  rentExemptReserve: null | u64,\n\n  /**\n   * Optional authority to close the account\n   */\n  closeAuthority: null | PublicKey,\n|};\n\n/**\n * @private\n */\nexport const AccountLayout: typeof BufferLayout.Structure = BufferLayout.struct(\n  [\n    Layout.publicKey('mint'),\n    Layout.publicKey('owner'),\n    Layout.uint64('amount'),\n    BufferLayout.u32('delegateOption'),\n    Layout.publicKey('delegate'),\n    BufferLayout.u8('state'),\n    BufferLayout.u32('isNativeOption'),\n    Layout.uint64('isNative'),\n    Layout.uint64('delegatedAmount'),\n    BufferLayout.u32('closeAuthorityOption'),\n    Layout.publicKey('closeAuthority'),\n  ],\n);\n\n/**\n * Information about an multisig\n */\ntype MultisigInfo = {|\n  /**\n   * The number of signers required\n   */\n  m: number,\n\n  /**\n   * Number of possible signers, corresponds to the\n   * number of `signers` that are valid.\n   */\n  n: number,\n\n  /**\n   * Is this mint initialized\n   */\n  initialized: boolean,\n\n  /**\n   * The signers\n   */\n  signer1: PublicKey,\n  signer2: PublicKey,\n  signer3: PublicKey,\n  signer4: PublicKey,\n  signer5: PublicKey,\n  signer6: PublicKey,\n  signer7: PublicKey,\n  signer8: PublicKey,\n  signer9: PublicKey,\n  signer10: PublicKey,\n  signer11: PublicKey,\n|};\n\n/**\n * @private\n */\nconst MultisigLayout = BufferLayout.struct([\n  BufferLayout.u8('m'),\n  BufferLayout.u8('n'),\n  BufferLayout.u8('is_initialized'),\n  Layout.publicKey('signer1'),\n  Layout.publicKey('signer2'),\n  Layout.publicKey('signer3'),\n  Layout.publicKey('signer4'),\n  Layout.publicKey('signer5'),\n  Layout.publicKey('signer6'),\n  Layout.publicKey('signer7'),\n  Layout.publicKey('signer8'),\n  Layout.publicKey('signer9'),\n  Layout.publicKey('signer10'),\n  Layout.publicKey('signer11'),\n]);\n\n/**\n * An ERC20-like Token\n */\nexport class Token {\n  /**\n   * @private\n   */\n  connection: Connection;\n\n  /**\n   * The public key identifying this mint\n   */\n  publicKey: PublicKey;\n\n  /**\n   * Program Identifier for the Token program\n   */\n  programId: PublicKey;\n\n  /**\n   * Program Identifier for the Associated Token program\n   */\n  associatedProgramId: PublicKey;\n\n  /**\n   * Fee payer\n   */\n  payer: Signer;\n\n  /**\n   * Create a Token object attached to the specific mint\n   *\n   * @param connection The connection to use\n   * @param token Public key of the mint\n   * @param programId token programId\n   * @param payer Payer of fees\n   */\n  constructor(\n    connection: Connection,\n    publicKey: PublicKey,\n    programId: PublicKey,\n    payer: Signer,\n  ) {\n    Object.assign(this, {\n      connection,\n      publicKey,\n      programId,\n      payer,\n      // Hard code is ok; Overriding is needed only for tests\n      associatedProgramId: ASSOCIATED_TOKEN_PROGRAM_ID,\n    });\n  }\n\n  /**\n   * Get the minimum balance for the mint to be rent exempt\n   *\n   * @return Number of lamports required\n   */\n  static async getMinBalanceRentForExemptMint(\n    connection: Connection,\n  ): Promise<number> {\n    return await connection.getMinimumBalanceForRentExemption(MintLayout.span);\n  }\n\n  /**\n   * Get the minimum balance for the account to be rent exempt\n   *\n   * @return Number of lamports required\n   */\n  static async getMinBalanceRentForExemptAccount(\n    connection: Connection,\n  ): Promise<number> {\n    return await connection.getMinimumBalanceForRentExemption(\n      AccountLayout.span,\n    );\n  }\n\n  /**\n   * Get the minimum balance for the multsig to be rent exempt\n   *\n   * @return Number of lamports required\n   */\n  static async getMinBalanceRentForExemptMultisig(\n    connection: Connection,\n  ): Promise<number> {\n    return await connection.getMinimumBalanceForRentExemption(\n      MultisigLayout.span,\n    );\n  }\n\n  /**\n   * Create and initialize a token.\n   *\n   * @param connection The connection to use\n   * @param payer Fee payer for transaction\n   * @param mintAuthority Account or multisig that will control minting\n   * @param freezeAuthority Optional account or multisig that can freeze token accounts\n   * @param decimals Location of the decimal place\n   * @param programId Optional token programId, uses the system programId by default\n   * @return Token object for the newly minted token\n   */\n  static async createMint(\n    connection: Connection,\n    payer: Signer,\n    mintAuthority: PublicKey,\n    freezeAuthority: PublicKey | null,\n    decimals: number,\n    programId: PublicKey,\n  ): Promise<Token> {\n    const mintAccount = Keypair.generate();\n    const token = new Token(\n      connection,\n      mintAccount.publicKey,\n      programId,\n      payer,\n    );\n\n    // Allocate memory for the account\n    const balanceNeeded = await Token.getMinBalanceRentForExemptMint(\n      connection,\n    );\n\n    const transaction = new Transaction();\n    transaction.add(\n      SystemProgram.createAccount({\n        fromPubkey: payer.publicKey,\n        newAccountPubkey: mintAccount.publicKey,\n        lamports: balanceNeeded,\n        space: MintLayout.span,\n        programId,\n      }),\n    );\n\n    transaction.add(\n      Token.createInitMintInstruction(\n        programId,\n        mintAccount.publicKey,\n        decimals,\n        mintAuthority,\n        freezeAuthority,\n      ),\n    );\n\n    // Send the two instructions\n    await sendAndConfirmTransaction(\n      'createAccount and InitializeMint',\n      connection,\n      transaction,\n      payer,\n      mintAccount,\n    );\n\n    return token;\n  }\n\n  /**\n   * Create and initialize a new account.\n   *\n   * This account may then be used as a `transfer()` or `approve()` destination\n   *\n   * @param owner User account that will own the new account\n   * @return Public key of the new empty account\n   */\n  async createAccount(owner: PublicKey): Promise<PublicKey> {\n    // Allocate memory for the account\n    const balanceNeeded = await Token.getMinBalanceRentForExemptAccount(\n      this.connection,\n    );\n\n    const newAccount = Keypair.generate();\n    const transaction = new Transaction();\n    transaction.add(\n      SystemProgram.createAccount({\n        fromPubkey: this.payer.publicKey,\n        newAccountPubkey: newAccount.publicKey,\n        lamports: balanceNeeded,\n        space: AccountLayout.span,\n        programId: this.programId,\n      }),\n    );\n\n    const mintPublicKey = this.publicKey;\n    transaction.add(\n      Token.createInitAccountInstruction(\n        this.programId,\n        mintPublicKey,\n        newAccount.publicKey,\n        owner,\n      ),\n    );\n\n    // Send the two instructions\n    await sendAndConfirmTransaction(\n      'createAccount and InitializeAccount',\n      this.connection,\n      transaction,\n      this.payer,\n      newAccount,\n    );\n\n    return newAccount.publicKey;\n  }\n\n  /**\n   * Create and initialize the associated account.\n   *\n   * This account may then be used as a `transfer()` or `approve()` destination\n   *\n   * @param owner User account that will own the new account\n   * @return Public key of the new associated account\n   */\n  async createAssociatedTokenAccount(owner: PublicKey): Promise<PublicKey> {\n    const associatedAddress = await Token.getAssociatedTokenAddress(\n      this.associatedProgramId,\n      this.programId,\n      this.publicKey,\n      owner,\n    );\n\n    return this.createAssociatedTokenAccountInternal(owner, associatedAddress);\n  }\n\n  async createAssociatedTokenAccountInternal(\n    owner: PublicKey,\n    associatedAddress: PublicKey,\n  ): Promise<PublicKey> {\n    await sendAndConfirmTransaction(\n      'CreateAssociatedTokenAccount',\n      this.connection,\n      new Transaction().add(\n        Token.createAssociatedTokenAccountInstruction(\n          this.associatedProgramId,\n          this.programId,\n          this.publicKey,\n          associatedAddress,\n          owner,\n          this.payer.publicKey,\n        ),\n      ),\n      this.payer,\n    );\n\n    return associatedAddress;\n  }\n\n  /**\n   * Retrieve the associated account or create one if not found.\n   *\n   * This account may then be used as a `transfer()` or `approve()` destination\n   *\n   * @param owner User account that will own the new account\n   * @return The new associated account\n   */\n  async getOrCreateAssociatedAccountInfo(\n    owner: PublicKey,\n  ): Promise<AccountInfo> {\n    const associatedAddress = await Token.getAssociatedTokenAddress(\n      this.associatedProgramId,\n      this.programId,\n      this.publicKey,\n      owner,\n    );\n\n    // This is the optimum logic, considering TX fee, client-side computation,\n    // RPC roundtrips and guaranteed idempotent.\n    // Sadly we can't do this atomically;\n    try {\n      return await this.getAccountInfo(associatedAddress);\n    } catch (err) {\n      // INVALID_ACCOUNT_OWNER can be possible if the associatedAddress has\n      // already been received some lamports (= became system accounts).\n      // Assuming program derived addressing is safe, this is the only case\n      // for the INVALID_ACCOUNT_OWNER in this code-path\n      if (\n        err.message === FAILED_TO_FIND_ACCOUNT ||\n        err.message === INVALID_ACCOUNT_OWNER\n      ) {\n        // as this isn't atomic, it's possible others can create associated\n        // accounts meanwhile\n        try {\n          await this.createAssociatedTokenAccountInternal(\n            owner,\n            associatedAddress,\n          );\n        } catch (err) {\n          // ignore all errors; for now there is no API compatible way to\n          // selectively ignore the expected instruction error if the\n          // associated account is existing already.\n        }\n\n        // Now this should always succeed\n        return await this.getAccountInfo(associatedAddress);\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * Create and initialize a new account on the special native token mint.\n   *\n   * In order to be wrapped, the account must have a balance of native tokens\n   * when it is initialized with the token program.\n   *\n   * This function sends lamports to the new account before initializing it.\n   *\n   * @param connection A solana web3 connection\n   * @param programId The token program ID\n   * @param owner The owner of the new token account\n   * @param payer The source of the lamports to initialize, and payer of the initialization fees.\n   * @param amount The amount of lamports to wrap\n   * @return {Promise<PublicKey>} The new token account\n   */\n  static async createWrappedNativeAccount(\n    connection: Connection,\n    programId: PublicKey,\n    owner: PublicKey,\n    payer: Signer,\n    amount: number,\n  ): Promise<PublicKey> {\n    // Allocate memory for the account\n    const balanceNeeded = await Token.getMinBalanceRentForExemptAccount(\n      connection,\n    );\n\n    // Create a new account\n    const newAccount = Keypair.generate();\n    const transaction = new Transaction();\n    transaction.add(\n      SystemProgram.createAccount({\n        fromPubkey: payer.publicKey,\n        newAccountPubkey: newAccount.publicKey,\n        lamports: balanceNeeded,\n        space: AccountLayout.span,\n        programId,\n      }),\n    );\n\n    // Send lamports to it (these will be wrapped into native tokens by the token program)\n    transaction.add(\n      SystemProgram.transfer({\n        fromPubkey: payer.publicKey,\n        toPubkey: newAccount.publicKey,\n        lamports: amount,\n      }),\n    );\n\n    // Assign the new account to the native token mint.\n    // the account will be initialized with a balance equal to the native token balance.\n    // (i.e. amount)\n    transaction.add(\n      Token.createInitAccountInstruction(\n        programId,\n        NATIVE_MINT,\n        newAccount.publicKey,\n        owner,\n      ),\n    );\n\n    // Send the three instructions\n    await sendAndConfirmTransaction(\n      'createAccount, transfer, and initializeAccount',\n      connection,\n      transaction,\n      payer,\n      newAccount,\n    );\n\n    return newAccount.publicKey;\n  }\n\n  /**\n   * Create and initialize a new multisig.\n   *\n   * This account may then be used for multisignature verification\n   *\n   * @param m Number of required signatures\n   * @param signers Full set of signers\n   * @return Public key of the new multisig account\n   */\n  async createMultisig(\n    m: number,\n    signers: Array<PublicKey>,\n  ): Promise<PublicKey> {\n    const multisigAccount = Keypair.generate();\n\n    // Allocate memory for the account\n    const balanceNeeded = await Token.getMinBalanceRentForExemptMultisig(\n      this.connection,\n    );\n    const transaction = new Transaction();\n    transaction.add(\n      SystemProgram.createAccount({\n        fromPubkey: this.payer.publicKey,\n        newAccountPubkey: multisigAccount.publicKey,\n        lamports: balanceNeeded,\n        space: MultisigLayout.span,\n        programId: this.programId,\n      }),\n    );\n\n    // create the new account\n    let keys = [\n      {pubkey: multisigAccount.publicKey, isSigner: false, isWritable: true},\n      {pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false},\n    ];\n    signers.forEach(signer =>\n      keys.push({pubkey: signer, isSigner: false, isWritable: false}),\n    );\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      BufferLayout.u8('m'),\n    ]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 2, // InitializeMultisig instruction\n        m,\n      },\n      data,\n    );\n    transaction.add({\n      keys,\n      programId: this.programId,\n      data,\n    });\n\n    // Send the two instructions\n    await sendAndConfirmTransaction(\n      'createAccount and InitializeMultisig',\n      this.connection,\n      transaction,\n      this.payer,\n      multisigAccount,\n    );\n\n    return multisigAccount.publicKey;\n  }\n\n  /**\n   * Retrieve mint information\n   */\n  async getMintInfo(): Promise<MintInfo> {\n    const info = await this.connection.getAccountInfo(this.publicKey);\n    if (info === null) {\n      throw new Error('Failed to find mint account');\n    }\n    if (!info.owner.equals(this.programId)) {\n      throw new Error(`Invalid mint owner: ${JSON.stringify(info.owner)}`);\n    }\n    if (info.data.length != MintLayout.span) {\n      throw new Error(`Invalid mint size`);\n    }\n\n    const data = Buffer.from(info.data);\n    const mintInfo = MintLayout.decode(data);\n\n    if (mintInfo.mintAuthorityOption === 0) {\n      mintInfo.mintAuthority = null;\n    } else {\n      mintInfo.mintAuthority = new PublicKey(mintInfo.mintAuthority);\n    }\n\n    mintInfo.supply = u64.fromBuffer(mintInfo.supply);\n    mintInfo.isInitialized = mintInfo.isInitialized != 0;\n\n    if (mintInfo.freezeAuthorityOption === 0) {\n      mintInfo.freezeAuthority = null;\n    } else {\n      mintInfo.freezeAuthority = new PublicKey(mintInfo.freezeAuthority);\n    }\n    return mintInfo;\n  }\n\n  /**\n   * Retrieve account information\n   *\n   * @param account Public key of the account\n   */\n  async getAccountInfo(\n    account: PublicKey,\n    commitment?: Commitment,\n  ): Promise<AccountInfo> {\n    const info = await this.connection.getAccountInfo(account, commitment);\n    if (info === null) {\n      throw new Error(FAILED_TO_FIND_ACCOUNT);\n    }\n    if (!info.owner.equals(this.programId)) {\n      throw new Error(INVALID_ACCOUNT_OWNER);\n    }\n    if (info.data.length != AccountLayout.span) {\n      throw new Error(`Invalid account size`);\n    }\n\n    const data = Buffer.from(info.data);\n    const accountInfo = AccountLayout.decode(data);\n    accountInfo.address = account;\n    accountInfo.mint = new PublicKey(accountInfo.mint);\n    accountInfo.owner = new PublicKey(accountInfo.owner);\n    accountInfo.amount = u64.fromBuffer(accountInfo.amount);\n\n    if (accountInfo.delegateOption === 0) {\n      accountInfo.delegate = null;\n      accountInfo.delegatedAmount = new u64();\n    } else {\n      accountInfo.delegate = new PublicKey(accountInfo.delegate);\n      accountInfo.delegatedAmount = u64.fromBuffer(accountInfo.delegatedAmount);\n    }\n\n    accountInfo.isInitialized = accountInfo.state !== 0;\n    accountInfo.isFrozen = accountInfo.state === 2;\n\n    if (accountInfo.isNativeOption === 1) {\n      accountInfo.rentExemptReserve = u64.fromBuffer(accountInfo.isNative);\n      accountInfo.isNative = true;\n    } else {\n      accountInfo.rentExemptReserve = null;\n      accountInfo.isNative = false;\n    }\n\n    if (accountInfo.closeAuthorityOption === 0) {\n      accountInfo.closeAuthority = null;\n    } else {\n      accountInfo.closeAuthority = new PublicKey(accountInfo.closeAuthority);\n    }\n\n    if (!accountInfo.mint.equals(this.publicKey)) {\n      throw new Error(\n        `Invalid account mint: ${JSON.stringify(\n          accountInfo.mint,\n        )} !== ${JSON.stringify(this.publicKey)}`,\n      );\n    }\n    return accountInfo;\n  }\n\n  /**\n   * Retrieve Multisig information\n   *\n   * @param multisig Public key of the account\n   */\n  async getMultisigInfo(multisig: PublicKey): Promise<MultisigInfo> {\n    const info = await this.connection.getAccountInfo(multisig);\n    if (info === null) {\n      throw new Error('Failed to find multisig');\n    }\n    if (!info.owner.equals(this.programId)) {\n      throw new Error(`Invalid multisig owner`);\n    }\n    if (info.data.length != MultisigLayout.span) {\n      throw new Error(`Invalid multisig size`);\n    }\n\n    const data = Buffer.from(info.data);\n    const multisigInfo = MultisigLayout.decode(data);\n    multisigInfo.signer1 = new PublicKey(multisigInfo.signer1);\n    multisigInfo.signer2 = new PublicKey(multisigInfo.signer2);\n    multisigInfo.signer3 = new PublicKey(multisigInfo.signer3);\n    multisigInfo.signer4 = new PublicKey(multisigInfo.signer4);\n    multisigInfo.signer5 = new PublicKey(multisigInfo.signer5);\n    multisigInfo.signer6 = new PublicKey(multisigInfo.signer6);\n    multisigInfo.signer7 = new PublicKey(multisigInfo.signer7);\n    multisigInfo.signer8 = new PublicKey(multisigInfo.signer8);\n    multisigInfo.signer9 = new PublicKey(multisigInfo.signer9);\n    multisigInfo.signer10 = new PublicKey(multisigInfo.signer10);\n    multisigInfo.signer11 = new PublicKey(multisigInfo.signer11);\n\n    return multisigInfo;\n  }\n\n  /**\n   * Transfer tokens to another account\n   *\n   * @param source Source account\n   * @param destination Destination account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Number of tokens to transfer\n   */\n  async transfer(\n    source: PublicKey,\n    destination: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): Promise<TransactionSignature> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    return await sendAndConfirmTransaction(\n      'Transfer',\n      this.connection,\n      new Transaction().add(\n        Token.createTransferInstruction(\n          this.programId,\n          source,\n          destination,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Grant a third-party permission to transfer up the specified number of tokens from an account\n   *\n   * @param account Public key of the account\n   * @param delegate Account authorized to perform a transfer tokens from the source account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Maximum number of tokens the delegate may transfer\n   */\n  async approve(\n    account: PublicKey,\n    delegate: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'Approve',\n      this.connection,\n      new Transaction().add(\n        Token.createApproveInstruction(\n          this.programId,\n          account,\n          delegate,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Remove approval for the transfer of any remaining tokens\n   *\n   * @param account Public key of the account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  async revoke(\n    account: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'Revoke',\n      this.connection,\n      new Transaction().add(\n        Token.createRevokeInstruction(\n          this.programId,\n          account,\n          ownerPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Assign a new authority to the account\n   *\n   * @param account Public key of the account\n   * @param newAuthority New authority of the account\n   * @param authorityType Type of authority to set\n   * @param currentAuthority Current authority of the account\n   * @param multiSigners Signing accounts if `currentAuthority` is a multiSig\n   */\n  async setAuthority(\n    account: PublicKey,\n    newAuthority: PublicKey | null,\n    authorityType: AuthorityType,\n    currentAuthority: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let currentAuthorityPublicKey: PublicKey;\n    let signers;\n    if (isAccount(currentAuthority)) {\n      currentAuthorityPublicKey = currentAuthority.publicKey;\n      signers = [currentAuthority];\n    } else {\n      currentAuthorityPublicKey = currentAuthority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'SetAuthority',\n      this.connection,\n      new Transaction().add(\n        Token.createSetAuthorityInstruction(\n          this.programId,\n          account,\n          newAuthority,\n          authorityType,\n          currentAuthorityPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Mint new tokens\n   *\n   * @param dest Public key of the account to mint to\n   * @param authority Minting authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Amount to mint\n   */\n  async mintTo(\n    dest: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      ownerPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      ownerPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'MintTo',\n      this.connection,\n      new Transaction().add(\n        Token.createMintToInstruction(\n          this.programId,\n          this.publicKey,\n          dest,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Burn tokens\n   *\n   * @param account Account to burn tokens from\n   * @param owner Account owner\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Amount to burn\n   */\n  async burn(\n    account: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'Burn',\n      this.connection,\n      new Transaction().add(\n        Token.createBurnInstruction(\n          this.programId,\n          this.publicKey,\n          account,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Close account\n   *\n   * @param account Account to close\n   * @param dest Account to receive the remaining balance of the closed account\n   * @param authority Authority which is allowed to close the account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   */\n  async closeAccount(\n    account: PublicKey,\n    dest: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let authorityPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      authorityPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      authorityPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'CloseAccount',\n      this.connection,\n      new Transaction().add(\n        Token.createCloseAccountInstruction(\n          this.programId,\n          account,\n          dest,\n          authorityPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Freeze account\n   *\n   * @param account Account to freeze\n   * @param authority The mint freeze authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   */\n  async freezeAccount(\n    account: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let authorityPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      authorityPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      authorityPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'FreezeAccount',\n      this.connection,\n      new Transaction().add(\n        Token.createFreezeAccountInstruction(\n          this.programId,\n          account,\n          this.publicKey,\n          authorityPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Thaw account\n   *\n   * @param account Account to thaw\n   * @param authority The mint freeze authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   */\n  async thawAccount(\n    account: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n  ): Promise<void> {\n    let authorityPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      authorityPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      authorityPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'ThawAccount',\n      this.connection,\n      new Transaction().add(\n        Token.createThawAccountInstruction(\n          this.programId,\n          account,\n          this.publicKey,\n          authorityPublicKey,\n          multiSigners,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Transfer tokens to another account, asserting the token mint and decimals\n   *\n   * @param source Source account\n   * @param destination Destination account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Number of tokens to transfer\n   * @param decimals Number of decimals in transfer amount\n   */\n  async transferChecked(\n    source: PublicKey,\n    destination: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): Promise<TransactionSignature> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    return await sendAndConfirmTransaction(\n      'TransferChecked',\n      this.connection,\n      new Transaction().add(\n        Token.createTransferCheckedInstruction(\n          this.programId,\n          source,\n          this.publicKey,\n          destination,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n          decimals,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Grant a third-party permission to transfer up the specified number of tokens from an account,\n   * asserting the token mint and decimals\n   *\n   * @param account Public key of the account\n   * @param delegate Account authorized to perform a transfer tokens from the source account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Maximum number of tokens the delegate may transfer\n   * @param decimals Number of decimals in approve amount\n   */\n  async approveChecked(\n    account: PublicKey,\n    delegate: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'ApproveChecked',\n      this.connection,\n      new Transaction().add(\n        Token.createApproveCheckedInstruction(\n          this.programId,\n          account,\n          this.publicKey,\n          delegate,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n          decimals,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Mint new tokens, asserting the token mint and decimals\n   *\n   * @param dest Public key of the account to mint to\n   * @param authority Minting authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Amount to mint\n   * @param decimals Number of decimals in amount to mint\n   */\n  async mintToChecked(\n    dest: PublicKey,\n    authority: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(authority)) {\n      ownerPublicKey = authority.publicKey;\n      signers = [authority];\n    } else {\n      ownerPublicKey = authority;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'MintToChecked',\n      this.connection,\n      new Transaction().add(\n        Token.createMintToCheckedInstruction(\n          this.programId,\n          this.publicKey,\n          dest,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n          decimals,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Burn tokens, asserting the token mint and decimals\n   *\n   * @param account Account to burn tokens from\n   * @param owner Account owner\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Amount to burn\n   * @param decimals Number of decimals in amount to burn\n   */\n  async burnChecked(\n    account: PublicKey,\n    owner: any,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): Promise<void> {\n    let ownerPublicKey;\n    let signers;\n    if (isAccount(owner)) {\n      ownerPublicKey = owner.publicKey;\n      signers = [owner];\n    } else {\n      ownerPublicKey = owner;\n      signers = multiSigners;\n    }\n    await sendAndConfirmTransaction(\n      'BurnChecked',\n      this.connection,\n      new Transaction().add(\n        Token.createBurnCheckedInstruction(\n          this.programId,\n          this.publicKey,\n          account,\n          ownerPublicKey,\n          multiSigners,\n          amount,\n          decimals,\n        ),\n      ),\n      this.payer,\n      ...signers,\n    );\n  }\n\n  /**\n   * Sync amount in native SPL token account to underlying lamports\n   *\n   * @param nativeAccount Account to sync\n   */\n  async syncNative(nativeAccount: PublicKey): Promise<void> {\n    await sendAndConfirmTransaction(\n      'SyncNative',\n      this.connection,\n      new Transaction().add(\n        Token.createSyncNativeInstruction(this.programId, nativeAccount),\n      ),\n      this.payer,\n    );\n  }\n\n  /**\n   * Construct an InitializeMint instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Token mint account\n   * @param decimals Number of decimals in token account amounts\n   * @param mintAuthority Minting authority\n   * @param freezeAuthority Optional authority that can freeze token accounts\n   */\n  static createInitMintInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    decimals: number,\n    mintAuthority: PublicKey,\n    freezeAuthority: PublicKey | null,\n  ): TransactionInstruction {\n    let keys = [\n      {pubkey: mint, isSigner: false, isWritable: true},\n      {pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false},\n    ];\n    const commandDataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      BufferLayout.u8('decimals'),\n      Layout.publicKey('mintAuthority'),\n      BufferLayout.u8('option'),\n      Layout.publicKey('freezeAuthority'),\n    ]);\n    let data = Buffer.alloc(1024);\n    {\n      const encodeLength = commandDataLayout.encode(\n        {\n          instruction: 0, // InitializeMint instruction\n          decimals,\n          mintAuthority: pubkeyToBuffer(mintAuthority),\n          option: freezeAuthority === null ? 0 : 1,\n          freezeAuthority: pubkeyToBuffer(freezeAuthority || new PublicKey(0)),\n        },\n        data,\n      );\n      data = data.slice(0, encodeLength);\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct an InitializeAccount instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Token mint account\n   * @param account New account\n   * @param owner Owner of the new account\n   */\n  static createInitAccountInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    account: PublicKey,\n    owner: PublicKey,\n  ): TransactionInstruction {\n    const keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n      {pubkey: owner, isSigner: false, isWritable: false},\n      {pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false},\n    ];\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 1, // InitializeAccount instruction\n      },\n      data,\n    );\n\n    return new TransactionInstruction({\n      keys,\n      programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Transfer instruction\n   *\n   * @param programId SPL Token program account\n   * @param source Source account\n   * @param destination Destination account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Number of tokens to transfer\n   */\n  static createTransferInstruction(\n    programId: PublicKey,\n    source: PublicKey,\n    destination: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 3, // Transfer instruction\n        amount: new u64(amount).toBuffer(),\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: source, isSigner: false, isWritable: true},\n      {pubkey: destination, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: owner,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct an Approve instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Public key of the account\n   * @param delegate Account authorized to perform a transfer of tokens from the source account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Maximum number of tokens the delegate may transfer\n   */\n  static createApproveInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    delegate: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 4, // Approve instruction\n        amount: new u64(amount).toBuffer(),\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: delegate, isSigner: false, isWritable: false},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: owner, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Revoke instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Public key of the account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  static createRevokeInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 5, // Approve instruction\n      },\n      data,\n    );\n\n    let keys = [{pubkey: account, isSigner: false, isWritable: true}];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: owner, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a SetAuthority instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Public key of the account\n   * @param newAuthority New authority of the account\n   * @param authorityType Type of authority to set\n   * @param currentAuthority Current authority of the specified type\n   * @param multiSigners Signing accounts if `currentAuthority` is a multiSig\n   */\n  static createSetAuthorityInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    newAuthority: PublicKey | null,\n    authorityType: AuthorityType,\n    currentAuthority: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const commandDataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      BufferLayout.u8('authorityType'),\n      BufferLayout.u8('option'),\n      Layout.publicKey('newAuthority'),\n    ]);\n\n    let data = Buffer.alloc(1024);\n    {\n      const encodeLength = commandDataLayout.encode(\n        {\n          instruction: 6, // SetAuthority instruction\n          authorityType: AuthorityTypeCodes[authorityType],\n          option: newAuthority === null ? 0 : 1,\n          newAuthority: pubkeyToBuffer(newAuthority || new PublicKey(0)),\n        },\n        data,\n      );\n      data = data.slice(0, encodeLength);\n    }\n\n    let keys = [{pubkey: account, isSigner: false, isWritable: true}];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: currentAuthority, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: currentAuthority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a MintTo instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Public key of the mint\n   * @param dest Public key of the account to mint to\n   * @param authority The mint authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Amount to mint\n   */\n  static createMintToInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    dest: PublicKey,\n    authority: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 7, // MintTo instruction\n        amount: new u64(amount).toBuffer(),\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: mint, isSigner: false, isWritable: true},\n      {pubkey: dest, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: authority,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: authority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Burn instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Mint for the account\n   * @param account Account to burn tokens from\n   * @param owner Owner of the account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount amount to burn\n   */\n  static createBurnInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    account: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 8, // Burn instruction\n        amount: new u64(amount).toBuffer(),\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: owner,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Close instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Account to close\n   * @param dest Account to receive the remaining balance of the closed account\n   * @param authority Account Close authority\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  static createCloseAccountInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    dest: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 9, // CloseAccount instruction\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: dest, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: owner, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Freeze instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Account to freeze\n   * @param mint Mint account\n   * @param authority Mint freeze authority\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  static createFreezeAccountInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    mint: PublicKey,\n    authority: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 10, // FreezeAccount instruction\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: authority, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: authority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a Thaw instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Account to thaw\n   * @param mint Mint account\n   * @param authority Mint freeze authority\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   */\n  static createThawAccountInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    mint: PublicKey,\n    authority: PublicKey,\n    multiSigners: Array<Signer>,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 11, // ThawAccount instruction\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: authority, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: authority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a TransferChecked instruction\n   *\n   * @param programId SPL Token program account\n   * @param source Source account\n   * @param mint Mint account\n   * @param destination Destination account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Number of tokens to transfer\n   * @param decimals Number of decimals in transfer amount\n   */\n  static createTransferCheckedInstruction(\n    programId: PublicKey,\n    source: PublicKey,\n    mint: PublicKey,\n    destination: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n      BufferLayout.u8('decimals'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 12, // TransferChecked instruction\n        amount: new u64(amount).toBuffer(),\n        decimals,\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: source, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n      {pubkey: destination, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: owner,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct an ApproveChecked instruction\n   *\n   * @param programId SPL Token program account\n   * @param account Public key of the account\n   * @param mint Mint account\n   * @param delegate Account authorized to perform a transfer of tokens from the source account\n   * @param owner Owner of the source account\n   * @param multiSigners Signing accounts if `owner` is a multiSig\n   * @param amount Maximum number of tokens the delegate may transfer\n   * @param decimals Number of decimals in approve amount\n   */\n  static createApproveCheckedInstruction(\n    programId: PublicKey,\n    account: PublicKey,\n    mint: PublicKey,\n    delegate: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n      BufferLayout.u8('decimals'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 13, // ApproveChecked instruction\n        amount: new u64(amount).toBuffer(),\n        decimals,\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: false},\n      {pubkey: delegate, isSigner: false, isWritable: false},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({pubkey: owner, isSigner: true, isWritable: false});\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a MintToChecked instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Public key of the mint\n   * @param dest Public key of the account to mint to\n   * @param authority The mint authority\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount Amount to mint\n   * @param decimals Number of decimals in amount to mint\n   */\n  static createMintToCheckedInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    dest: PublicKey,\n    authority: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n      BufferLayout.u8('decimals'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 14, // MintToChecked instruction\n        amount: new u64(amount).toBuffer(),\n        decimals,\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: mint, isSigner: false, isWritable: true},\n      {pubkey: dest, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: authority,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: authority, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a BurnChecked instruction\n   *\n   * @param programId SPL Token program account\n   * @param mint Mint for the account\n   * @param account Account to burn tokens from\n   * @param owner Owner of the account\n   * @param multiSigners Signing accounts if `authority` is a multiSig\n   * @param amount amount to burn\n   */\n  static createBurnCheckedInstruction(\n    programId: PublicKey,\n    mint: PublicKey,\n    account: PublicKey,\n    owner: PublicKey,\n    multiSigners: Array<Signer>,\n    amount: number | u64,\n    decimals: number,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([\n      BufferLayout.u8('instruction'),\n      Layout.uint64('amount'),\n      BufferLayout.u8('decimals'),\n    ]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 15, // BurnChecked instruction\n        amount: new u64(amount).toBuffer(),\n        decimals,\n      },\n      data,\n    );\n\n    let keys = [\n      {pubkey: account, isSigner: false, isWritable: true},\n      {pubkey: mint, isSigner: false, isWritable: true},\n    ];\n    if (multiSigners.length === 0) {\n      keys.push({\n        pubkey: owner,\n        isSigner: true,\n        isWritable: false,\n      });\n    } else {\n      keys.push({pubkey: owner, isSigner: false, isWritable: false});\n      multiSigners.forEach(signer =>\n        keys.push({\n          pubkey: signer.publicKey,\n          isSigner: true,\n          isWritable: false,\n        }),\n      );\n    }\n\n    return new TransactionInstruction({\n      keys,\n      programId: programId,\n      data,\n    });\n  }\n\n  /**\n   * Construct a SyncNative instruction\n   *\n   * @param programId SPL Token program account\n   * @param nativeAccount Account to sync lamports from\n   */\n  static createSyncNativeInstruction(\n    programId: PublicKey,\n    nativeAccount: PublicKey,\n  ): TransactionInstruction {\n    const dataLayout = BufferLayout.struct([BufferLayout.u8('instruction')]);\n\n    const data = Buffer.alloc(dataLayout.span);\n    dataLayout.encode(\n      {\n        instruction: 17, // SyncNative instruction\n      },\n      data,\n    );\n\n    let keys = [{pubkey: nativeAccount, isSigner: false, isWritable: true}];\n    return new TransactionInstruction({keys, programId: programId, data});\n  }\n\n  /**\n   * Get the address for the associated token account\n   *\n   * @param associatedProgramId SPL Associated Token program account\n   * @param programId SPL Token program account\n   * @param mint Token mint account\n   * @param owner Owner of the new account\n   * @return Public key of the associated token account\n   */\n  static async getAssociatedTokenAddress(\n    associatedProgramId: PublicKey,\n    programId: PublicKey,\n    mint: PublicKey,\n    owner: PublicKey,\n    allowOwnerOffCurve: boolean = false,\n  ): Promise<PublicKey> {\n    if (!allowOwnerOffCurve && !PublicKey.isOnCurve(owner.toBuffer())) {\n      throw new Error(`Owner cannot sign: ${owner.toString()}`);\n    }\n    return (\n      await PublicKey.findProgramAddress(\n        [owner.toBuffer(), programId.toBuffer(), mint.toBuffer()],\n        associatedProgramId,\n      )\n    )[0];\n  }\n\n  /**\n   * Construct the AssociatedTokenProgram instruction to create the associated\n   * token account\n   *\n   * @param associatedProgramId SPL Associated Token program account\n   * @param programId SPL Token program account\n   * @param mint Token mint account\n   * @param associatedAccount New associated account\n   * @param owner Owner of the new account\n   * @param payer Payer of fees\n   */\n  static createAssociatedTokenAccountInstruction(\n    associatedProgramId: PublicKey,\n    programId: PublicKey,\n    mint: PublicKey,\n    associatedAccount: PublicKey,\n    owner: PublicKey,\n    payer: PublicKey,\n  ): TransactionInstruction {\n    const data = Buffer.alloc(0);\n\n    let keys = [\n      {pubkey: payer, isSigner: true, isWritable: true},\n      {pubkey: associatedAccount, isSigner: false, isWritable: true},\n      {pubkey: owner, isSigner: false, isWritable: false},\n      {pubkey: mint, isSigner: false, isWritable: false},\n      {pubkey: SystemProgram.programId, isSigner: false, isWritable: false},\n      {pubkey: programId, isSigner: false, isWritable: false},\n      {pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false},\n    ];\n\n    return new TransactionInstruction({\n      keys,\n      programId: associatedProgramId,\n      data,\n    });\n  }\n}\n"], "names": ["require$$0", "ieee754", "require$$1", "inspect", "<PERSON><PERSON><PERSON><PERSON>", "global", "inherits", "utilInspect", "<PERSON><PERSON><PERSON>", "public<PERSON>ey", "property", "BufferLayout", "uint64", "sendAndConfirmTransaction", "title", "connection", "transaction", "signers", "realSendAndConfirmTransaction", "skipPreflight", "TOKEN_PROGRAM_ID", "PublicKey", "ASSOCIATED_TOKEN_PROGRAM_ID", "FAILED_TO_FIND_ACCOUNT", "INVALID_ACCOUNT_OWNER", "pubkeyToBuffer", "from", "<PERSON><PERSON><PERSON><PERSON>", "u64", "BN", "a", "toArray", "reverse", "b", "length", "assert", "zeroPad", "alloc", "copy", "fromBuffer", "buffer", "map", "i", "toString", "slice", "join", "isAccount", "accountOrPublicKey", "AuthorityTypeCodes", "MintTokens", "FreezeA<PERSON>unt", "Account<PERSON><PERSON><PERSON>", "CloseAccount", "NATIVE_MINT", "MintLayout", "Layout", "AccountLayout", "MultisigLayout", "Token", "constructor", "programId", "payer", "Object", "assign", "associatedProgramId", "getMinBalanceRentForExemptMint", "getMinimumBalanceForRentExemption", "span", "getMinBalanceRentForExemptAccount", "getMinBalanceRentForExemptMultisig", "createMint", "mintAuthority", "freezeAuthority", "decimals", "mintAccount", "Keypair", "generate", "token", "balanceNeeded", "Transaction", "add", "SystemProgram", "createAccount", "fromPubkey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lamports", "space", "createInitMintInstruction", "owner", "newAccount", "mintPublicKey", "createInitAccountInstruction", "createAssociatedTokenAccount", "associatedAddress", "getAssociatedTokenAddress", "createAssociatedTokenAccountInternal", "createAssociatedTokenAccountInstruction", "getOrCreateAssociatedAccountInfo", "getAccountInfo", "err", "message", "createWrappedNativeAccount", "amount", "transfer", "to<PERSON><PERSON><PERSON>", "createMultisig", "m", "multisigAccount", "keys", "pubkey", "<PERSON><PERSON><PERSON><PERSON>", "isWritable", "SYSVAR_RENT_PUBKEY", "for<PERSON>ach", "signer", "push", "dataLayout", "data", "encode", "instruction", "getMintInfo", "info", "Error", "equals", "JSON", "stringify", "mintInfo", "decode", "mintAuthorityOption", "supply", "isInitialized", "freezeAuthorityOption", "account", "commitment", "accountInfo", "address", "mint", "delegateOption", "delegate", "delegated<PERSON><PERSON>", "state", "isFrozen", "isNativeOption", "rentExemptReserve", "isNative", "closeAuthorityOption", "closeAuthority", "getMultisigInfo", "multisig", "multisigInfo", "signer1", "signer2", "signer3", "signer4", "signer5", "signer6", "signer7", "signer8", "signer9", "signer10", "signer11", "source", "destination", "multiSigners", "ownerPublicKey", "createTransferInstruction", "approve", "createApproveInstruction", "revoke", "createRevokeInstruction", "setAuthority", "newAuthority", "authorityType", "currentAuthority", "currentAuthorityPublicKey", "createSetAuthorityInstruction", "mintTo", "dest", "authority", "createMintToInstruction", "burn", "createBurnInstruction", "closeAccount", "authorityPublicKey", "createCloseAccountInstruction", "freezeAccount", "createFreezeAccountInstruction", "thawAccount", "createThawAccountInstruction", "transferChecked", "createTransferCheckedInstruction", "approveChecked", "createApproveCheckedInstruction", "mintToChecked", "createMintToCheckedInstruction", "burnChecked", "createBurnCheckedInstruction", "syncNative", "nativeAccount", "createSyncNativeInstruction", "commandDataLayout", "encodeLength", "option", "TransactionInstruction", "allowOwnerOffCurve", "isOnCurve", "findProgramAddress", "associatedAccount"], "mappings": ";;;;;;;;mBAEkB,GAAG,WAAU;oBACZ,GAAG,YAAW;sBACZ,GAAG,cAAa;AACrC;AACA,IAAI,MAAM,GAAG,GAAE;AACf,IAAI,SAAS,GAAG,GAAE;AAClB,IAAI,GAAG,GAAG,OAAO,UAAU,KAAK,WAAW,GAAG,UAAU,GAAG,MAAK;AAChE;AACA,IAAI,IAAI,GAAG,mEAAkE;AAC7E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AACjD,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAC;AACrB,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC;AACnC,CAAC;AACD;AACA;AACA;AACA,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,GAAE;AACjC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,GAAE;AACjC;AACA,SAAS,OAAO,EAAE,GAAG,EAAE;AACvB,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,OAAM;AACtB;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE;AACnB,IAAI,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC;AACrE,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAC;AACjC,EAAE,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,IAAG;AACrC;AACA,EAAE,IAAI,eAAe,GAAG,QAAQ,KAAK,GAAG;AACxC,MAAM,CAAC;AACP,MAAM,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAC;AACxB;AACA,EAAE,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC;AACpC,CAAC;AACD;AACA;AACA,SAAS,UAAU,EAAE,GAAG,EAAE;AAC1B,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,GAAG,EAAC;AACzB,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC,EAAC;AACxB,EAAE,IAAI,eAAe,GAAG,IAAI,CAAC,CAAC,EAAC;AAC/B,EAAE,OAAO,CAAC,CAAC,QAAQ,GAAG,eAAe,IAAI,CAAC,GAAG,CAAC,IAAI,eAAe;AACjE,CAAC;AACD;AACA,SAAS,WAAW,EAAE,GAAG,EAAE,QAAQ,EAAE,eAAe,EAAE;AACtD,EAAE,OAAO,CAAC,CAAC,QAAQ,GAAG,eAAe,IAAI,CAAC,GAAG,CAAC,IAAI,eAAe;AACjE,CAAC;AACD;AACA,SAAS,WAAW,EAAE,GAAG,EAAE;AAC3B,EAAE,IAAI,IAAG;AACT,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,GAAG,EAAC;AACzB,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC,EAAC;AACxB,EAAE,IAAI,eAAe,GAAG,IAAI,CAAC,CAAC,EAAC;AAC/B;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,QAAQ,EAAE,eAAe,CAAC,EAAC;AAChE;AACA,EAAE,IAAI,OAAO,GAAG,EAAC;AACjB;AACA;AACA,EAAE,IAAI,GAAG,GAAG,eAAe,GAAG,CAAC;AAC/B,MAAM,QAAQ,GAAG,CAAC;AAClB,MAAM,SAAQ;AACd;AACA,EAAE,IAAI,EAAC;AACP,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;AAC/B,IAAI,GAAG;AACP,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;AACzC,OAAO,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC9C,OAAO,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7C,MAAM,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC;AACtC,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,KAAI;AACvC,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,KAAI;AACtC,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,KAAI;AAC/B,GAAG;AACH;AACA,EAAE,IAAI,eAAe,KAAK,CAAC,EAAE;AAC7B,IAAI,GAAG;AACP,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACxC,OAAO,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC;AAC7C,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,KAAI;AAC/B,GAAG;AACH;AACA,EAAE,IAAI,eAAe,KAAK,CAAC,EAAE;AAC7B,IAAI,GAAG;AACP,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;AACzC,OAAO,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7C,OAAO,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC;AAC7C,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,KAAI;AACtC,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,KAAI;AAC/B,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,eAAe,EAAE,GAAG,EAAE;AAC/B,EAAE,OAAO,MAAM,CAAC,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;AACjC,IAAI,MAAM,CAAC,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;AAC5B,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AAC3B,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC;AACtB,CAAC;AACD;AACA,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE;AACzC,EAAE,IAAI,IAAG;AACT,EAAE,IAAI,MAAM,GAAG,GAAE;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;AACvC,IAAI,GAAG;AACP,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,QAAQ;AAClC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC;AACpC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAC;AAC3B,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAC;AACrC,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;AACxB,CAAC;AACD;AACA,SAAS,aAAa,EAAE,KAAK,EAAE;AAC/B,EAAE,IAAI,IAAG;AACT,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,OAAM;AACxB,EAAE,IAAI,UAAU,GAAG,GAAG,GAAG,EAAC;AAC1B,EAAE,IAAI,KAAK,GAAG,GAAE;AAChB,EAAE,IAAI,cAAc,GAAG,MAAK;AAC5B;AACA;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,GAAG,GAAG,UAAU,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,cAAc,EAAE;AAC1E,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,cAAc,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC,EAAC;AAChG,GAAG;AACH;AACA;AACA,EAAE,IAAI,UAAU,KAAK,CAAC,EAAE;AACxB,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,EAAC;AACxB,IAAI,KAAK,CAAC,IAAI;AACd,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;AACtB,MAAM,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;AAC/B,MAAM,IAAI;AACV,MAAK;AACL,GAAG,MAAM,IAAI,UAAU,KAAK,CAAC,EAAE;AAC/B,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,EAAC;AAChD,IAAI,KAAK,CAAC,IAAI;AACd,MAAM,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC;AACvB,MAAM,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;AAC/B,MAAM,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;AAC/B,MAAM,GAAG;AACT,MAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AACvB;;;;;;YCpJY,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAC7D,EAAE,IAAI,CAAC,EAAE,EAAC;AACV,EAAE,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,GAAG,EAAC;AACpC,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,EAAC;AAC5B,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,EAAC;AACvB,EAAE,IAAI,KAAK,GAAG,CAAC,EAAC;AAChB,EAAE,IAAI,CAAC,GAAG,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,EAAC;AACjC,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAC;AACvB,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAC;AAC5B;AACA,EAAE,CAAC,IAAI,EAAC;AACR;AACA,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAC;AAC/B,EAAE,CAAC,MAAM,CAAC,KAAK,EAAC;AAChB,EAAE,KAAK,IAAI,KAAI;AACf,EAAE,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;AAC9E;AACA,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAC;AAC/B,EAAE,CAAC,MAAM,CAAC,KAAK,EAAC;AAChB,EAAE,KAAK,IAAI,KAAI;AACf,EAAE,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;AAC9E;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACf,IAAI,CAAC,GAAG,CAAC,GAAG,MAAK;AACjB,GAAG,MAAM,IAAI,CAAC,KAAK,IAAI,EAAE;AACzB,IAAI,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC;AAC9C,GAAG,MAAM;AACT,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAC;AAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,MAAK;AACjB,GAAG;AACH,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AACjD,EAAC;AACD;aACa,GAAG,UAAU,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACrE,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAC;AACb,EAAE,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,GAAG,EAAC;AACpC,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,EAAC;AAC5B,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,EAAC;AACvB,EAAE,IAAI,EAAE,IAAI,IAAI,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC;AAClE,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAC;AACjC,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,EAAC;AACvB,EAAE,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAC;AAC7D;AACA,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAC;AACzB;AACA,EAAE,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,QAAQ,EAAE;AAC1C,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAC;AAC5B,IAAI,CAAC,GAAG,KAAI;AACZ,GAAG,MAAM;AACT,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,EAAC;AAC9C,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC3C,MAAM,CAAC,GAAE;AACT,MAAM,CAAC,IAAI,EAAC;AACZ,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,EAAE;AACxB,MAAM,KAAK,IAAI,EAAE,GAAG,EAAC;AACrB,KAAK,MAAM;AACX,MAAM,KAAK,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,EAAC;AAC1C,KAAK;AACL,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;AACxB,MAAM,CAAC,GAAE;AACT,MAAM,CAAC,IAAI,EAAC;AACZ,KAAK;AACL;AACA,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,EAAE;AAC3B,MAAM,CAAC,GAAG,EAAC;AACX,MAAM,CAAC,GAAG,KAAI;AACd,KAAK,MAAM,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,EAAE;AAC/B,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAC;AAC/C,MAAM,CAAC,GAAG,CAAC,GAAG,MAAK;AACnB,KAAK,MAAM;AACX,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAC;AAC5D,MAAM,CAAC,GAAG,EAAC;AACX,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE;AAClF;AACA,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,EAAC;AACrB,EAAE,IAAI,IAAI,KAAI;AACd,EAAE,OAAO,IAAI,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE;AACjF;AACA,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAG;AACnC;;;;;;;;;;AC3EA;AACA,MAAM,MAAM,GAAGA,SAAoB;AACnC,MAAMC,SAAO,GAAGC,QAAkB;AAClC,MAAM,mBAAmB;AACzB,EAAE,CAAC,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU;AACtE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,4BAA4B,CAAC;AACjD,MAAM,KAAI;AACV;AACA,iBAAiB,OAAM;AACvB,qBAAqB,WAAU;AAC/B,4BAA4B,GAAE;AAC9B;AACA,MAAM,YAAY,GAAG,WAAU;AAC/B,qBAAqB,aAAY;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,mBAAmB,GAAG,iBAAiB,GAAE;AAChD;AACA,IAAI,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,OAAO,KAAK,WAAW;AACjE,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;AACzC,EAAE,OAAO,CAAC,KAAK;AACf,IAAI,2EAA2E;AAC/E,IAAI,sEAAsE;AAC1E,IAAG;AACH,CAAC;AACD;AACA,SAAS,iBAAiB,IAAI;AAC9B;AACA,EAAE,IAAI;AACN,IAAI,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC,EAAC;AACjC,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,OAAO,EAAE,EAAE,GAAE;AACpD,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,EAAC;AACtD,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,EAAC;AACrC,IAAI,OAAO,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE;AAC3B,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,KAAK;AAChB,GAAG;AACH,CAAC;AACD;AACA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE;AAClD,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,GAAG,EAAE,YAAY;AACnB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAS;AAChD,IAAI,OAAO,IAAI,CAAC,MAAM;AACtB,GAAG;AACH,CAAC,EAAC;AACF;AACA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE;AAClD,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,GAAG,EAAE,YAAY;AACnB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAS;AAChD,IAAI,OAAO,IAAI,CAAC,UAAU;AAC1B,GAAG;AACH,CAAC,EAAC;AACF;AACA,SAAS,YAAY,EAAE,MAAM,EAAE;AAC/B,EAAE,IAAI,MAAM,GAAG,YAAY,EAAE;AAC7B,IAAI,MAAM,IAAI,UAAU,CAAC,aAAa,GAAG,MAAM,GAAG,gCAAgC,CAAC;AACnF,GAAG;AACH;AACA,EAAE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,EAAC;AACpC,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,SAAS,EAAC;AAC9C,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,EAAE,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE;AAChD;AACA,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;AAC9C,MAAM,MAAM,IAAI,SAAS;AACzB,QAAQ,oEAAoE;AAC5E,OAAO;AACP,KAAK;AACL,IAAI,OAAO,WAAW,CAAC,GAAG,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,gBAAgB,EAAE,MAAM,CAAC;AAC5C,CAAC;AACD;AACA,MAAM,CAAC,QAAQ,GAAG,KAAI;AACtB;AACA,SAAS,IAAI,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE;AAChD,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjC,IAAI,OAAO,UAAU,CAAC,KAAK,EAAE,gBAAgB,CAAC;AAC9C,GAAG;AACH;AACA,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AACjC,IAAI,OAAO,aAAa,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE;AACrB,IAAI,MAAM,IAAI,SAAS;AACvB,MAAM,6EAA6E;AACnF,MAAM,sCAAsC,IAAI,OAAO,KAAK,CAAC;AAC7D,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE,WAAW,CAAC;AACpC,OAAO,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,EAAE;AACxD,IAAI,OAAO,eAAe,CAAC,KAAK,EAAE,gBAAgB,EAAE,MAAM,CAAC;AAC3D,GAAG;AACH;AACA,EAAE,IAAI,OAAO,iBAAiB,KAAK,WAAW;AAC9C,OAAO,UAAU,CAAC,KAAK,EAAE,iBAAiB,CAAC;AAC3C,OAAO,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE;AAC/D,IAAI,OAAO,eAAe,CAAC,KAAK,EAAE,gBAAgB,EAAE,MAAM,CAAC;AAC3D,GAAG;AACH;AACA,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjC,IAAI,MAAM,IAAI,SAAS;AACvB,MAAM,uEAAuE;AAC7E,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,GAAE;AAClD,EAAE,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,EAAE;AAC5C,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,gBAAgB,EAAE,MAAM,CAAC;AACzD,GAAG;AACH;AACA,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,EAAC;AAC7B,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC;AACjB;AACA,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI;AACjE,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,UAAU,EAAE;AACvD,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,EAAE,gBAAgB,EAAE,MAAM,CAAC;AACrF,GAAG;AACH;AACA,EAAE,MAAM,IAAI,SAAS;AACrB,IAAI,6EAA6E;AACjF,IAAI,sCAAsC,IAAI,OAAO,KAAK,CAAC;AAC3D,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,IAAI,GAAG,UAAU,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE;AACzD,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,gBAAgB,EAAE,MAAM,CAAC;AAC9C,EAAC;AACD;AACA;AACA;AACA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,SAAS,EAAC;AAC7D,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,EAAC;AACzC;AACA,SAAS,UAAU,EAAE,IAAI,EAAE;AAC3B,EAAE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAChC,IAAI,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC;AACjE,GAAG,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE;AACvB,IAAI,MAAM,IAAI,UAAU,CAAC,aAAa,GAAG,IAAI,GAAG,gCAAgC,CAAC;AACjF,GAAG;AACH,CAAC;AACD;AACA,SAAS,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;AACtC,EAAE,UAAU,CAAC,IAAI,EAAC;AAClB,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE;AACjB,IAAI,OAAO,YAAY,CAAC,IAAI,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,IAAI,KAAK,SAAS,EAAE;AAC1B;AACA;AACA;AACA,IAAI,OAAO,OAAO,QAAQ,KAAK,QAAQ;AACvC,QAAQ,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC/C,QAAQ,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACrC,GAAG;AACH,EAAE,OAAO,YAAY,CAAC,IAAI,CAAC;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,KAAK,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/C,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC;AACpC,EAAC;AACD;AACA,SAAS,WAAW,EAAE,IAAI,EAAE;AAC5B,EAAE,UAAU,CAAC,IAAI,EAAC;AAClB,EAAE,OAAO,YAAY,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,CAAC,WAAW,GAAG,UAAU,IAAI,EAAE;AACrC,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC;AAC1B,EAAC;AACD;AACA;AACA;AACA,MAAM,CAAC,eAAe,GAAG,UAAU,IAAI,EAAE;AACzC,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC;AAC1B,EAAC;AACD;AACA,SAAS,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE;AACvC,EAAE,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,EAAE,EAAE;AACvD,IAAI,QAAQ,GAAG,OAAM;AACrB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACpC,IAAI,MAAM,IAAI,SAAS,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AACxD,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,EAAC;AACjD,EAAE,IAAI,GAAG,GAAG,YAAY,CAAC,MAAM,EAAC;AAChC;AACA,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAC;AAC5C;AACA,EAAE,IAAI,MAAM,KAAK,MAAM,EAAE;AACzB;AACA;AACA;AACA,IAAI,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,EAAC;AAC9B,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,aAAa,EAAE,KAAK,EAAE;AAC/B,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAC;AACjE,EAAE,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,EAAC;AAClC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACtC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAG;AAC3B,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,aAAa,EAAE,SAAS,EAAE;AACnC,EAAE,IAAI,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;AACzC,IAAI,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,SAAS,EAAC;AAC1C,IAAI,OAAO,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC;AACzE,GAAG;AACH,EAAE,OAAO,aAAa,CAAC,SAAS,CAAC;AACjC,CAAC;AACD;AACA,SAAS,eAAe,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE;AACrD,EAAE,IAAI,UAAU,GAAG,CAAC,IAAI,KAAK,CAAC,UAAU,GAAG,UAAU,EAAE;AACvD,IAAI,MAAM,IAAI,UAAU,CAAC,sCAAsC,CAAC;AAChE,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,UAAU,GAAG,UAAU,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE;AACrD,IAAI,MAAM,IAAI,UAAU,CAAC,sCAAsC,CAAC;AAChE,GAAG;AACH;AACA,EAAE,IAAI,IAAG;AACT,EAAE,IAAI,UAAU,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE;AACxD,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,EAAC;AAC/B,GAAG,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE;AACnC,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,UAAU,EAAC;AAC3C,GAAG,MAAM;AACT,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAC;AACnD,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,SAAS,EAAC;AAC9C;AACA,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,UAAU,EAAE,GAAG,EAAE;AAC1B,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC5B,IAAI,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAC;AACvC,IAAI,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,EAAC;AACjC;AACA,IAAI,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,MAAM,OAAO,GAAG;AAChB,KAAK;AACL;AACA,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAC;AAC5B,IAAI,OAAO,GAAG;AACd,GAAG;AACH;AACA,EAAE,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;AAChC,IAAI,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AACnE,MAAM,OAAO,YAAY,CAAC,CAAC,CAAC;AAC5B,KAAK;AACL,IAAI,OAAO,aAAa,CAAC,GAAG,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACxD,IAAI,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC;AAClC,GAAG;AACH,CAAC;AACD;AACA,SAAS,OAAO,EAAE,MAAM,EAAE;AAC1B;AACA;AACA,EAAE,IAAI,MAAM,IAAI,YAAY,EAAE;AAC9B,IAAI,MAAM,IAAI,UAAU,CAAC,iDAAiD;AAC1E,yBAAyB,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC;AAC3E,GAAG;AACH,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,CAAC;AACD;AACA,SAAS,UAAU,EAAE,MAAM,EAAE;AAC7B,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE;AACzB,IAAI,MAAM,GAAG,EAAC;AACd,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA,MAAM,CAAC,QAAQ,GAAG,SAAS,QAAQ,EAAE,CAAC,EAAE;AACxC,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,SAAS,KAAK,IAAI;AAC1C,IAAI,CAAC,KAAK,MAAM,CAAC,SAAS;AAC1B,EAAC;AACD;AACA,MAAM,CAAC,OAAO,GAAG,SAAS,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE;AACzC,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAC;AAC3E,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAC;AAC3E,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAClD,IAAI,MAAM,IAAI,SAAS;AACvB,MAAM,uEAAuE;AAC7E,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;AACvB;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAM;AAClB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAM;AAClB;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AACtD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACvB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AACd,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AACd,MAAM,KAAK;AACX,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC;AACrB,EAAE,OAAO,CAAC;AACV,EAAC;AACD;AACA,MAAM,CAAC,UAAU,GAAG,SAAS,UAAU,EAAE,QAAQ,EAAE;AACnD,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE;AACxC,IAAI,KAAK,KAAK,CAAC;AACf,IAAI,KAAK,MAAM,CAAC;AAChB,IAAI,KAAK,OAAO,CAAC;AACjB,IAAI,KAAK,OAAO,CAAC;AACjB,IAAI,KAAK,QAAQ,CAAC;AAClB,IAAI,KAAK,QAAQ,CAAC;AAClB,IAAI,KAAK,QAAQ,CAAC;AAClB,IAAI,KAAK,MAAM,CAAC;AAChB,IAAI,KAAK,OAAO,CAAC;AACjB,IAAI,KAAK,SAAS,CAAC;AACnB,IAAI,KAAK,UAAU;AACnB,MAAM,OAAO,IAAI;AACjB,IAAI;AACJ,MAAM,OAAO,KAAK;AAClB,GAAG;AACH,EAAC;AACD;AACA,MAAM,CAAC,MAAM,GAAG,SAAS,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;AAC/C,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC5B,IAAI,MAAM,IAAI,SAAS,CAAC,6CAA6C,CAAC;AACtE,GAAG;AACH;AACA,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1B,GAAG;AACH;AACA,EAAE,IAAI,EAAC;AACP,EAAE,IAAI,MAAM,KAAK,SAAS,EAAE;AAC5B,IAAI,MAAM,GAAG,EAAC;AACd,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACtC,MAAM,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM;AAC9B,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAC;AAC3C,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACpC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;AACrB,IAAI,IAAI,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE;AACrC,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE;AAC5C,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAC;AACzD,QAAQ,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAC;AAC7B,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;AACrC,UAAU,MAAM;AAChB,UAAU,GAAG;AACb,UAAU,GAAG;AACb,UAAS;AACT,OAAO;AACP,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACtC,MAAM,MAAM,IAAI,SAAS,CAAC,6CAA6C,CAAC;AACxE,KAAK,MAAM;AACX,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAC;AAC3B,KAAK;AACL,IAAI,GAAG,IAAI,GAAG,CAAC,OAAM;AACrB,GAAG;AACH,EAAE,OAAO,MAAM;AACf,EAAC;AACD;AACA,SAAS,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE;AACvC,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC/B,IAAI,OAAO,MAAM,CAAC,MAAM;AACxB,GAAG;AACH,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;AACrE,IAAI,OAAO,MAAM,CAAC,UAAU;AAC5B,GAAG;AACH,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAClC,IAAI,MAAM,IAAI,SAAS;AACvB,MAAM,4EAA4E;AAClF,MAAM,gBAAgB,GAAG,OAAO,MAAM;AACtC,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,GAAG,GAAG,MAAM,CAAC,OAAM;AAC3B,EAAE,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAC;AACnE,EAAE,IAAI,CAAC,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;AACvC;AACA;AACA,EAAE,IAAI,WAAW,GAAG,MAAK;AACzB,EAAE,SAAS;AACX,IAAI,QAAQ,QAAQ;AACpB,MAAM,KAAK,OAAO,CAAC;AACnB,MAAM,KAAK,QAAQ,CAAC;AACpB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,GAAG;AAClB,MAAM,KAAK,MAAM,CAAC;AAClB,MAAM,KAAK,OAAO;AAClB,QAAQ,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM;AACzC,MAAM,KAAK,MAAM,CAAC;AAClB,MAAM,KAAK,OAAO,CAAC;AACnB,MAAM,KAAK,SAAS,CAAC;AACrB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,GAAG,GAAG,CAAC;AACtB,MAAM,KAAK,KAAK;AAChB,QAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,MAAM;AAC3C,MAAM;AACN,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,OAAO,SAAS,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM;AAC5D,SAAS;AACT,QAAQ,QAAQ,GAAG,CAAC,EAAE,GAAG,QAAQ,EAAE,WAAW,GAAE;AAChD,QAAQ,WAAW,GAAG,KAAI;AAC1B,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,CAAC,UAAU,GAAG,WAAU;AAC9B;AACA,SAAS,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE;AAC7C,EAAE,IAAI,WAAW,GAAG,MAAK;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,EAAE;AACxC,IAAI,KAAK,GAAG,EAAC;AACb,GAAG;AACH;AACA;AACA,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AAC3B,IAAI,OAAO,EAAE;AACb,GAAG;AACH;AACA,EAAE,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;AAC9C,IAAI,GAAG,GAAG,IAAI,CAAC,OAAM;AACrB,GAAG;AACH;AACA,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE;AAChB,IAAI,OAAO,EAAE;AACb,GAAG;AACH;AACA;AACA,EAAE,GAAG,MAAM,EAAC;AACZ,EAAE,KAAK,MAAM,EAAC;AACd;AACA,EAAE,IAAI,GAAG,IAAI,KAAK,EAAE;AACpB,IAAI,OAAO,EAAE;AACb,GAAG;AACH;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,GAAG,OAAM;AAClC;AACA,EAAE,OAAO,IAAI,EAAE;AACf,IAAI,QAAQ,QAAQ;AACpB,MAAM,KAAK,KAAK;AAChB,QAAQ,OAAO,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AACzC;AACA,MAAM,KAAK,MAAM,CAAC;AAClB,MAAM,KAAK,OAAO;AAClB,QAAQ,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AAC1C;AACA,MAAM,KAAK,OAAO;AAClB,QAAQ,OAAO,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AAC3C;AACA,MAAM,KAAK,QAAQ,CAAC;AACpB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AAC5C;AACA,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AAC5C;AACA,MAAM,KAAK,MAAM,CAAC;AAClB,MAAM,KAAK,OAAO,CAAC;AACnB,MAAM,KAAK,SAAS,CAAC;AACrB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AAC7C;AACA,MAAM;AACN,QAAQ,IAAI,WAAW,EAAE,MAAM,IAAI,SAAS,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AAC7E,QAAQ,QAAQ,GAAG,CAAC,QAAQ,GAAG,EAAE,EAAE,WAAW,GAAE;AAChD,QAAQ,WAAW,GAAG,KAAI;AAC1B,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG,KAAI;AACjC;AACA,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACxB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AACb,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC;AACV,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,IAAI;AAC7C,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,OAAM;AACzB,EAAE,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;AACrB,IAAI,MAAM,IAAI,UAAU,CAAC,2CAA2C,CAAC;AACrE,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;AACnC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC;AACxB,GAAG;AACH,EAAE,OAAO,IAAI;AACb,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,IAAI;AAC7C,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,OAAM;AACzB,EAAE,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;AACrB,IAAI,MAAM,IAAI,UAAU,CAAC,2CAA2C,CAAC;AACrE,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;AACnC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC;AACxB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC;AAC5B,GAAG;AACH,EAAE,OAAO,IAAI;AACb,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,IAAI;AAC7C,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,OAAM;AACzB,EAAE,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;AACrB,IAAI,MAAM,IAAI,UAAU,CAAC,2CAA2C,CAAC;AACrE,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;AACnC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC;AACxB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC;AAC5B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC;AAC5B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC;AAC5B,GAAG;AACH,EAAE,OAAO,IAAI;AACb,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,IAAI;AACjD,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;AAC5B,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;AAC7B,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC;AAC/D,EAAE,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC5C,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,SAAQ;AAC3D;AACA,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,EAAE,CAAC,EAAE;AAC9C,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC;AAC3E,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,OAAO,IAAI;AAC7B,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC;AACtC,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,IAAI;AAC/C,EAAE,IAAI,GAAG,GAAG,GAAE;AACd,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,kBAAiB;AACvC,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,IAAI,GAAE;AACrE,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,QAAO;AACvC,EAAE,OAAO,UAAU,GAAG,GAAG,GAAG,GAAG;AAC/B,EAAC;AACD,IAAI,mBAAmB,EAAE;AACzB,EAAE,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,QAAO;AAClE,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE;AACrF,EAAE,IAAI,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE;AACtC,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAC;AAClE,GAAG;AACH,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAChC,IAAI,MAAM,IAAI,SAAS;AACvB,MAAM,kEAAkE;AACxE,MAAM,gBAAgB,IAAI,OAAO,MAAM,CAAC;AACxC,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE;AAC3B,IAAI,KAAK,GAAG,EAAC;AACb,GAAG;AACH,EAAE,IAAI,GAAG,KAAK,SAAS,EAAE;AACzB,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,EAAC;AACpC,GAAG;AACH,EAAE,IAAI,SAAS,KAAK,SAAS,EAAE;AAC/B,IAAI,SAAS,GAAG,EAAC;AACjB,GAAG;AACH,EAAE,IAAI,OAAO,KAAK,SAAS,EAAE;AAC7B,IAAI,OAAO,GAAG,IAAI,CAAC,OAAM;AACzB,GAAG;AACH;AACA,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,GAAG,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE;AAClF,IAAI,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC;AAC9C,GAAG;AACH;AACA,EAAE,IAAI,SAAS,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,EAAE;AAC5C,IAAI,OAAO,CAAC;AACZ,GAAG;AACH,EAAE,IAAI,SAAS,IAAI,OAAO,EAAE;AAC5B,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,EAAE,IAAI,KAAK,IAAI,GAAG,EAAE;AACpB,IAAI,OAAO,CAAC;AACZ,GAAG;AACH;AACA,EAAE,KAAK,MAAM,EAAC;AACd,EAAE,GAAG,MAAM,EAAC;AACZ,EAAE,SAAS,MAAM,EAAC;AAClB,EAAE,OAAO,MAAM,EAAC;AAChB;AACA,EAAE,IAAI,IAAI,KAAK,MAAM,EAAE,OAAO,CAAC;AAC/B;AACA,EAAE,IAAI,CAAC,GAAG,OAAO,GAAG,UAAS;AAC7B,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,MAAK;AACrB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAC;AAC5B;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,EAAC;AACjD,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAC;AAC7C;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AAChC,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE;AACvC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAC;AACrB,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAC;AACvB,MAAM,KAAK;AACX,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC;AACrB,EAAE,OAAO,CAAC;AACV,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,EAAE,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE;AACvE;AACA,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;AACpC;AACA;AACA,EAAE,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACtC,IAAI,QAAQ,GAAG,WAAU;AACzB,IAAI,UAAU,GAAG,EAAC;AAClB,GAAG,MAAM,IAAI,UAAU,GAAG,UAAU,EAAE;AACtC,IAAI,UAAU,GAAG,WAAU;AAC3B,GAAG,MAAM,IAAI,UAAU,GAAG,CAAC,UAAU,EAAE;AACvC,IAAI,UAAU,GAAG,CAAC,WAAU;AAC5B,GAAG;AACH,EAAE,UAAU,GAAG,CAAC,WAAU;AAC1B,EAAE,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE;AAC/B;AACA,IAAI,UAAU,GAAG,GAAG,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAC;AAC9C,GAAG;AACH;AACA;AACA,EAAE,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,WAAU;AAC7D,EAAE,IAAI,UAAU,IAAI,MAAM,CAAC,MAAM,EAAE;AACnC,IAAI,IAAI,GAAG,EAAE,OAAO,CAAC,CAAC;AACtB,SAAS,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,EAAC;AACvC,GAAG,MAAM,IAAI,UAAU,GAAG,CAAC,EAAE;AAC7B,IAAI,IAAI,GAAG,EAAE,UAAU,GAAG,EAAC;AAC3B,SAAS,OAAO,CAAC,CAAC;AAClB,GAAG;AACH;AACA;AACA,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAC;AACpC,GAAG;AACH;AACA;AACA,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC5B;AACA,IAAI,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,MAAM,OAAO,CAAC,CAAC;AACf,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAC;AAC/D,GAAG,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACtC,IAAI,GAAG,GAAG,GAAG,GAAG,KAAI;AACpB,IAAI,IAAI,OAAO,UAAU,CAAC,SAAS,CAAC,OAAO,KAAK,UAAU,EAAE;AAC5D,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,OAAO,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC;AACzE,OAAO,MAAM;AACb,QAAQ,OAAO,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC;AAC7E,OAAO;AACP,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAC;AACjE,GAAG;AACH;AACA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC;AAC7D,CAAC;AACD;AACA,SAAS,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE;AAC5D,EAAE,IAAI,SAAS,GAAG,EAAC;AACnB,EAAE,IAAI,SAAS,GAAG,GAAG,CAAC,OAAM;AAC5B,EAAE,IAAI,SAAS,GAAG,GAAG,CAAC,OAAM;AAC5B;AACA,EAAE,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC9B,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,GAAE;AAC7C,IAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO;AACnD,QAAQ,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,UAAU,EAAE;AAC3D,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5C,QAAQ,OAAO,CAAC,CAAC;AACjB,OAAO;AACP,MAAM,SAAS,GAAG,EAAC;AACnB,MAAM,SAAS,IAAI,EAAC;AACpB,MAAM,SAAS,IAAI,EAAC;AACpB,MAAM,UAAU,IAAI,EAAC;AACrB,KAAK;AACL,GAAG;AACH;AACA,EAAE,SAAS,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,CAAC,EAAE;AACzB,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;AACnB,KAAK,MAAM;AACX,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,SAAS,CAAC;AAC5C,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,EAAC;AACP,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,IAAI,UAAU,GAAG,CAAC,EAAC;AACvB,IAAI,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AAC7C,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,EAAE;AAC9E,QAAQ,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,EAAC;AAC7C,QAAQ,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK,SAAS,EAAE,OAAO,UAAU,GAAG,SAAS;AAC3E,OAAO,MAAM;AACb,QAAQ,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,WAAU;AAClD,QAAQ,UAAU,GAAG,CAAC,EAAC;AACvB,OAAO;AACP,KAAK;AACL,GAAG,MAAM;AACT,IAAI,IAAI,UAAU,GAAG,SAAS,GAAG,SAAS,EAAE,UAAU,GAAG,SAAS,GAAG,UAAS;AAC9E,IAAI,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,MAAM,IAAI,KAAK,GAAG,KAAI;AACtB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;AAC/C,UAAU,KAAK,GAAG,MAAK;AACvB,UAAU,KAAK;AACf,SAAS;AACT,OAAO;AACP,MAAM,IAAI,KAAK,EAAE,OAAO,CAAC;AACzB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE;AAC1E,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvD,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE;AACxE,EAAE,OAAO,oBAAoB,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC;AACpE,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE;AAChF,EAAE,OAAO,oBAAoB,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC;AACrE,EAAC;AACD;AACA,SAAS,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAChD,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;AAC9B,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,GAAG,OAAM;AACvC,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,MAAM,GAAG,UAAS;AACtB,GAAG,MAAM;AACT,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,EAAC;AAC3B,IAAI,IAAI,MAAM,GAAG,SAAS,EAAE;AAC5B,MAAM,MAAM,GAAG,UAAS;AACxB,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,OAAM;AAC9B;AACA,EAAE,IAAI,MAAM,GAAG,MAAM,GAAG,CAAC,EAAE;AAC3B,IAAI,MAAM,GAAG,MAAM,GAAG,EAAC;AACvB,GAAG;AACH,EAAE,IAAI,EAAC;AACP,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;AAC/B,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAC;AACxD,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC;AACrC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,OAAM;AAC5B,GAAG;AACH,EAAE,OAAO,CAAC;AACV,CAAC;AACD;AACA,SAAS,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACjD,EAAE,OAAO,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC;AAClF,CAAC;AACD;AACA,SAAS,UAAU,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClD,EAAE,OAAO,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC;AAC9D,CAAC;AACD;AACA,SAAS,WAAW,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACnD,EAAE,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC;AAC/D,CAAC;AACD;AACA,SAAS,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACjD,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC;AACrF,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AAC3E;AACA,EAAE,IAAI,MAAM,KAAK,SAAS,EAAE;AAC5B,IAAI,QAAQ,GAAG,OAAM;AACrB,IAAI,MAAM,GAAG,IAAI,CAAC,OAAM;AACxB,IAAI,MAAM,GAAG,EAAC;AACd;AACA,GAAG,MAAM,IAAI,MAAM,KAAK,SAAS,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AACjE,IAAI,QAAQ,GAAG,OAAM;AACrB,IAAI,MAAM,GAAG,IAAI,CAAC,OAAM;AACxB,IAAI,MAAM,GAAG,EAAC;AACd;AACA,GAAG,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC/B,IAAI,MAAM,GAAG,MAAM,KAAK,EAAC;AACzB,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC1B,MAAM,MAAM,GAAG,MAAM,KAAK,EAAC;AAC3B,MAAM,IAAI,QAAQ,KAAK,SAAS,EAAE,QAAQ,GAAG,OAAM;AACnD,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,OAAM;AACvB,MAAM,MAAM,GAAG,UAAS;AACxB,KAAK;AACL,GAAG,MAAM;AACT,IAAI,MAAM,IAAI,KAAK;AACnB,MAAM,yEAAyE;AAC/E,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,OAAM;AACxC,EAAE,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,GAAG,SAAS,EAAE,MAAM,GAAG,UAAS;AACpE;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;AACjF,IAAI,MAAM,IAAI,UAAU,CAAC,wCAAwC,CAAC;AAClE,GAAG;AACH;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,GAAG,OAAM;AAClC;AACA,EAAE,IAAI,WAAW,GAAG,MAAK;AACzB,EAAE,SAAS;AACX,IAAI,QAAQ,QAAQ;AACpB,MAAM,KAAK,KAAK;AAChB,QAAQ,OAAO,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;AACrD;AACA,MAAM,KAAK,MAAM,CAAC;AAClB,MAAM,KAAK,OAAO;AAClB,QAAQ,OAAO,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;AACtD;AACA,MAAM,KAAK,OAAO,CAAC;AACnB,MAAM,KAAK,QAAQ,CAAC;AACpB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;AACvD;AACA,MAAM,KAAK,QAAQ;AACnB;AACA,QAAQ,OAAO,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;AACxD;AACA,MAAM,KAAK,MAAM,CAAC;AAClB,MAAM,KAAK,OAAO,CAAC;AACnB,MAAM,KAAK,SAAS,CAAC;AACrB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;AACtD;AACA,MAAM;AACN,QAAQ,IAAI,WAAW,EAAE,MAAM,IAAI,SAAS,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AAC7E,QAAQ,QAAQ,GAAG,CAAC,EAAE,GAAG,QAAQ,EAAE,WAAW,GAAE;AAChD,QAAQ,WAAW,GAAG,KAAI;AAC1B,KAAK;AACL,GAAG;AACH,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,IAAI;AAC7C,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;AAC1D,GAAG;AACH,EAAC;AACD;AACA,SAAS,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;AACvC,EAAE,IAAI,KAAK,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,EAAE;AACzC,IAAI,OAAO,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,OAAO,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACtD,GAAG;AACH,CAAC;AACD;AACA,SAAS,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;AACrC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAC;AACjC,EAAE,MAAM,GAAG,GAAG,GAAE;AAChB;AACA,EAAE,IAAI,CAAC,GAAG,MAAK;AACf,EAAE,OAAO,CAAC,GAAG,GAAG,EAAE;AAClB,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,EAAC;AAC5B,IAAI,IAAI,SAAS,GAAG,KAAI;AACxB,IAAI,IAAI,gBAAgB,GAAG,CAAC,SAAS,GAAG,IAAI;AAC5C,QAAQ,CAAC;AACT,QAAQ,CAAC,SAAS,GAAG,IAAI;AACzB,YAAY,CAAC;AACb,YAAY,CAAC,SAAS,GAAG,IAAI;AAC7B,gBAAgB,CAAC;AACjB,gBAAgB,EAAC;AACjB;AACA,IAAI,IAAI,CAAC,GAAG,gBAAgB,IAAI,GAAG,EAAE;AACrC,MAAM,IAAI,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,cAAa;AAC1D;AACA,MAAM,QAAQ,gBAAgB;AAC9B,QAAQ,KAAK,CAAC;AACd,UAAU,IAAI,SAAS,GAAG,IAAI,EAAE;AAChC,YAAY,SAAS,GAAG,UAAS;AACjC,WAAW;AACX,UAAU,KAAK;AACf,QAAQ,KAAK,CAAC;AACd,UAAU,UAAU,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAC;AACjC,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,IAAI,EAAE;AAC5C,YAAY,aAAa,GAAG,CAAC,SAAS,GAAG,IAAI,KAAK,GAAG,IAAI,UAAU,GAAG,IAAI,EAAC;AAC3E,YAAY,IAAI,aAAa,GAAG,IAAI,EAAE;AACtC,cAAc,SAAS,GAAG,cAAa;AACvC,aAAa;AACb,WAAW;AACX,UAAU,KAAK;AACf,QAAQ,KAAK,CAAC;AACd,UAAU,UAAU,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAC;AACjC,UAAU,SAAS,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAC;AAChC,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,IAAI,EAAE;AAC3E,YAAY,aAAa,GAAG,CAAC,SAAS,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC,UAAU,GAAG,IAAI,KAAK,GAAG,IAAI,SAAS,GAAG,IAAI,EAAC;AACtG,YAAY,IAAI,aAAa,GAAG,KAAK,KAAK,aAAa,GAAG,MAAM,IAAI,aAAa,GAAG,MAAM,CAAC,EAAE;AAC7F,cAAc,SAAS,GAAG,cAAa;AACvC,aAAa;AACb,WAAW;AACX,UAAU,KAAK;AACf,QAAQ,KAAK,CAAC;AACd,UAAU,UAAU,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAC;AACjC,UAAU,SAAS,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAC;AAChC,UAAU,UAAU,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAC;AACjC,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,IAAI,EAAE;AAC3G,YAAY,aAAa,GAAG,CAAC,SAAS,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,UAAU,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,SAAS,GAAG,IAAI,KAAK,GAAG,IAAI,UAAU,GAAG,IAAI,EAAC;AACpI,YAAY,IAAI,aAAa,GAAG,MAAM,IAAI,aAAa,GAAG,QAAQ,EAAE;AACpE,cAAc,SAAS,GAAG,cAAa;AACvC,aAAa;AACb,WAAW;AACX,OAAO;AACP,KAAK;AACL;AACA,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;AAC5B;AACA;AACA,MAAM,SAAS,GAAG,OAAM;AACxB,MAAM,gBAAgB,GAAG,EAAC;AAC1B,KAAK,MAAM,IAAI,SAAS,GAAG,MAAM,EAAE;AACnC;AACA,MAAM,SAAS,IAAI,QAAO;AAC1B,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,GAAG,KAAK,GAAG,MAAM,EAAC;AACjD,MAAM,SAAS,GAAG,MAAM,GAAG,SAAS,GAAG,MAAK;AAC5C,KAAK;AACL;AACA,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,EAAC;AACvB,IAAI,CAAC,IAAI,iBAAgB;AACzB,GAAG;AACH;AACA,EAAE,OAAO,qBAAqB,CAAC,GAAG,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,oBAAoB,GAAG,OAAM;AACnC;AACA,SAAS,qBAAqB,EAAE,UAAU,EAAE;AAC5C,EAAE,MAAM,GAAG,GAAG,UAAU,CAAC,OAAM;AAC/B,EAAE,IAAI,GAAG,IAAI,oBAAoB,EAAE;AACnC,IAAI,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC;AACxD,GAAG;AACH;AACA;AACA,EAAE,IAAI,GAAG,GAAG,GAAE;AACd,EAAE,IAAI,CAAC,GAAG,EAAC;AACX,EAAE,OAAO,CAAC,GAAG,GAAG,EAAE;AAClB,IAAI,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK;AACpC,MAAM,MAAM;AACZ,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,oBAAoB,CAAC;AACpD,MAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,UAAU,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;AACtC,EAAE,IAAI,GAAG,GAAG,GAAE;AACd,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAC;AACjC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AACpC,IAAI,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,EAAC;AAC7C,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;AACvC,EAAE,IAAI,GAAG,GAAG,GAAE;AACd,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAC;AACjC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AACpC,IAAI,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AACtC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;AACpC,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,OAAM;AACxB;AACA,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,EAAC;AACpC,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,IAAG;AAC7C;AACA,EAAE,IAAI,GAAG,GAAG,GAAE;AACd,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AACpC,IAAI,GAAG,IAAI,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AACtC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,YAAY,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;AACxC,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAC;AACrC,EAAE,IAAI,GAAG,GAAG,GAAE;AACd;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AAChD,IAAI,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAC;AAC/D,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE;AACrD,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,OAAM;AACzB,EAAE,KAAK,GAAG,CAAC,CAAC,MAAK;AACjB,EAAE,GAAG,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC,IAAG;AACvC;AACA,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE;AACjB,IAAI,KAAK,IAAI,IAAG;AAChB,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,EAAC;AAC5B,GAAG,MAAM,IAAI,KAAK,GAAG,GAAG,EAAE;AAC1B,IAAI,KAAK,GAAG,IAAG;AACf,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE;AACf,IAAI,GAAG,IAAI,IAAG;AACd,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,EAAC;AACxB,GAAG,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE;AACxB,IAAI,GAAG,GAAG,IAAG;AACb,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,MAAK;AAC9B;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAC;AAC1C;AACA,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,EAAC;AACjD;AACA,EAAE,OAAO,MAAM;AACf,EAAC;AACD;AACA;AACA;AACA;AACA,SAAS,WAAW,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;AAC3C,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC;AAClF,EAAE,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,EAAE,MAAM,IAAI,UAAU,CAAC,uCAAuC,CAAC;AAC1F,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,UAAU;AAC3B,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE;AACjF,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,UAAU,GAAG,UAAU,KAAK,EAAC;AAC/B,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAC;AAC7D;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAC;AACxB,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,CAAC,GAAG,EAAC;AACX,EAAE,OAAO,EAAE,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;AAC7C,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAG;AACjC,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,UAAU;AAC3B,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE;AACjF,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,UAAU,GAAG,UAAU,KAAK,EAAC;AAC/B,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAC;AAChD,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,UAAU,EAAC;AACvC,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,OAAO,UAAU,GAAG,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;AAC3C,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,UAAU,CAAC,GAAG,IAAG;AAC5C,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,SAAS;AAC1B,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE;AACnE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC;AACrB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY;AAC7B,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE;AACzE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC/C,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY;AAC7B,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE;AACzE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY;AAC7B,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE;AACzE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD;AACA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACvB,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC9B,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;AACpC,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY;AAC7B,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE;AACzE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD;AACA,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS;AAClC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE;AAC5B,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,eAAe,GAAG,kBAAkB,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE;AACxF,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAC;AAClC,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAC;AAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAC;AAC/B,EAAE,IAAI,KAAK,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;AACjD,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAC;AACxC,GAAG;AACH;AACA,EAAE,MAAM,EAAE,GAAG,KAAK;AAClB,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,GAAE;AAC5B;AACA,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC;AAC3B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,GAAG,CAAC,IAAI,GAAE;AAClB;AACA,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC;AAChD,CAAC,EAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,eAAe,GAAG,kBAAkB,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE;AACxF,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAC;AAClC,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAC;AAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAC;AAC/B,EAAE,IAAI,KAAK,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;AACjD,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAC;AACxC,GAAG;AACH;AACA,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,EAAE,MAAM,EAAC;AAClB;AACA,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AAC3B,IAAI,KAAI;AACR;AACA,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC;AAChD,CAAC,EAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE;AAC/E,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,UAAU,GAAG,UAAU,KAAK,EAAC;AAC/B,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAC;AAC7D;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAC;AACxB,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,CAAC,GAAG,EAAC;AACX,EAAE,OAAO,EAAE,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;AAC7C,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAG;AACjC,GAAG;AACH,EAAE,GAAG,IAAI,KAAI;AACb;AACA,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,EAAC;AACpD;AACA,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE;AAC/E,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,UAAU,GAAG,UAAU,KAAK,EAAC;AAC/B,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAC;AAC7D;AACA,EAAE,IAAI,CAAC,GAAG,WAAU;AACpB,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,EAAC;AAC9B,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;AAClC,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,IAAG;AACnC,GAAG;AACH,EAAE,GAAG,IAAI,KAAI;AACb;AACA,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,EAAC;AACpD;AACA,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE;AACjE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC;AACnD,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACzC,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE;AACvE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,EAAC;AACpD,EAAE,OAAO,CAAC,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,UAAU,GAAG,GAAG;AAChD,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE;AACvE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC;AACpD,EAAE,OAAO,CAAC,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,UAAU,GAAG,GAAG;AAChD,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE;AACvE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD;AACA,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACtB,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3B,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC5B,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC5B,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE;AACvE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD;AACA,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AAC5B,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC5B,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3B,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACtB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,cAAc,GAAG,kBAAkB,CAAC,SAAS,cAAc,EAAE,MAAM,EAAE;AACtF,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAC;AAClC,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAC;AAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAC;AAC/B,EAAE,IAAI,KAAK,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;AACjD,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAC;AACxC,GAAG;AACH;AACA,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;AAC9B,KAAK,IAAI,IAAI,EAAE,EAAC;AAChB;AACA,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC;AACnC,IAAI,MAAM,CAAC,KAAK;AAChB,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AAC7B,CAAC,EAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,cAAc,GAAG,kBAAkB,CAAC,SAAS,cAAc,EAAE,MAAM,EAAE;AACtF,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAC;AAClC,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAC;AAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAC;AAC/B,EAAE,IAAI,KAAK,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;AACjD,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAC;AACxC,GAAG;AACH;AACA,EAAE,MAAM,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,EAAE,MAAM,EAAC;AAClB;AACA,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC;AACnC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AACnC,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC;AACT,CAAC,EAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE;AACvE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,OAAOD,SAAO,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;AAChD,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE;AACvE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,OAAOA,SAAO,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE;AACzE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,OAAOA,SAAO,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;AAChD,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE;AACzE,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAC;AACpD,EAAE,OAAOA,SAAO,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,EAAC;AACD;AACA,SAAS,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtD,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,6CAA6C,CAAC;AAC/F,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,EAAE,MAAM,IAAI,UAAU,CAAC,mCAAmC,CAAC;AAC3F,EAAE,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC;AAC3E,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,WAAW;AAC5B,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE;AAC1F,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,UAAU,GAAG,UAAU,KAAK,EAAC;AAC/B,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,GAAG,EAAC;AACpD,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,EAAC;AAC1D,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,CAAC,GAAG,EAAC;AACX,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,KAAI;AAC7B,EAAE,OAAO,EAAE,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;AAC7C,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,IAAI,KAAI;AAC3C,GAAG;AACH;AACA,EAAE,OAAO,MAAM,GAAG,UAAU;AAC5B,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,WAAW;AAC5B,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE;AAC1F,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,UAAU,GAAG,UAAU,KAAK,EAAC;AAC/B,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,GAAG,EAAC;AACpD,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,EAAC;AAC1D,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,UAAU,GAAG,EAAC;AACxB,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,KAAI;AACjC,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;AACrC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,IAAI,KAAI;AAC3C,GAAG;AACH;AACA,EAAE,OAAO,MAAM,GAAG,UAAU;AAC5B,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,UAAU;AAC3B,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAC5E,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAC;AAC1D,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AAC/B,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,aAAa;AAC9B,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAClF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAC;AAC5D,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AAC/B,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,EAAC;AAClC,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,aAAa;AAC9B,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAClF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAC;AAC5D,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAAC;AAC9B,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AACnC,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,aAAa;AAC9B,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAClF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAC;AAChE,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE,EAAC;AACnC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE,EAAC;AACnC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,EAAC;AAClC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AAC/B,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,aAAa;AAC9B,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAClF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAC;AAChE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,EAAC;AAC/B,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE,EAAC;AACnC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,EAAC;AAClC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AACnC,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,SAAS,cAAc,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;AACvD,EAAE,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAC;AAC7C;AACA,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,EAAC;AAC7C,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAE;AACpB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAE;AACpB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAE;AACpB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAE;AACpB,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,EAAC;AAC3D,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAE;AACpB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAE;AACpB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAE;AACpB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAE;AACpB,EAAE,OAAO,MAAM;AACf,CAAC;AACD;AACA,SAAS,cAAc,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;AACvD,EAAE,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAC;AAC7C;AACA,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,EAAC;AAC7C,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAE;AACtB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAE;AACtB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAE;AACtB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAE;AACtB,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,EAAC;AAC3D,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAE;AACtB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAE;AACtB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAE;AACtB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAC;AACd,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,GAAE;AAClB,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,SAAS,gBAAgB,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE;AACrG,EAAE,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACrF,CAAC,EAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,SAAS,gBAAgB,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE;AACrG,EAAE,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACrF,CAAC,EAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE;AACxF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,EAAC;AACnD;AACA,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,KAAK,EAAC;AAChE,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,EAAC;AACX,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,KAAI;AAC7B,EAAE,OAAO,EAAE,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;AAC7C,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;AAC9D,MAAM,GAAG,GAAG,EAAC;AACb,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,GAAG,KAAI;AACxD,GAAG;AACH;AACA,EAAE,OAAO,MAAM,GAAG,UAAU;AAC5B,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE;AACxF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,EAAC;AACnD;AACA,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,KAAK,EAAC;AAChE,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,UAAU,GAAG,EAAC;AACxB,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,GAAG,GAAG,EAAC;AACb,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,KAAI;AACjC,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;AAC9D,MAAM,GAAG,GAAG,EAAC;AACb,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,GAAG,KAAI;AACxD,GAAG;AACH;AACA,EAAE,OAAO,MAAM,GAAG,UAAU;AAC5B,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAC1E,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,EAAC;AAC9D,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,EAAC;AACzC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AAC/B,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAChF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,EAAC;AAClE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AAC/B,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,EAAC;AAClC,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAChF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,EAAC;AAClE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAAC;AAC9B,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AACnC,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAChF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,UAAU,EAAC;AAC1E,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AAC/B,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,EAAC;AAClC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE,EAAC;AACnC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE,EAAC;AACnC,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAChF,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,UAAU,EAAC;AAC1E,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,KAAK,GAAG,EAAC;AAC/C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,EAAC;AAC/B,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE,EAAC;AACnC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,EAAC;AAClC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,EAAC;AACnC,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,eAAe,GAAG,kBAAkB,CAAC,SAAS,eAAe,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE;AACnG,EAAE,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzG,CAAC,EAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,eAAe,GAAG,kBAAkB,CAAC,SAAS,eAAe,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE;AACnG,EAAE,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzG,CAAC,EAAC;AACF;AACA,SAAS,YAAY,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC1D,EAAE,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC;AAC3E,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC;AAC5D,CAAC;AACD;AACA,SAAS,UAAU,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE;AACjE,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAkD,EAAC;AACxF,GAAG;AACH,EAAEA,SAAO,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,EAAC;AACxD,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAChF,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC;AACxD,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAChF,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC;AACzD,EAAC;AACD;AACA,SAAS,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE;AAClE,EAAE,KAAK,GAAG,CAAC,MAAK;AAChB,EAAE,MAAM,GAAG,MAAM,KAAK,EAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAoD,EAAC;AAC1F,GAAG;AACH,EAAEA,SAAO,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,EAAC;AACxD,EAAE,OAAO,MAAM,GAAG,CAAC;AACnB,CAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAClF,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC;AACzD,EAAC;AACD;AACA,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;AAClF,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC;AAC1D,EAAC;AACD;AACA;AACA,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE;AACxE,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC;AAClF,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,EAAC;AACvB,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,OAAM;AAC1C,EAAE,IAAI,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,CAAC,OAAM;AAC/D,EAAE,IAAI,CAAC,WAAW,EAAE,WAAW,GAAG,EAAC;AACnC,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,MAAK;AACzC;AACA;AACA,EAAE,IAAI,GAAG,KAAK,KAAK,EAAE,OAAO,CAAC;AAC7B,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;AACxD;AACA;AACA,EAAE,IAAI,WAAW,GAAG,CAAC,EAAE;AACvB,IAAI,MAAM,IAAI,UAAU,CAAC,2BAA2B,CAAC;AACrD,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC;AACnF,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC;AAC9D;AACA;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,OAAM;AAC1C,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,WAAW,GAAG,GAAG,GAAG,KAAK,EAAE;AACjD,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,WAAW,GAAG,MAAK;AAC7C,GAAG;AACH;AACA,EAAE,MAAM,GAAG,GAAG,GAAG,GAAG,MAAK;AACzB;AACA,EAAE,IAAI,IAAI,KAAK,MAAM,IAAI,OAAO,UAAU,CAAC,SAAS,CAAC,UAAU,KAAK,UAAU,EAAE;AAChF;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,EAAC;AAC5C,GAAG,MAAM;AACT,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;AACjC,MAAM,MAAM;AACZ,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;AAC/B,MAAM,WAAW;AACjB,MAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE;AAClE;AACA,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,MAAM,QAAQ,GAAG,MAAK;AACtB,MAAM,KAAK,GAAG,EAAC;AACf,MAAM,GAAG,GAAG,IAAI,CAAC,OAAM;AACvB,KAAK,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACxC,MAAM,QAAQ,GAAG,IAAG;AACpB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAM;AACvB,KAAK;AACL,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AAChE,MAAM,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC;AACtD,KAAK;AACL,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACtE,MAAM,MAAM,IAAI,SAAS,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AAC1D,KAAK;AACL,IAAI,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,EAAC;AACpC,MAAM,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,GAAG,GAAG;AAC5C,UAAU,QAAQ,KAAK,QAAQ,EAAE;AACjC;AACA,QAAQ,GAAG,GAAG,KAAI;AAClB,OAAO;AACP,KAAK;AACL,GAAG,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACtC,IAAI,GAAG,GAAG,GAAG,GAAG,IAAG;AACnB,GAAG,MAAM,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE;AACvC,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,EAAC;AACrB,GAAG;AACH;AACA;AACA,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;AAC7D,IAAI,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC;AAC9C,GAAG;AACH;AACA,EAAE,IAAI,GAAG,IAAI,KAAK,EAAE;AACpB,IAAI,OAAO,IAAI;AACf,GAAG;AACH;AACA,EAAE,KAAK,GAAG,KAAK,KAAK,EAAC;AACrB,EAAE,GAAG,GAAG,GAAG,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,KAAK,EAAC;AACnD;AACA,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,EAAC;AACnB;AACA,EAAE,IAAI,EAAC;AACP,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AAClC,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,IAAG;AACnB,KAAK;AACL,GAAG,MAAM;AACT,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;AACtC,QAAQ,GAAG;AACX,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAC;AAClC,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,OAAM;AAC5B,IAAI,IAAI,GAAG,KAAK,CAAC,EAAE;AACnB,MAAM,MAAM,IAAI,SAAS,CAAC,aAAa,GAAG,GAAG;AAC7C,QAAQ,mCAAmC,CAAC;AAC5C,KAAK;AACL,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;AACtC,MAAM,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,EAAC;AACtC,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,IAAI;AACb,EAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAG,GAAE;AACjB,SAAS,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE;AACnC,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,SAAS,SAAS,IAAI,CAAC;AAC7C,IAAI,WAAW,CAAC,GAAG;AACnB,MAAM,KAAK,GAAE;AACb;AACA,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;AAC7C,QAAQ,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAChD,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,YAAY,EAAE,IAAI;AAC1B,OAAO,EAAC;AACR;AACA;AACA,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAC;AACzC;AACA;AACA,MAAM,IAAI,CAAC,MAAK;AAChB;AACA,MAAM,OAAO,IAAI,CAAC,KAAI;AACtB,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC,GAAG;AAChB,MAAM,OAAO,GAAG;AAChB,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC,CAAC,KAAK,EAAE;AACrB,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;AAC1C,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,KAAK;AACb,QAAQ,QAAQ,EAAE,IAAI;AACtB,OAAO,EAAC;AACR,KAAK;AACL;AACA,IAAI,QAAQ,CAAC,GAAG;AAChB,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AACrD,KAAK;AACL,IAAG;AACH,CAAC;AACD;AACA,CAAC,CAAC,0BAA0B;AAC5B,EAAE,UAAU,IAAI,EAAE;AAClB,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,4BAA4B,CAAC;AAClD,KAAK;AACL;AACA,IAAI,OAAO,gDAAgD;AAC3D,GAAG,EAAE,UAAU,EAAC;AAChB,CAAC,CAAC,sBAAsB;AACxB,EAAE,UAAU,IAAI,EAAE,MAAM,EAAE;AAC1B,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,iDAAiD,EAAE,OAAO,MAAM,CAAC,CAAC;AAC1F,GAAG,EAAE,SAAS,EAAC;AACf,CAAC,CAAC,kBAAkB;AACpB,EAAE,UAAU,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE;AAC/B,IAAI,IAAI,GAAG,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,kBAAkB,EAAC;AACtD,IAAI,IAAI,QAAQ,GAAG,MAAK;AACxB,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE;AAC9D,MAAM,QAAQ,GAAG,qBAAqB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAC;AACrD,KAAK,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,EAAC;AAC9B,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;AACjF,QAAQ,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,EAAC;AAClD,OAAO;AACP,MAAM,QAAQ,IAAI,IAAG;AACrB,KAAK;AACL,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAC;AACvD,IAAI,OAAO,GAAG;AACd,GAAG,EAAE,UAAU,EAAC;AAChB;AACA,SAAS,qBAAqB,EAAE,GAAG,EAAE;AACrC,EAAE,IAAI,GAAG,GAAG,GAAE;AACd,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,OAAM;AACpB,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,EAAC;AACtC,EAAE,OAAO,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACjC,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;AACzC,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,WAAW,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE;AAC/C,EAAE,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAC;AAClC,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,KAAK,SAAS,EAAE;AAC3E,IAAI,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,UAAU,GAAG,CAAC,CAAC,EAAC;AACtD,GAAG;AACH,CAAC;AACD;AACA,SAAS,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE;AAC/D,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,EAAE;AAClC,IAAI,MAAM,CAAC,GAAG,OAAO,GAAG,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAE;AAChD,IAAI,IAAI,MAAK;AACb,IAAI,IAAI,UAAU,GAAG,CAAC,EAAE;AACxB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;AAC1C,QAAQ,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC;AACrE,OAAO,MAAM;AACb,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC;AAC5E,gBAAgB,CAAC,EAAE,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC;AACjD,OAAO;AACP,KAAK,MAAM;AACX,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAC;AAC/C,KAAK;AACL,IAAI,MAAM,IAAI,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;AAC5D,GAAG;AACH,EAAE,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,EAAC;AACtC,CAAC;AACD;AACA,SAAS,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE;AACtC,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjC,IAAI,MAAM,IAAI,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC;AAChE,GAAG;AACH,CAAC;AACD;AACA,SAAS,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;AAC3C,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,EAAE;AACnC,IAAI,cAAc,CAAC,KAAK,EAAE,IAAI,EAAC;AAC/B,IAAI,MAAM,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,IAAI,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC;AAC5E,GAAG;AACH;AACA,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;AAClB,IAAI,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE;AAC/C,GAAG;AACH;AACA,EAAE,MAAM,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,IAAI,QAAQ;AACpD,oCAAoC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACzE,oCAAoC,KAAK,CAAC;AAC1C,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,iBAAiB,GAAG,oBAAmB;AAC7C;AACA,SAAS,WAAW,EAAE,GAAG,EAAE;AAC3B;AACA,EAAE,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AACzB;AACA,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,EAAC;AACjD;AACA,EAAE,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE;AAC/B;AACA,EAAE,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;AAC/B,IAAI,GAAG,GAAG,GAAG,GAAG,IAAG;AACnB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,CAAC;AACD;AACA,SAAS,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE;AACrC,EAAE,KAAK,GAAG,KAAK,IAAI,SAAQ;AAC3B,EAAE,IAAI,UAAS;AACf,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,OAAM;AAC9B,EAAE,IAAI,aAAa,GAAG,KAAI;AAC1B,EAAE,MAAM,KAAK,GAAG,GAAE;AAClB;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;AACnC,IAAI,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,EAAC;AACpC;AACA;AACA,IAAI,IAAI,SAAS,GAAG,MAAM,IAAI,SAAS,GAAG,MAAM,EAAE;AAClD;AACA,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B;AACA,QAAQ,IAAI,SAAS,GAAG,MAAM,EAAE;AAChC;AACA,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC;AAC7D,UAAU,QAAQ;AAClB,SAAS,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE;AACrC;AACA,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC;AAC7D,UAAU,QAAQ;AAClB,SAAS;AACT;AACA;AACA,QAAQ,aAAa,GAAG,UAAS;AACjC;AACA,QAAQ,QAAQ;AAChB,OAAO;AACP;AACA;AACA,MAAM,IAAI,SAAS,GAAG,MAAM,EAAE;AAC9B,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC;AAC3D,QAAQ,aAAa,GAAG,UAAS;AACjC,QAAQ,QAAQ;AAChB,OAAO;AACP;AACA;AACA,MAAM,SAAS,GAAG,CAAC,aAAa,GAAG,MAAM,IAAI,EAAE,GAAG,SAAS,GAAG,MAAM,IAAI,QAAO;AAC/E,KAAK,MAAM,IAAI,aAAa,EAAE;AAC9B;AACA,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC;AACzD,KAAK;AACL;AACA,IAAI,aAAa,GAAG,KAAI;AACxB;AACA;AACA,IAAI,IAAI,SAAS,GAAG,IAAI,EAAE;AAC1B,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK;AACjC,MAAM,KAAK,CAAC,IAAI,CAAC,SAAS,EAAC;AAC3B,KAAK,MAAM,IAAI,SAAS,GAAG,KAAK,EAAE;AAClC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK;AACjC,MAAM,KAAK,CAAC,IAAI;AAChB,QAAQ,SAAS,IAAI,GAAG,GAAG,IAAI;AAC/B,QAAQ,SAAS,GAAG,IAAI,GAAG,IAAI;AAC/B,QAAO;AACP,KAAK,MAAM,IAAI,SAAS,GAAG,OAAO,EAAE;AACpC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK;AACjC,MAAM,KAAK,CAAC,IAAI;AAChB,QAAQ,SAAS,IAAI,GAAG,GAAG,IAAI;AAC/B,QAAQ,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI;AACtC,QAAQ,SAAS,GAAG,IAAI,GAAG,IAAI;AAC/B,QAAO;AACP,KAAK,MAAM,IAAI,SAAS,GAAG,QAAQ,EAAE;AACrC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK;AACjC,MAAM,KAAK,CAAC,IAAI;AAChB,QAAQ,SAAS,IAAI,IAAI,GAAG,IAAI;AAChC,QAAQ,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI;AACtC,QAAQ,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI;AACtC,QAAQ,SAAS,GAAG,IAAI,GAAG,IAAI;AAC/B,QAAO;AACP,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC;AAC3C,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,KAAK;AACd,CAAC;AACD;AACA,SAAS,YAAY,EAAE,GAAG,EAAE;AAC5B,EAAE,MAAM,SAAS,GAAG,GAAE;AACtB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACvC;AACA,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,EAAC;AAC5C,GAAG;AACH,EAAE,OAAO,SAAS;AAClB,CAAC;AACD;AACA,SAAS,cAAc,EAAE,GAAG,EAAE,KAAK,EAAE;AACrC,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,GAAE;AACf,EAAE,MAAM,SAAS,GAAG,GAAE;AACtB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACvC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK;AAC/B;AACA,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,EAAC;AACzB,IAAI,EAAE,GAAG,CAAC,IAAI,EAAC;AACf,IAAI,EAAE,GAAG,CAAC,GAAG,IAAG;AAChB,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,EAAC;AACtB,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,EAAC;AACtB,GAAG;AACH;AACA,EAAE,OAAO,SAAS;AAClB,CAAC;AACD;AACA,SAAS,aAAa,EAAE,GAAG,EAAE;AAC7B,EAAE,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC7C,CAAC;AACD;AACA,SAAS,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE;AAC/C,EAAE,IAAI,EAAC;AACP,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;AAC/B,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK;AAC9D,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EAAC;AAC5B,GAAG;AACH,EAAE,OAAO,CAAC;AACV,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE;AAChC,EAAE,OAAO,GAAG,YAAY,IAAI;AAC5B,KAAK,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,WAAW,IAAI,IAAI,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI,IAAI;AAC3E,MAAM,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;AACzC,CAAC;AACD,SAAS,WAAW,EAAE,GAAG,EAAE;AAC3B;AACA,EAAE,OAAO,GAAG,KAAK,GAAG;AACpB,CAAC;AACD;AACA;AACA;AACA,MAAM,mBAAmB,GAAG,CAAC,YAAY;AACzC,EAAE,MAAM,QAAQ,GAAG,mBAAkB;AACrC,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,EAAC;AAC9B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;AAC/B,IAAI,MAAM,GAAG,GAAG,CAAC,GAAG,GAAE;AACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;AACjC,MAAM,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAC;AAChD,KAAK;AACL,GAAG;AACH,EAAE,OAAO,KAAK;AACd,CAAC,IAAG;AACJ;AACA;AACA,SAAS,kBAAkB,EAAE,EAAE,EAAE;AACjC,EAAE,OAAO,OAAO,MAAM,KAAK,WAAW,GAAG,sBAAsB,GAAG,EAAE;AACpE,CAAC;AACD;AACA,SAAS,sBAAsB,IAAI;AACnC,EAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC;AACzC;;;ACzjEA,eAAe,CAAC,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM;AACtD,EAAE,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI;AACpC,EAAE,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,EAAE;;ACD7C,IAAI,QAAQ,CAAC;AACb,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC;AACxC,EAAE,QAAQ,GAAG,SAAS,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE;AAChD;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,UAAS;AAC3B,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE;AACxD,MAAM,WAAW,EAAE;AACnB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,YAAY,EAAE,IAAI;AAC1B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,CAAC,MAAM;AACP,EAAE,QAAQ,GAAG,SAAS,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE;AAChD,IAAI,IAAI,CAAC,MAAM,GAAG,UAAS;AAC3B,IAAI,IAAI,QAAQ,GAAG,YAAY,GAAE;AACjC,IAAI,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,UAAS;AAC5C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,GAAE;AACnC,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,KAAI;AACrC,IAAG;AACH,CAAC;AACD,iBAAe,QAAQ;;AC4FvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,SAAO,CAAC,GAAG,EAAE,IAAI,EAAE;AACnC;AACA,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,IAAI,EAAE,EAAE;AACZ,IAAI,OAAO,EAAE,cAAc;AAC3B,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACtD,EAAE,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACvD,EAAE,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;AACvB;AACA,IAAI,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC;AAC1B,GAAG,MAAM,IAAI,IAAI,EAAE;AACnB;AACA,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACvB,GAAG;AACH;AACA,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,UAAU,GAAG,KAAK,CAAC;AAC1D,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5C,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;AAClD,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC;AAC/D,EAAE,IAAI,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,OAAO,GAAG,gBAAgB,CAAC;AACjD,EAAE,OAAO,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1C,CAAC;AACD;AACA;AACAA,SAAO,CAAC,MAAM,GAAG;AACjB,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AAClB,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AACpB,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AACvB,EAAE,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AACrB,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACpB,EAAE,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACnB,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACpB,EAAE,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACnB,EAAE,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACnB,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACpB,EAAE,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACtB,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AAClB,EAAE,QAAQ,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACrB,CAAC,CAAC;AACF;AACA;AACAA,SAAO,CAAC,MAAM,GAAG;AACjB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,MAAM,EAAE,SAAS;AACnB;AACA,EAAE,QAAQ,EAAE,KAAK;AACjB,CAAC,CAAC;AACF;AACA;AACA,SAAS,gBAAgB,CAAC,GAAG,EAAE,SAAS,EAAE;AAC1C,EAAE,IAAI,KAAK,GAAGA,SAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACxC;AACA,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,OAAO,SAAS,GAAGA,SAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;AAC3D,WAAW,SAAS,GAAGA,SAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtD,GAAG,MAAM;AACT,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,CAAC;AACD;AACA;AACA,SAAS,cAAc,CAAC,GAAG,EAAE,SAAS,EAAE;AACxC,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB;AACA,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE;AACnC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACrB,GAAG,CAAC,CAAC;AACL;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA;AACA,SAAS,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE;AAC/C;AACA;AACA,EAAE,IAAI,GAAG,CAAC,aAAa;AACvB,MAAM,KAAK;AACX,MAAM,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC;AAC/B;AACA,MAAM,KAAK,CAAC,OAAO,KAAKA,SAAO;AAC/B;AACA,MAAM,EAAE,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,SAAS,KAAK,KAAK,CAAC,EAAE;AACrE,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACxB,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH;AACA;AACA,EAAE,IAAI,SAAS,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9C,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,EAAE,IAAI,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AACtC;AACA,EAAE,IAAI,GAAG,CAAC,UAAU,EAAE;AACtB,IAAI,IAAI,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC;AACpB,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE;AAC7E,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;AAC9B,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,IAAI,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AAC3B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;AACrD,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,GAAG,GAAG,EAAE,SAAS,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACzB,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC1E,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;AACvB,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;AACtE,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,MAAM,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,EAAE,KAAK,GAAG,KAAK,EAAE,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpD;AACA;AACA,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AACtB,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,GAAG;AACH;AACA;AACA,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AACzB,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;AAChD,IAAI,IAAI,GAAG,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC;AAClC,GAAG;AACH;AACA;AACA,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACvB,IAAI,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvD,GAAG;AACH;AACA;AACA,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;AACrB,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxD,GAAG;AACH;AACA;AACA,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AACtB,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AACpC,GAAG;AACH;AACA,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE;AAC1D,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxC,GAAG;AACH;AACA,EAAE,IAAI,YAAY,GAAG,CAAC,EAAE;AACxB,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACzB,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC1E,KAAK,MAAM;AACX,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AAChD,KAAK;AACL,GAAG;AACH;AACA,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvB;AACA,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,MAAM,GAAG,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACtE,GAAG,MAAM;AACT,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,EAAE;AACpC,MAAM,OAAO,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAC/E,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACjB;AACA,EAAE,OAAO,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA,SAAS,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE;AACrC,EAAE,IAAI,WAAW,CAAC,KAAK,CAAC;AACxB,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACjD,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACvB,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;AACnE,8CAA8C,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AAClE,8CAA8C,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;AAC1E,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC;AACrB,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7C,EAAE,IAAI,SAAS,CAAC,KAAK,CAAC;AACtB,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,EAAE,SAAS,CAAC,CAAC;AAC9C;AACA,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AACnB,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACvC,CAAC;AACD;AACA;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,OAAO,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AAC1D,CAAC;AACD;AACA;AACA,SAAS,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE;AAClE,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;AAChD,IAAI,IAAI,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAC1C,MAAM,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW;AACtE,UAAU,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5B,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE;AAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAC7B,MAAM,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW;AACtE,UAAU,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACtB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACA;AACA,SAAS,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE;AAC3E,EAAE,IAAI,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;AACtB,EAAE,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;AAC9E,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE;AAChB,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;AAClB,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;AACtD,KAAK,MAAM;AACX,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG,MAAM;AACT,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;AAClB,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG;AACH,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;AACzC,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,GAAG;AACH,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC1C,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE;AAChC,QAAQ,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACjD,OAAO,MAAM;AACb,QAAQ,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;AAC7D,OAAO;AACP,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AAClC,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;AACnD,YAAY,OAAO,IAAI,GAAG,IAAI,CAAC;AAC/B,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,SAAS,MAAM;AACf,UAAU,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;AAC1D,YAAY,OAAO,KAAK,GAAG,IAAI,CAAC;AAChC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxB,SAAS;AACT,OAAO;AACP,KAAK,MAAM;AACX,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;AACjD,KAAK;AACL,GAAG;AACH,EAAE,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;AACzB,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AACrC,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AACpC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,EAAE;AACpD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACvC,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AACtC,kBAAkB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AACtC,kBAAkB,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC3C,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACzC,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC3B,CAAC;AACD;AACA;AACA,SAAS,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;AAEpD,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,GAAG,EAAE;AAEjD,IAAI,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAc;AAC9C,IAAI,OAAO,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAChE,GAAG,EAAE,CAAC,CAAC,CAAC;AACR;AACA,EAAE,IAAI,MAAM,GAAG,EAAE,EAAE;AACnB,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC;AACpB,YAAY,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,KAAK,CAAC;AAC5C,WAAW,GAAG;AACd,WAAW,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AAC/B,WAAW,GAAG;AACd,WAAW,MAAM,CAAC,CAAC,CAAC,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC;AACD;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,EAAE,EAAE;AAC5B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC;AACD;AACO,SAAS,SAAS,CAAC,GAAG,EAAE;AAC/B,EAAE,OAAO,OAAO,GAAG,KAAK,SAAS,CAAC;AAClC,CAAC;AACD;AACO,SAAS,MAAM,CAAC,GAAG,EAAE;AAC5B,EAAE,OAAO,GAAG,KAAK,IAAI,CAAC;AACtB,CAAC;AAKD;AACO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAC9B,EAAE,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC;AACjC,CAAC;AACD;AACO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAC9B,EAAE,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC;AACjC,CAAC;AAKD;AACO,SAAS,WAAW,CAAC,GAAG,EAAE;AACjC,EAAE,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC;AACxB,CAAC;AACD;AACO,SAAS,QAAQ,CAAC,EAAE,EAAE;AAC7B,EAAE,OAAO,QAAQ,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,EAAE,CAAC,KAAK,iBAAiB,CAAC;AAClE,CAAC;AACD;AACO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAC9B,EAAE,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,CAAC;AACjD,CAAC;AACD;AACO,SAAS,MAAM,CAAC,CAAC,EAAE;AAC1B,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC;AAC9D,CAAC;AACD;AACO,SAAS,OAAO,CAAC,CAAC,EAAE;AAC3B,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;AACpB,OAAO,cAAc,CAAC,CAAC,CAAC,KAAK,gBAAgB,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC;AACrE,CAAC;AACD;AACO,SAAS,UAAU,CAAC,GAAG,EAAE;AAChC,EAAE,OAAO,OAAO,GAAG,KAAK,UAAU,CAAC;AACnC,CAAC;AACD;AACO,SAAS,WAAW,CAAC,GAAG,EAAE;AACjC,EAAE,OAAO,GAAG,KAAK,IAAI;AACrB,SAAS,OAAO,GAAG,KAAK,SAAS;AACjC,SAAS,OAAO,GAAG,KAAK,QAAQ;AAChC,SAAS,OAAO,GAAG,KAAK,QAAQ;AAChC,SAAS,OAAO,GAAG,KAAK,QAAQ;AAChC,SAAS,OAAO,GAAG,KAAK,WAAW,CAAC;AACpC,CAAC;AAKD;AACA,SAAS,cAAc,CAAC,CAAC,EAAE;AAC3B,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC;AA0CD;AACO,SAAS,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE;AACrC;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,MAAM,CAAC;AAC5C;AACA,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9B,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACtB,EAAE,OAAO,CAAC,EAAE,EAAE;AACd,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CACA;AACA,SAAS,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE;AACnC,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACzD;;AC3jBA,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;AACvB,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACf,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AACnB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AACnB;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AACtD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACvB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACf,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACf,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG;AACH,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAC7C;AACA,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,IAAI,UAAU,GAAG,EAAE;AAC/C,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;AACvB,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AA4BF,IAAI,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;AACnC,IAAI,mBAAmB,CAAC;AACxB,SAAS,kBAAkB,GAAG;AAC9B,EAAE,IAAI,OAAO,mBAAmB,KAAK,WAAW,EAAE;AAClD,IAAI,OAAO,mBAAmB,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,mBAAmB,IAAI,YAAY;AAC5C,IAAI,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC,IAAI,KAAK,KAAK,CAAC;AAC5C,GAAG,EAAE,CAAC,CAAC;AACP,CAAC;AACD,SAAS,SAAS,EAAE,GAAG,EAAE;AACzB,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,CAAC;AACD,SAAS,MAAM,CAAC,MAAM,EAAE;AACxB,EAAE,IAAIC,eAAQ,CAAC,MAAM,CAAC,EAAE;AACxB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,OAAOC,QAAM,CAAC,WAAW,KAAK,UAAU,EAAE;AAChD,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,OAAO,WAAW,CAAC,MAAM,KAAK,UAAU,EAAE;AAChD,IAAI,OAAO,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,MAAM,YAAY,QAAQ,EAAE;AAClC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,YAAY,WAAW,EAAE;AAC7D,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE;AAChC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACnD,CAAC;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,GAAG,6BAA6B,CAAC;AAC1C;AACA,SAAS,OAAO,CAAC,IAAI,EAAE;AACvB,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACzB,IAAI,OAAO;AACX,GAAG;AACH,EAAE,IAAI,kBAAkB,EAAE,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC5B,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE,OAAO,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC;AACD,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC;AAChC,SAAS,cAAc,CAAC,OAAO,EAAE;AACxC,EAAE,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;AAC/B,EAAE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC/B,EAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACnC,EAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACnC,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACnC,IAAI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAClC,GAAG,MAAM;AACT,IAAI,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,GAAG;AACH,EAAE,IAAI,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,IAAI,CAAC;AAC9D,EAAE,IAAI,KAAK,CAAC,iBAAiB,EAAE;AAC/B,IAAI,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AACtD,GAAG,MAAM;AACT;AACA,IAAI,IAAI,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;AAC1B,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE;AACnB,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC;AAC1B;AACA;AACA,MAAM,IAAI,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAChD,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;AAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE;AACpB;AACA;AACA,QAAQ,IAAI,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AACnD,QAAQ,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AAC3C,OAAO;AACP;AACA,MAAM,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AACvB,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACAC,UAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAChC;AACA,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;AACxB,EAAE,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;AAC7B,IAAI,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD,SAAS,OAAO,CAAC,SAAS,EAAE;AAC5B,EAAE,IAAI,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AACtD,IAAI,OAAOC,SAAW,CAAC,SAAS,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,IAAI,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AACnC,EAAE,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,OAAO,GAAG,EAAE,CAAC;AAC3C,EAAE,OAAO,WAAW,IAAI,IAAI,GAAG,GAAG,CAAC;AACnC,CAAC;AACD,SAAS,UAAU,CAAC,IAAI,EAAE;AAC1B,EAAE,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG;AAClD,SAAS,IAAI,CAAC,QAAQ,GAAG,GAAG;AAC5B,SAAS,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,kBAAkB,EAAE;AAC9E,EAAE,MAAM,IAAI,cAAc,CAAC;AAC3B,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,QAAQ,EAAE,QAAQ;AACtB,IAAI,QAAQ,EAAE,QAAQ;AACtB,IAAI,kBAAkB,EAAE,kBAAkB;AAC1C,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACnD,CAAC;AACD,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;AAEf;AACA;AACA;AACA;AACA,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AACd,SAAS,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;AACjD,EAAE,IAAI,MAAM,IAAI,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACvE,CAAC;AACD;AACA;AACA;AACA,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACpB,SAAS,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;AACpD,EAAE,IAAI,MAAM,IAAI,QAAQ,EAAE;AAC1B,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACpD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;AACtB,SAAS,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;AACrD,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE;AAC5C,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AAC5D,GAAG;AACH,CAAC;AACD,MAAM,CAAC,eAAe,GAAG,eAAe,CAAC;AAClC,SAAS,eAAe,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;AAC3D,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE;AAC3C,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;AACxE,GAAG;AACH,CAAC;AACD;AACA,SAAS,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE;AACrD;AACA,EAAE,IAAI,MAAM,KAAK,QAAQ,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,MAAM,IAAIH,eAAQ,CAAC,MAAM,CAAC,IAAIA,eAAQ,CAAC,QAAQ,CAAC,EAAE;AACrD,IAAI,OAAO,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3C;AACA;AACA;AACA,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE;AACjD,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC;AACnD;AACA;AACA;AACA;AACA,GAAG,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACrD,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM;AAC5C,WAAW,MAAM,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM;AAC5C,WAAW,MAAM,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS;AAClD,WAAW,MAAM,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS;AAClD,WAAW,MAAM,CAAC,UAAU,KAAK,QAAQ,CAAC,UAAU,CAAC;AACrD;AACA;AACA;AACA,GAAG,MAAM,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ;AAC3D,cAAc,QAAQ,KAAK,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,CAAC,EAAE;AAClE,IAAI,OAAO,MAAM,GAAG,MAAM,KAAK,QAAQ,GAAG,MAAM,IAAI,QAAQ,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC;AAC/C,aAAa,SAAS,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,QAAQ,CAAC;AACtD,aAAa,EAAE,MAAM,YAAY,YAAY;AAC7C,eAAe,MAAM,YAAY,YAAY,CAAC,EAAE;AAChD,IAAI,OAAO,OAAO,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AAChD,mBAAmB,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,MAAM,IAAIA,eAAQ,CAAC,MAAM,CAAC,KAAKA,eAAQ,CAAC,QAAQ,CAAC,EAAE;AACtD,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;AAChD;AACA,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnD,IAAI,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;AAC5B,MAAM,IAAI,WAAW,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC5D,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,KAAK;AACL;AACA,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClC;AACA,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACrD,GAAG;AACH,CAAC;AACD;AACA,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,oBAAoB,CAAC;AACxE,CAAC;AACD;AACA,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,oBAAoB,EAAE;AACtD,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS;AACpE,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC;AACtC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;AACnB,EAAE,IAAI,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC;AACrE,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAC/B,EAAE,IAAI,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC;AACpD,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvB,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvB,IAAI,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACb;AACA;AACA,EAAE,IAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM;AAC7B,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;AACZ,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;AACZ;AACA,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACvB,MAAM,OAAO,KAAK,CAAC;AACnB,GAAG;AACH;AACA;AACA,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,oBAAoB,CAAC;AACjE,MAAM,OAAO,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA;AACA;AACA,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;AAC5B,SAAS,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;AACxD,EAAE,IAAI,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE;AAC3C,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;AAClE,GAAG;AACH,CAAC;AACD;AACA,MAAM,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AACxC,SAAS,kBAAkB,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;AAC9D,EAAE,IAAI,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE;AAC1C,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;AAC9E,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;AAC1B,SAAS,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;AACvD,EAAE,IAAI,MAAM,KAAK,QAAQ,EAAE;AAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACxD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC;AAChC,SAAS,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;AAC1D,EAAE,IAAI,MAAM,KAAK,QAAQ,EAAE;AAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;AAC3D,GAAG;AACH,CAAC;AACD;AACA,SAAS,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC7C,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE;AAC5B,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,iBAAiB,EAAE;AACrE,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjC,GAAG;AACH;AACA,EAAE,IAAI;AACN,IAAI,IAAI,MAAM,YAAY,QAAQ,EAAE;AACpC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE;AACd;AACA,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;AACrC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC;AAC5C,CAAC;AACD;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI;AACN,IAAI,KAAK,EAAE,CAAC;AACZ,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD;AACA,SAAS,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;AACxD,EAAE,IAAI,MAAM,CAAC;AACb;AACA,EAAE,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;AACnC,IAAI,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;AAC/D,GAAG;AACH;AACA,EAAE,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AACpC,IAAI,OAAO,GAAG,QAAQ,CAAC;AACvB,IAAI,QAAQ,GAAG,IAAI,CAAC;AACpB,GAAG;AACH;AACA,EAAE,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAC5B;AACA,EAAE,OAAO,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG;AAC1E,aAAa,OAAO,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;AAC5C;AACA,EAAE,IAAI,WAAW,IAAI,CAAC,MAAM,EAAE;AAC9B,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,4BAA4B,GAAG,OAAO,CAAC,CAAC;AACnE,GAAG;AACH;AACA,EAAE,IAAI,mBAAmB,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC;AACxD,EAAE,IAAI,mBAAmB,GAAG,CAAC,WAAW,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;AAC5D,EAAE,IAAI,qBAAqB,GAAG,CAAC,WAAW,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC;AAClE;AACA,EAAE,IAAI,CAAC,mBAAmB;AAC1B,MAAM,mBAAmB;AACzB,MAAM,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC;AACzC,MAAM,qBAAqB,EAAE;AAC7B,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,wBAAwB,GAAG,OAAO,CAAC,CAAC;AAC/D,GAAG;AACH;AACA,EAAE,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,QAAQ;AACxC,MAAM,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE;AACzE,IAAI,MAAM,MAAM,CAAC;AACjB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AAChB,SAAS,MAAM,CAAC,KAAK,cAAc,KAAK,cAAc,OAAO,EAAE;AACtE,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACvC,CAAC;AACD;AACA;AACA,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;AAC5B,SAAS,YAAY,CAAC,KAAK,cAAc,KAAK,cAAc,OAAO,EAAE;AAC5E,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;AACD;AACA,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAClB,SAAS,OAAO,CAAC,GAAG,EAAE;AAC7B,EAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC;AACrB;;;;ACjWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,CAAC;AACb,EAAE,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AACjC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qBAAqB,GAAG;AAC1B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,QAAQ,EAAE;AACtB,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACzD,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAC5B,IAAI,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC3B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,CAAC;eACa,GAAG,OAAO;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,IAAI,EAAE,EAAE,EAAE;AACpC,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE;AACnB,IAAI,OAAO,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,QAAQ,GAAG,GAAG,CAAC;AAC1C,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;yBACuB,GAAG,iBAAiB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE;AAC9C,EAAE,IAAI,UAAU,KAAK,OAAO,KAAK,EAAE;AACnC,IAAI,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;AACvC,IAAI,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC1D,GAAG;AACH,EAAE,IAAI,EAAE,MAAM,KAAK,MAAM,YAAY,MAAM,CAAC,CAAC,EAAE;AAC/C,IAAI,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACnD,GAAG;AACH,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,EAAE;AAClD,IAAI,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACzB,EAAE,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;AACnC,EAAE,MAAM,CAAC,qBAAqB,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC;AACrD,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE;AACnD,IAAI,KAAK,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE;AAC/B,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE;AACzC,IAAI,KAAK,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE;AAC/B,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG,CAAC,CAAC;AACL,CAAC;8BAC4B,GAAG,sBAAsB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,cAAc,SAAS,MAAM,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAClD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,SAAS,cAAc,CAAC;AACzC,EAAE,WAAW,CAAC,WAAW,EAAE,QAAQ,EAAE;AACrC,IAAI,IAAI,SAAS,KAAK,WAAW,EAAE;AACnC,MAAM,WAAW,GAAG,CAAC,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,EAAE;AAChE,MAAM,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;AACtE,KAAK;AACL,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,GAAG;AACH;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY,SAAS,cAAc,CAAC;AAC1C,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AACxC,IAAI,IAAI,EAAE,MAAM,YAAY,MAAM,CAAC,EAAE;AACrC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL;AACA,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC1C,MAAM,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;AACjE,KAAK;AACL;AACA,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;AACpD;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,YAAY,IAAI;AACxC,gBAAgB,IAAI,CAAC,MAAM,YAAY,MAAM,CAAC,EAAE;AAChD,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AACvD,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5D,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,SAAS,MAAM,CAAC;AAC1B,EAAE,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9B,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,SAAS,MAAM,CAAC;AAC5B,EAAE,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9B,IAAI,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,GAAG,SAAS,MAAM,CAAC;AACzB,EAAE,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9B,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,SAAS,MAAM,CAAC;AAC3B,EAAE,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9B,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD;AACA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9B;AACA;AACA;AACA,SAAS,WAAW,CAAC,GAAG,EAAE;AAC1B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AACvC,EAAE,MAAM,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;AACpC,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtB,CAAC;AACD;AACA,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE;AAClC,EAAE,OAAO,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AAC7B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,SAAS,MAAM,CAAC;AAChC,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY,SAAS,MAAM,CAAC;AAClC,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,SAAS,MAAM,CAAC;AAC/B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3C,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3C,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,SAAS,MAAM,CAAC;AACjC,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACvC,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACvC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,SAAS,MAAM,CAAC;AAC3B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAChC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,OAAO,SAAS,MAAM,CAAC;AAC7B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAChC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,SAAS,MAAM,CAAC;AAC5B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAClC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACjC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,QAAQ,SAAS,MAAM,CAAC;AAC9B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAClC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACjC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,QAAQ,SAAS,MAAM,CAAC;AAC9B,EAAE,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC9C,IAAI,IAAI,EAAE,aAAa,YAAY,MAAM,CAAC,EAAE;AAC5C,MAAM,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAC;AAC5D,KAAK;AACL,IAAI,IAAI,EAAE,CAAC,CAAC,KAAK,YAAY,cAAc,KAAK,KAAK,CAAC,OAAO,EAAE;AAC/D,cAAc,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AACzD,MAAM,MAAM,IAAI,SAAS,CAAC,qCAAqC;AAC/D,4BAA4B,uCAAuC,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,EAAE,KAAK,YAAY,cAAc,CAAC;AAC3C,YAAY,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE;AACrC,MAAM,IAAI,GAAG,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC;AACxC,KAAK;AACL;AACA,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;AACjB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,IAAI,IAAI,KAAK,YAAY,cAAc,EAAE;AACzC,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;AACrC,MAAM,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;AAC7C,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC;AAClB,MAAM,OAAO,GAAG,GAAG,KAAK,EAAE;AAC1B,QAAQ,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;AAC7D,QAAQ,EAAE,GAAG,CAAC;AACd,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,IAAI,IAAI,KAAK,YAAY,cAAc,EAAE;AACzC,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,KAAK,EAAE;AACtB,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACpD,MAAM,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACtD,MAAM,CAAC,IAAI,CAAC,CAAC;AACb,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AACnC,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;AACzC,MAAM,OAAO,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;AACpD,KAAK,EAAE,CAAC,CAAC,CAAC;AACV,IAAI,IAAI,IAAI,CAAC,KAAK,YAAY,cAAc,EAAE;AAC9C,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,SAAS,MAAM,CAAC;AAC/B,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE;AAChD,IAAI,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AAC/B,aAAa,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;AAC7E,MAAM,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;AACtE,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,QAAQ;AACtC,YAAY,SAAS,KAAK,cAAc,CAAC,EAAE;AAC3C,MAAM,cAAc,GAAG,QAAQ,CAAC;AAChC,MAAM,QAAQ,GAAG,SAAS,CAAC;AAC3B,KAAK;AACL;AACA;AACA,IAAI,KAAK,MAAM,EAAE,IAAI,MAAM,EAAE;AAC7B,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI;AACtB,cAAc,SAAS,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE;AAC1C,QAAQ,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;AAChF,OAAO;AACP,KAAK;AACL;AACA,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI;AACR,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;AACjE,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,cAAc,CAAC;AAC3C,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;AACjB,IAAI,IAAI;AACR,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK;AAC9C,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1C,QAAQ,MAAM,IAAI,GAAG,CAAC;AACtB,QAAQ,OAAO,IAAI,GAAG,GAAG,CAAC;AAC1B,OAAO,EAAE,CAAC,CAAC,CAAC;AACZ,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC9C,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,SAAS,KAAK,EAAE,CAAC,QAAQ,EAAE;AACrC,QAAQ,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACjD,OAAO;AACP,MAAM,MAAM,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACtC,MAAM,IAAI,IAAI,CAAC,cAAc;AAC7B,cAAc,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE;AACpC,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC;AAC/B,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;AACtB,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACzB,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;AACxC,MAAM,IAAI,SAAS,KAAK,EAAE,CAAC,QAAQ,EAAE;AACrC,QAAQ,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AACpC,QAAQ,IAAI,SAAS,KAAK,EAAE,EAAE;AAC9B,UAAU,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/C,UAAU,IAAI,CAAC,GAAG,IAAI,EAAE;AACxB;AACA;AACA,YAAY,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACzC,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,UAAU,GAAG,MAAM,CAAC;AAC1B,MAAM,MAAM,IAAI,IAAI,CAAC;AACrB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,OAAO,CAAC,UAAU,GAAG,SAAS,IAAI,WAAW,CAAC;AAClD,GAAG;AACH;AACA;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC9C,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,QAAQ;AACpC,cAAc,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;AAClC,QAAQ,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;AAC3C,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,QAAQ,EAAE;AACtB,IAAI,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE;AACtC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,EAAE,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACpC,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,QAAQ,EAAE;AACrB,IAAI,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE;AACtC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,EAAE,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACpC,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE;AACvB,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAC;AACpB,OAAO,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;AAC9B,QAAQ,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC;AAC1B,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACtD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACtD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,wBAAwB,SAAS,kBAAkB,CAAC;AAC1D,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,EAAE,CAAC,MAAM,YAAY,cAAc;AAC3C,aAAa,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;AAChC,MAAM,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;AAC/E,KAAK;AACL;AACA,IAAI,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,SAAS,CAAC,CAAC;AACpD;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACzC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC9C,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,SAAS,MAAM,CAAC;AAC3B,EAAE,WAAW,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE;AAC9C,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,YAAY,IAAI;AACvC,mBAAmB,KAAK,YAAY,MAAM,CAAC,CAAC,CAAC;AAC7C,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,KAAK,GAAG,IAAI,wBAAwB,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE,KAAK,MAAM,IAAI,CAAC,KAAK,YAAY,cAAc;AAC/C,kBAAkB,KAAK,CAAC,OAAO,EAAE,EAAE;AACnC,MAAM,KAAK,GAAG,IAAI,wBAAwB,CAAC,KAAK,CAAC,CAAC;AAClD,KAAK,MAAM,IAAI,EAAE,KAAK,YAAY,kBAAkB,CAAC,EAAE;AACvD,MAAM,MAAM,IAAI,SAAS,CAAC,qCAAqC;AAC/D,4BAA4B,+BAA+B,CAAC,CAAC;AAC7D,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,aAAa,EAAE;AACrC,MAAM,aAAa,GAAG,IAAI,CAAC;AAC3B,KAAK;AACL,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,aAAa;AACjC,cAAc,aAAa,YAAY,MAAM,CAAC,CAAC,EAAE;AACjD,MAAM,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;AACpE,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,aAAa,EAAE;AAChC,MAAM,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE;AAClC,QAAQ,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACjE,OAAO;AACP,MAAM,IAAI,SAAS,KAAK,aAAa,CAAC,QAAQ,EAAE;AAChD,QAAQ,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC3D,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;AAChC,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,EAAE;AAC9B,QAAQ,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AAClC,OAAO;AACP,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,uBAAuB,GAAG,GAAG,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB;AACA;AACA,IAAI,IAAI,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,gBAAgB,GAAG,SAAS,GAAG,EAAE;AAC1C,MAAM,OAAO,qBAAqB,CAAC,GAAG,CAAC,CAAC;AACxC,KAAK,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,sBAAsB,GAAG,SAAS,GAAG,EAAE;AAChD,MAAM,qBAAqB,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7C,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL;AACA;AACA;AACA,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;AAC3E,KAAK;AACL,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,uBAAuB,CAAC,GAAG,EAAE;AAC/B,IAAI,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;AACzD,MAAM,IAAI,IAAI,CAAC,aAAa;AAC5B,aAAa,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;AAC9D,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClE,MAAM,IAAI,GAAG;AACb,cAAc,CAAC,CAAC,GAAG,CAAC,MAAM;AAC1B,iBAAiB,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE;AACpD,QAAQ,OAAO,GAAG,CAAC;AACnB,OAAO;AACP,KAAK,MAAM;AACX,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvC,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACvC,QAAQ,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC9C,UAAU,OAAO,GAAG,CAAC;AACrB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,CAAC;AACb,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AACnC,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnC,IAAI,IAAI,SAAS,KAAK,GAAG,EAAE;AAC3B,MAAM,IAAI,aAAa,GAAG,CAAC,CAAC;AAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AAC/B,MAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE;AACxC,QAAQ,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AACxC,OAAO;AACP,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC1C,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;AACjC,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AAChF,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACnC,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC3C,IAAI,IAAI,SAAS,KAAK,GAAG,EAAE;AAC3B,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AACrC,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AACrC,MAAM,IAAI,aAAa,GAAG,CAAC,CAAC;AAC5B,MAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE;AACxC,QAAQ,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AACxC,OAAO;AACP,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/C,MAAM,OAAO,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC5D,wCAAwC,MAAM,GAAG,aAAa,CAAC,CAAC;AAChE,KAAK;AACL,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACtC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE;AACxC,IAAI,MAAM,EAAE,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;AAChC,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,IAAII,aAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;AAC7B,MAAM,IAAI,SAAS,KAAK,MAAM,EAAE;AAChC,QAAQ,MAAM,GAAG,CAAC,CAAC;AACnB,OAAO;AACP,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAClC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa,SAAS,MAAM,CAAC;AACnC,EAAE,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE;AAChD,IAAI,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE;AACnC,MAAM,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE;AACvD,MAAM,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;AACtE,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,MAAM;AACnC,YAAY,SAAS,KAAK,QAAQ,CAAC,EAAE;AACrC,MAAM,QAAQ,GAAG,MAAM,CAAC;AACxB,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,IAAI,EAAE,MAAM,YAAY,MAAM,CAAC,EAAE;AACvC,QAAQ,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACvD,OAAO;AACP,MAAM,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,aAAa;AACvC,cAAc,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC;AAC/B,cAAc,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AACvD,QAAQ,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;AACzE,OAAO;AACP,MAAM,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE;AACxC,QAAQ,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;AACnE,OAAO;AACP,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE;AACxB,MAAM,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;AACtC,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,KAAK,CAAC,uBAAuB,EAAE;AACxD,QAAQ,IAAI,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AAChD,OAAO;AACP,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC;AACjC,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AACxB;AACA;AACA,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;AAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3D,KAAK;AACL;AACA,IAAI,OAAO,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AAC1E,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC9C,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;AACnD,MAAM,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;AAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3D,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AAC1E,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC9B,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AACjC,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;AACnD,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7D,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;AAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3D,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,MAAM;AACnB,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;AACjD,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC7D,IAAI,IAAI,IAAI,GAAG,aAAa,CAAC;AAC7B,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AACxE,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AAC7D,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI;AAC/B,cAAc,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACvC,QAAQ,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;AACrE,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3C,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,CAAC,EAAE;AAC7B,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,IAAI,CAAC,IAAI,WAAW,CAAC;AACrB,GAAG;AACH,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY,SAAS,MAAM,CAAC;AAClC,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;AACnC,IAAI,IAAI,EAAE,CAAC,IAAI,YAAY,IAAI;AAC/B,cAAc,IAAI,YAAY,MAAM,CAAC,CAAC,EAAE;AACxC,MAAM,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;AAClE,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG;AAChC,YAAY,SAAS,KAAK,QAAQ,CAAC,EAAE;AACrC,MAAM,QAAQ,GAAG,GAAG,CAAC;AACrB,MAAM,GAAG,GAAG,SAAS,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;AACzD,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB;AACA;AACA;AACA;AACA,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,EAAE;AACvC,MAAM,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,eAAe,GAAG,WAAW;AACtC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC9C,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,SAAS,KAAK,EAAE,CAAC,QAAQ,EAAE;AACrC,QAAQ,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC7C,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,SAAS,KAAK,EAAE,CAAC,QAAQ,EAAE;AACrC,QAAQ,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AACpC,QAAQ,IAAI,SAAS,KAAK,EAAE,EAAE;AAC9B,UAAU,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACxB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC3B,IAAI,MAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,QAAQ,EAAE;AACvB;AACA;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,QAAQ,EAAE;AACrB,IAAI,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE;AACtC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,EAAE,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACpC,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,QAAQ,CAAC;AACf,EAAE,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE;AACzC,IAAI,IAAI,EAAE,SAAS,YAAY,YAAY,CAAC,EAAE;AAC9C,MAAM,MAAM,IAAI,SAAS,CAAC,kCAAkC,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,EAAE;AAClD,MAAM,MAAM,IAAI,SAAS,CAAC,+BAA+B,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;AACzC,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC5E,IAAI,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,SAAS,EAAE;AACvC,MAAM,MAAM,IAAI,KAAK,CAAC,oCAAoC;AAC1D,yBAAyB,SAAS,GAAG,QAAQ,CAAC,GAAG,MAAM;AACvD,wBAAwB,SAAS,GAAG,UAAU,CAAC,CAAC;AAChD,KAAK;AACL;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B;AACA;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;AACrC,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE;AACrB,MAAM,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;AAClC,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;AAC5B,MAAM,IAAI,CAAC,KAAK,GAAG,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC;AAC/C,KAAK;AACL;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;AAClD,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC7D,IAAI,MAAM,KAAK,GAAG,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC;AAC3C,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;AACjC,YAAY,KAAK,KAAK,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE;AACjE,MAAM,MAAM,IAAI,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC;AACnE,4BAA4B,uCAAuC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;AACtF,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;AAClD,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC1E,qCAAqC,SAAS,CAAC,CAAC;AAChD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,OAAO,SAAS,QAAQ,CAAC;AAC/B,EAAE,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE;AACnC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC7D,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,IAAI,SAAS,KAAK,OAAO,KAAK,EAAE;AACpC;AACA,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACvD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,SAAS,MAAM,CAAC;AAC1B,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM,YAAY,cAAc,KAAK,MAAM,CAAC,OAAO,EAAE;AACjE,cAAc,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;AAC3D,MAAM,MAAM,IAAI,SAAS,CAAC,kCAAkC;AAC5D,4BAA4B,uCAAuC,CAAC,CAAC;AACrE,KAAK;AACL;AACA,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,EAAE,MAAM,YAAY,cAAc,CAAC,EAAE;AAC7C,MAAM,IAAI,GAAG,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE;AAClB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE;AAClB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAI,IAAI,IAAI,CAAC,MAAM,YAAY,cAAc,EAAE;AAC/C,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,EAAEA,aAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC9B,cAAc,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE;AACrC,MAAM,MAAM,IAAI,SAAS,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC;AAC/D,4BAA4B,oBAAoB,GAAG,IAAI,GAAG,iBAAiB,CAAC,CAAC;AAC7E,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,MAAM,EAAE;AACpC,MAAM,MAAM,IAAI,UAAU,CAAC,0BAA0B,CAAC,CAAC;AACvD,KAAK;AACL,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACtD,IAAI,IAAI,IAAI,CAAC,MAAM,YAAY,cAAc,EAAE;AAC/C,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,OAAO,SAAS,MAAM,CAAC;AAC7B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACxB,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAACA,aAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAC7B,MAAM,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC;AACrB,IAAI,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAC/C,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC;AAC5B,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACvC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAChE,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL;AACA;AACA;AACA,IAAI,IAAI,QAAQ,KAAK,OAAO,GAAG,EAAE;AACjC,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;AAC3B,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,IAAIA,aAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACzC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,MAAM,EAAE;AACpC,MAAM,MAAM,IAAI,UAAU,CAAC,0BAA0B,CAAC,CAAC;AACvD,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACzB,IAAI,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,IAAI,OAAO,IAAI,GAAG,CAAC,CAAC;AACpB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,SAAS,MAAM,CAAC;AAC1B,EAAE,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE;AACjC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,OAAO;AACpC,YAAY,SAAS,KAAK,QAAQ,CAAC,EAAE;AACrC,MAAM,QAAQ,GAAG,OAAO,CAAC;AACzB,MAAM,OAAO,GAAG,SAAS,CAAC;AAC1B,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,OAAO,EAAE;AAC/B,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;AACnB,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;AAC3C,MAAM,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;AACxD,KAAK;AACL;AACA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAACA,aAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAC7B,MAAM,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;AAC7B,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO;AAC1B,YAAY,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE;AAClC,MAAM,MAAM,IAAI,UAAU,CAAC,6BAA6B,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC5D,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL;AACA;AACA;AACA,IAAI,IAAI,QAAQ,KAAK,OAAO,GAAG,EAAE;AACjC,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;AAC3B,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,IAAIA,aAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACzC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO;AAC1B,YAAY,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE;AAClC,MAAM,MAAM,IAAI,UAAU,CAAC,6BAA6B,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,MAAM,EAAE;AACpC,MAAM,MAAM,IAAI,UAAU,CAAC,0BAA0B,CAAC,CAAC;AACvD,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACzB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,QAAQ,SAAS,MAAM,CAAC;AAC9B,EAAE,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAE;AAC/B,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB;AACA,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AACD;uBACsB,GAAG,eAAe;oBACrB,GAAG,YAAY;qBACd,GAAG,aAAa;aACxB,GAAG,KAAK;eACN,GAAG,OAAO;YACb,GAAG,IAAI;cACL,GAAG,MAAM;cACT,GAAG,MAAM;gBACP,GAAG,QAAQ;eACZ,GAAG,OAAO;iBACR,GAAG,SAAS;iBACZ,GAAG,SAAS;kBACX,GAAG,UAAU;2BACJ,GAAG,mBAAmB;iCAChB,GAAG,yBAAyB;cAC/C,GAAG,MAAM;sBACD,GAAG,cAAc;qBAClB,GAAG,aAAa;iBACpB,GAAG,SAAS;gBACb,GAAG,QAAQ;aACd,GAAG,KAAK;gBACL,GAAG,QAAQ;aACd,GAAG,KAAK;iBACJ,GAAG,SAAS;AAC5B;AACA;eACc,IAAI,CAAC,WAAW,EAAE,QAAQ,KAAK,IAAI,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE;AACrF;AACA;eACc,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,KAAK,IAAI,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC5F;AACA;AACA;AACA,oBAAU,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AACjD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AAClD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AAClD;AACA;AACA;AACA,sBAAW,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AAClD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AAClD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AAClD;AACA;AACA;aACY,IAAI,QAAQ,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;AACtD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACtD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACtD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACtD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACtD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACtD;AACA;AACA;eACc,IAAI,QAAQ,IAAI,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE;AAC1D;AACA;AACA;WACU,IAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AAChD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACjD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACjD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACjD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACjD;AACA;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACjD;AACA;AACA;aACY,IAAI,QAAQ,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE;AACrD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACrD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACrD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACrD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACrD;AACA;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;AACrD;AACA;AACA;eACc,IAAI,QAAQ,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;AACzD;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE;AAChD;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;AACpD;AACA;YACW,IAAI,QAAQ,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE;AACjD;AACA;cACa,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACrD;AACA;AACA,4BAAc,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;AACzG;AACA;aACY,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,KAAK,IAAI,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,EAAE;AAChF;AACA;YACW,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,QAAQ,KAAK,IAAI,QAAQ,CAAC,aAAa,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE;AACjG;AACA;cACa,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC,EAAE;AAChG;AACA;iCACgC,IAAI,CAAC,MAAM,EAAE,QAAQ,KAAK,IAAI,wBAAwB,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC1G;AACA;AACA,wBAAY,IAAI,CAAC,MAAM,EAAE,QAAQ,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;AAClE;AACA;aACY,IAAI,QAAQ,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;AACnD;AACA;aACY,IAAI,CAAC,OAAO,EAAE,QAAQ,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;AACpE;AACA;cACa,IAAI,CAAC,KAAK,EAAE,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;;ACvpFnE;AAIA;AACA;AACA;;AACO,MAAMC,SAAS,GAAG,CAACC,QAAQ,GAAW,WAApB,KAA4C;AACnE,SAAOC,IAAA,CAAkB,EAAlB,EAAsBD,QAAtB,CAAP;AACD,CAFM;AAIP;AACA;AACA;;AACO,MAAME,MAAM,GAAG,CAACF,QAAQ,GAAW,QAApB,KAAyC;AAC7D,SAAOC,IAAA,CAAkB,CAAlB,EAAqBD,QAArB,CAAP;AACD,CAFM;;ACdP;AAUO,SAASG,yBAAT,CACLC,KADK,EAELC,UAFK,EAGLC,WAHK,EAIL,GAAGC,OAJE,EAK0B;AAC/B,SAAOC,2BAA6B,CAACH,UAAD,EAAaC,WAAb,EAA0BC,OAA1B,EAAmC;AACrEE,IAAAA,aAAa,EAAE;AADsD,GAAnC,CAApC;AAGD;;MCOYC,gBAAgB,GAAc,IAAIC,SAAJ,CACzC,6CADyC;MAI9BC,2BAA2B,GAAc,IAAID,SAAJ,CACpD,8CADoD;AAItD,MAAME,sBAAsB,GAAG,wBAA/B;AACA,MAAMC,qBAAqB,GAAG,uBAA9B;AAEA;AACA;AACA;AACA;AACA;;AACA,SAASC,cAAT,CAAwBhB,SAAxB,EAA6D;AAC3D,SAAOD,aAAM,CAACkB,IAAP,CAAYjB,SAAS,CAACkB,QAAV,EAAZ,CAAP;AACD;AAED;AACA;AACA;;;AACO,MAAMC,GAAN,SAAkBC,EAAlB,CAAqB;AAC1B;AACF;AACA;AACEF,EAAAA,QAAQ,GAAkB;AACxB,UAAMG,CAAC,GAAG,MAAMC,OAAN,GAAgBC,OAAhB,EAAV;AACA,UAAMC,CAAC,GAAGzB,aAAM,CAACkB,IAAP,CAAYI,CAAZ,CAAV;;AACA,QAAIG,CAAC,CAACC,MAAF,KAAa,CAAjB,EAAoB;AAClB,aAAOD,CAAP;AACD;;AACDE,IAAAA,MAAM,CAACF,CAAC,CAACC,MAAF,GAAW,CAAZ,EAAe,eAAf,CAAN;AAEA,UAAME,OAAO,GAAG5B,aAAM,CAAC6B,KAAP,CAAa,CAAb,CAAhB;AACAJ,IAAAA,CAAC,CAACK,IAAF,CAAOF,OAAP;AACA,WAAOA,OAAP;AACD;AAED;AACF;AACA;;;AACmB,SAAVG,UAAU,CAACC,MAAD,EAA6B;AAC5CL,IAAAA,MAAM,CAACK,MAAM,CAACN,MAAP,KAAkB,CAAnB,EAAuB,0BAAyBM,MAAM,CAACN,MAAO,EAA9D,CAAN;AACA,WAAO,IAAIN,GAAJ,CACL,CAAC,GAAGY,MAAJ,EACGR,OADH,GAEGS,GAFH,CAEOC,CAAC,IAAK,KAAIA,CAAC,CAACC,QAAF,CAAW,EAAX,CAAe,EAApB,CAAsBC,KAAtB,CAA4B,CAAC,CAA7B,CAFZ,EAGGC,IAHH,CAGQ,EAHR,CADK,EAKL,EALK,CAAP;AAOD;;AA7ByB;;AAgC5B,SAASC,SAAT,CAAmBC,kBAAnB,EAAqD;AACnD,SAAO,eAAeA,kBAAtB;AACD;;AAQD,MAAMC,kBAAkB,GAAG;AACzBC,EAAAA,UAAU,EAAE,CADa;AAEzBC,EAAAA,aAAa,EAAE,CAFU;AAGzBC,EAAAA,YAAY,EAAE,CAHW;AAIzBC,EAAAA,YAAY,EAAE;AAJW,CAA3B;;MAQaC,WAAW,GAAc,IAAIhC,SAAJ,CACpC,6CADoC;AAItC;AACA;AACA;;MA8BaiC,UAAU,GAAkC3C,MAAA,CAAoB,CAC3EA,GAAA,CAAiB,qBAAjB,CAD2E,EAE3E4C,SAAA,CAAiB,eAAjB,CAF2E,EAG3EA,MAAA,CAAc,QAAd,CAH2E,EAI3E5C,EAAA,CAAgB,UAAhB,CAJ2E,EAK3EA,EAAA,CAAgB,eAAhB,CAL2E,EAM3EA,GAAA,CAAiB,uBAAjB,CAN2E,EAO3E4C,SAAA,CAAiB,iBAAjB,CAP2E,CAApB;AAUzD;AACA;AACA;;AA4DA;AACA;AACA;;MACaC,aAAa,GAAkC7C,MAAA,CAC1D,CACE4C,SAAA,CAAiB,MAAjB,CADF,EAEEA,SAAA,CAAiB,OAAjB,CAFF,EAGEA,MAAA,CAAc,QAAd,CAHF,EAIE5C,GAAA,CAAiB,gBAAjB,CAJF,EAKE4C,SAAA,CAAiB,UAAjB,CALF,EAME5C,EAAA,CAAgB,OAAhB,CANF,EAOEA,GAAA,CAAiB,gBAAjB,CAPF,EAQE4C,MAAA,CAAc,UAAd,CARF,EASEA,MAAA,CAAc,iBAAd,CATF,EAUE5C,GAAA,CAAiB,sBAAjB,CAVF,EAWE4C,SAAA,CAAiB,gBAAjB,CAXF,CAD0D;AAgB5D;AACA;AACA;;AAkCA;AACA;AACA;;AACA,MAAME,cAAc,GAAG9C,MAAA,CAAoB,CACzCA,EAAA,CAAgB,GAAhB,CADyC,EAEzCA,EAAA,CAAgB,GAAhB,CAFyC,EAGzCA,EAAA,CAAgB,gBAAhB,CAHyC,EAIzC4C,SAAA,CAAiB,SAAjB,CAJyC,EAKzCA,SAAA,CAAiB,SAAjB,CALyC,EAMzCA,SAAA,CAAiB,SAAjB,CANyC,EAOzCA,SAAA,CAAiB,SAAjB,CAPyC,EAQzCA,SAAA,CAAiB,SAAjB,CARyC,EASzCA,SAAA,CAAiB,SAAjB,CATyC,EAUzCA,SAAA,CAAiB,SAAjB,CAVyC,EAWzCA,SAAA,CAAiB,SAAjB,CAXyC,EAYzCA,SAAA,CAAiB,SAAjB,CAZyC,EAazCA,SAAA,CAAiB,UAAjB,CAbyC,EAczCA,SAAA,CAAiB,UAAjB,CAdyC,CAApB,CAAvB;AAiBA;AACA;AACA;;AACO,MAAMG,KAAN,CAAY;AACjB;AACF;AACA;;AAGE;AACF;AACA;;AAGE;AACF;AACA;;AAGE;AACF;AACA;;AAGE;AACF;AACA;;AAGE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACEC,EAAAA,WAAW,CACT5C,UADS,EAETN,SAFS,EAGTmD,SAHS,EAITC,KAJS,EAKT;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AACAC,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoB;AAClBhD,MAAAA,UADkB;AAElBN,MAAAA,SAFkB;AAGlBmD,MAAAA,SAHkB;AAIlBC,MAAAA,KAJkB;AAKlB;AACAG,MAAAA,mBAAmB,EAAE1C;AANH,KAApB;AAQD;AAED;AACF;AACA;AACA;AACA;;;AAC6C,eAA9B2C,8BAA8B,CACzClD,UADyC,EAExB;AACjB,WAAO,MAAMA,UAAU,CAACmD,iCAAX,CAA6CZ,UAAU,CAACa,IAAxD,CAAb;AACD;AAED;AACF;AACA;AACA;AACA;;;AACgD,eAAjCC,iCAAiC,CAC5CrD,UAD4C,EAE3B;AACjB,WAAO,MAAMA,UAAU,CAACmD,iCAAX,CACXV,aAAa,CAACW,IADH,CAAb;AAGD;AAED;AACF;AACA;AACA;AACA;;;AACiD,eAAlCE,kCAAkC,CAC7CtD,UAD6C,EAE5B;AACjB,WAAO,MAAMA,UAAU,CAACmD,iCAAX,CACXT,cAAc,CAACU,IADJ,CAAb;AAGD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACyB,eAAVG,UAAU,CACrBvD,UADqB,EAErB8C,KAFqB,EAGrBU,aAHqB,EAIrBC,eAJqB,EAKrBC,QALqB,EAMrBb,SANqB,EAOL;AAChB,UAAMc,WAAW,GAAGC,OAAO,CAACC,QAAR,EAApB;AACA,UAAMC,KAAK,GAAG,IAAInB,KAAJ,CACZ3C,UADY,EAEZ2D,WAAW,CAACjE,SAFA,EAGZmD,SAHY,EAIZC,KAJY,CAAd,CAFgB;;AAUhB,UAAMiB,aAAa,GAAG,MAAMpB,KAAK,CAACO,8BAAN,CAC1BlD,UAD0B,CAA5B;AAIA,UAAMC,WAAW,GAAG,IAAI+D,WAAJ,EAApB;AACA/D,IAAAA,WAAW,CAACgE,GAAZ,CACEC,aAAa,CAACC,aAAd,CAA4B;AAC1BC,MAAAA,UAAU,EAAEtB,KAAK,CAACpD,SADQ;AAE1B2E,MAAAA,gBAAgB,EAAEV,WAAW,CAACjE,SAFJ;AAG1B4E,MAAAA,QAAQ,EAAEP,aAHgB;AAI1BQ,MAAAA,KAAK,EAAEhC,UAAU,CAACa,IAJQ;AAK1BP,MAAAA;AAL0B,KAA5B,CADF;AAUA5C,IAAAA,WAAW,CAACgE,GAAZ,CACEtB,KAAK,CAAC6B,yBAAN,CACE3B,SADF,EAEEc,WAAW,CAACjE,SAFd,EAGEgE,QAHF,EAIEF,aAJF,EAKEC,eALF,CADF,EAzBgB;;AAoChB,UAAM3D,yBAAyB,CAC7B,kCAD6B,EAE7BE,UAF6B,EAG7BC,WAH6B,EAI7B6C,KAJ6B,EAK7Ba,WAL6B,CAA/B;AAQA,WAAOG,KAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqB,QAAbK,aAAa,CAACM,KAAD,EAAuC;AACxD;AACA,UAAMV,aAAa,GAAG,MAAMpB,KAAK,CAACU,iCAAN,CAC1B,KAAKrD,UADqB,CAA5B;AAIA,UAAM0E,UAAU,GAAGd,OAAO,CAACC,QAAR,EAAnB;AACA,UAAM5D,WAAW,GAAG,IAAI+D,WAAJ,EAApB;AACA/D,IAAAA,WAAW,CAACgE,GAAZ,CACEC,aAAa,CAACC,aAAd,CAA4B;AAC1BC,MAAAA,UAAU,EAAE,KAAKtB,KAAL,CAAWpD,SADG;AAE1B2E,MAAAA,gBAAgB,EAAEK,UAAU,CAAChF,SAFH;AAG1B4E,MAAAA,QAAQ,EAAEP,aAHgB;AAI1BQ,MAAAA,KAAK,EAAE9B,aAAa,CAACW,IAJK;AAK1BP,MAAAA,SAAS,EAAE,KAAKA;AALU,KAA5B,CADF;AAUA,UAAM8B,aAAa,GAAG,KAAKjF,SAA3B;AACAO,IAAAA,WAAW,CAACgE,GAAZ,CACEtB,KAAK,CAACiC,4BAAN,CACE,KAAK/B,SADP,EAEE8B,aAFF,EAGED,UAAU,CAAChF,SAHb,EAIE+E,KAJF,CADF,EAnBwD;;AA6BxD,UAAM3E,yBAAyB,CAC7B,qCAD6B,EAE7B,KAAKE,UAFwB,EAG7BC,WAH6B,EAI7B,KAAK6C,KAJwB,EAK7B4B,UAL6B,CAA/B;AAQA,WAAOA,UAAU,CAAChF,SAAlB;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoC,QAA5BmF,4BAA4B,CAACJ,KAAD,EAAuC;AACvE,UAAMK,iBAAiB,GAAG,MAAMnC,KAAK,CAACoC,yBAAN,CAC9B,KAAK9B,mBADyB,EAE9B,KAAKJ,SAFyB,EAG9B,KAAKnD,SAHyB,EAI9B+E,KAJ8B,CAAhC;AAOA,WAAO,KAAKO,oCAAL,CAA0CP,KAA1C,EAAiDK,iBAAjD,CAAP;AACD;;AAEyC,QAApCE,oCAAoC,CACxCP,KADwC,EAExCK,iBAFwC,EAGpB;AACpB,UAAMhF,yBAAyB,CAC7B,8BAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACsC,uCAAN,CACE,KAAKhC,mBADP,EAEE,KAAKJ,SAFP,EAGE,KAAKnD,SAHP,EAIEoF,iBAJF,EAKEL,KALF,EAME,KAAK3B,KAAL,CAAWpD,SANb,CADF,CAH6B,EAa7B,KAAKoD,KAbwB,CAA/B;AAgBA,WAAOgC,iBAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACwC,QAAhCI,gCAAgC,CACpCT,KADoC,EAEd;AACtB,UAAMK,iBAAiB,GAAG,MAAMnC,KAAK,CAACoC,yBAAN,CAC9B,KAAK9B,mBADyB,EAE9B,KAAKJ,SAFyB,EAG9B,KAAKnD,SAHyB,EAI9B+E,KAJ8B,CAAhC,CADsB;AAStB;AACA;;AACA,QAAI;AACF,aAAO,MAAM,KAAKU,cAAL,CAAoBL,iBAApB,CAAb;AACD,KAFD,CAEE,OAAOM,GAAP,EAAY;AACZ;AACA;AACA;AACA;AACA,UACEA,GAAG,CAACC,OAAJ,KAAgB7E,sBAAhB,IACA4E,GAAG,CAACC,OAAJ,KAAgB5E,qBAFlB,EAGE;AACA;AACA;AACA,YAAI;AACF,gBAAM,KAAKuE,oCAAL,CACJP,KADI,EAEJK,iBAFI,CAAN;AAID,SALD,CAKE,OAAOM,GAAP,EAAY;AAEZ;AACA;AACD,SAZD;;;AAeA,eAAO,MAAM,KAAKD,cAAL,CAAoBL,iBAApB,CAAb;AACD,OAnBD,MAmBO;AACL,cAAMM,GAAN;AACD;AACF;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACyC,eAA1BE,0BAA0B,CACrCtF,UADqC,EAErC6C,SAFqC,EAGrC4B,KAHqC,EAIrC3B,KAJqC,EAKrCyC,MALqC,EAMjB;AACpB;AACA,UAAMxB,aAAa,GAAG,MAAMpB,KAAK,CAACU,iCAAN,CAC1BrD,UAD0B,CAA5B,CAFoB;;AAOpB,UAAM0E,UAAU,GAAGd,OAAO,CAACC,QAAR,EAAnB;AACA,UAAM5D,WAAW,GAAG,IAAI+D,WAAJ,EAApB;AACA/D,IAAAA,WAAW,CAACgE,GAAZ,CACEC,aAAa,CAACC,aAAd,CAA4B;AAC1BC,MAAAA,UAAU,EAAEtB,KAAK,CAACpD,SADQ;AAE1B2E,MAAAA,gBAAgB,EAAEK,UAAU,CAAChF,SAFH;AAG1B4E,MAAAA,QAAQ,EAAEP,aAHgB;AAI1BQ,MAAAA,KAAK,EAAE9B,aAAa,CAACW,IAJK;AAK1BP,MAAAA;AAL0B,KAA5B,CADF,EAToB;;AAoBpB5C,IAAAA,WAAW,CAACgE,GAAZ,CACEC,aAAa,CAACsB,QAAd,CAAuB;AACrBpB,MAAAA,UAAU,EAAEtB,KAAK,CAACpD,SADG;AAErB+F,MAAAA,QAAQ,EAAEf,UAAU,CAAChF,SAFA;AAGrB4E,MAAAA,QAAQ,EAAEiB;AAHW,KAAvB,CADF,EApBoB;AA6BpB;AACA;;AACAtF,IAAAA,WAAW,CAACgE,GAAZ,CACEtB,KAAK,CAACiC,4BAAN,CACE/B,SADF,EAEEP,WAFF,EAGEoC,UAAU,CAAChF,SAHb,EAIE+E,KAJF,CADF,EA/BoB;;AAyCpB,UAAM3E,yBAAyB,CAC7B,gDAD6B,EAE7BE,UAF6B,EAG7BC,WAH6B,EAI7B6C,KAJ6B,EAK7B4B,UAL6B,CAA/B;AAQA,WAAOA,UAAU,CAAChF,SAAlB;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsB,QAAdgG,cAAc,CAClBC,CADkB,EAElBzF,OAFkB,EAGE;AACpB,UAAM0F,eAAe,GAAGhC,OAAO,CAACC,QAAR,EAAxB,CADoB;;AAIpB,UAAME,aAAa,GAAG,MAAMpB,KAAK,CAACW,kCAAN,CAC1B,KAAKtD,UADqB,CAA5B;AAGA,UAAMC,WAAW,GAAG,IAAI+D,WAAJ,EAApB;AACA/D,IAAAA,WAAW,CAACgE,GAAZ,CACEC,aAAa,CAACC,aAAd,CAA4B;AAC1BC,MAAAA,UAAU,EAAE,KAAKtB,KAAL,CAAWpD,SADG;AAE1B2E,MAAAA,gBAAgB,EAAEuB,eAAe,CAAClG,SAFR;AAG1B4E,MAAAA,QAAQ,EAAEP,aAHgB;AAI1BQ,MAAAA,KAAK,EAAE7B,cAAc,CAACU,IAJI;AAK1BP,MAAAA,SAAS,EAAE,KAAKA;AALU,KAA5B,CADF,EARoB;;AAmBpB,QAAIgD,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEF,eAAe,CAAClG,SAAzB;AAAoCqG,MAAAA,QAAQ,EAAE,KAA9C;AAAqDC,MAAAA,UAAU,EAAE;AAAjE,KADS,EAET;AAACF,MAAAA,MAAM,EAAEG,kBAAT;AAA6BF,MAAAA,QAAQ,EAAE,KAAvC;AAA8CC,MAAAA,UAAU,EAAE;AAA1D,KAFS,CAAX;AAIA9F,IAAAA,OAAO,CAACgG,OAAR,CAAgBC,MAAM,IACpBN,IAAI,CAACO,IAAL,CAAU;AAACN,MAAAA,MAAM,EAAEK,MAAT;AAAiBJ,MAAAA,QAAQ,EAAE,KAA3B;AAAkCC,MAAAA,UAAU,EAAE;AAA9C,KAAV,CADF;AAGA,UAAMK,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErCA,EAAA,CAAgB,GAAhB,CAFqC,CAApB,CAAnB;AAIA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBb,MAAAA;AAFF,KADF,EAKEW,IALF;AAOArG,IAAAA,WAAW,CAACgE,GAAZ,CAAgB;AACd4B,MAAAA,IADc;AAEdhD,MAAAA,SAAS,EAAE,KAAKA,SAFF;AAGdyD,MAAAA;AAHc,KAAhB,EAtCoB;;AA6CpB,UAAMxG,yBAAyB,CAC7B,sCAD6B,EAE7B,KAAKE,UAFwB,EAG7BC,WAH6B,EAI7B,KAAK6C,KAJwB,EAK7B8C,eAL6B,CAA/B;AAQA,WAAOA,eAAe,CAAClG,SAAvB;AACD;AAED;AACF;AACA;;;AACmB,QAAX+G,WAAW,GAAsB;AACrC,UAAMC,IAAI,GAAG,MAAM,KAAK1G,UAAL,CAAgBmF,cAAhB,CAA+B,KAAKzF,SAApC,CAAnB;;AACA,QAAIgH,IAAI,KAAK,IAAb,EAAmB;AACjB,YAAM,IAAIC,KAAJ,CAAU,6BAAV,CAAN;AACD;;AACD,QAAI,CAACD,IAAI,CAACjC,KAAL,CAAWmC,MAAX,CAAkB,KAAK/D,SAAvB,CAAL,EAAwC;AACtC,YAAM,IAAI8D,KAAJ,CAAW,uBAAsBE,IAAI,CAACC,SAAL,CAAeJ,IAAI,CAACjC,KAApB,CAA2B,EAA5D,CAAN;AACD;;AACD,QAAIiC,IAAI,CAACJ,IAAL,CAAUnF,MAAV,IAAoBoB,UAAU,CAACa,IAAnC,EAAyC;AACvC,YAAM,IAAIuD,KAAJ,CAAW,mBAAX,CAAN;AACD;;AAED,UAAML,IAAI,GAAG7G,aAAM,CAACkB,IAAP,CAAY+F,IAAI,CAACJ,IAAjB,CAAb;AACA,UAAMS,QAAQ,GAAGxE,UAAU,CAACyE,MAAX,CAAkBV,IAAlB,CAAjB;;AAEA,QAAIS,QAAQ,CAACE,mBAAT,KAAiC,CAArC,EAAwC;AACtCF,MAAAA,QAAQ,CAACvD,aAAT,GAAyB,IAAzB;AACD,KAFD,MAEO;AACLuD,MAAAA,QAAQ,CAACvD,aAAT,GAAyB,IAAIlD,SAAJ,CAAcyG,QAAQ,CAACvD,aAAvB,CAAzB;AACD;;AAEDuD,IAAAA,QAAQ,CAACG,MAAT,GAAkBrG,GAAG,CAACW,UAAJ,CAAeuF,QAAQ,CAACG,MAAxB,CAAlB;AACAH,IAAAA,QAAQ,CAACI,aAAT,GAAyBJ,QAAQ,CAACI,aAAT,IAA0B,CAAnD;;AAEA,QAAIJ,QAAQ,CAACK,qBAAT,KAAmC,CAAvC,EAA0C;AACxCL,MAAAA,QAAQ,CAACtD,eAAT,GAA2B,IAA3B;AACD,KAFD,MAEO;AACLsD,MAAAA,QAAQ,CAACtD,eAAT,GAA2B,IAAInD,SAAJ,CAAcyG,QAAQ,CAACtD,eAAvB,CAA3B;AACD;;AACD,WAAOsD,QAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACsB,QAAd5B,cAAc,CAClBkC,OADkB,EAElBC,UAFkB,EAGI;AACtB,UAAMZ,IAAI,GAAG,MAAM,KAAK1G,UAAL,CAAgBmF,cAAhB,CAA+BkC,OAA/B,EAAwCC,UAAxC,CAAnB;;AACA,QAAIZ,IAAI,KAAK,IAAb,EAAmB;AACjB,YAAM,IAAIC,KAAJ,CAAUnG,sBAAV,CAAN;AACD;;AACD,QAAI,CAACkG,IAAI,CAACjC,KAAL,CAAWmC,MAAX,CAAkB,KAAK/D,SAAvB,CAAL,EAAwC;AACtC,YAAM,IAAI8D,KAAJ,CAAUlG,qBAAV,CAAN;AACD;;AACD,QAAIiG,IAAI,CAACJ,IAAL,CAAUnF,MAAV,IAAoBsB,aAAa,CAACW,IAAtC,EAA4C;AAC1C,YAAM,IAAIuD,KAAJ,CAAW,sBAAX,CAAN;AACD;;AAED,UAAML,IAAI,GAAG7G,aAAM,CAACkB,IAAP,CAAY+F,IAAI,CAACJ,IAAjB,CAAb;AACA,UAAMiB,WAAW,GAAG9E,aAAa,CAACuE,MAAd,CAAqBV,IAArB,CAApB;AACAiB,IAAAA,WAAW,CAACC,OAAZ,GAAsBH,OAAtB;AACAE,IAAAA,WAAW,CAACE,IAAZ,GAAmB,IAAInH,SAAJ,CAAciH,WAAW,CAACE,IAA1B,CAAnB;AACAF,IAAAA,WAAW,CAAC9C,KAAZ,GAAoB,IAAInE,SAAJ,CAAciH,WAAW,CAAC9C,KAA1B,CAApB;AACA8C,IAAAA,WAAW,CAAChC,MAAZ,GAAqB1E,GAAG,CAACW,UAAJ,CAAe+F,WAAW,CAAChC,MAA3B,CAArB;;AAEA,QAAIgC,WAAW,CAACG,cAAZ,KAA+B,CAAnC,EAAsC;AACpCH,MAAAA,WAAW,CAACI,QAAZ,GAAuB,IAAvB;AACAJ,MAAAA,WAAW,CAACK,eAAZ,GAA8B,IAAI/G,GAAJ,EAA9B;AACD,KAHD,MAGO;AACL0G,MAAAA,WAAW,CAACI,QAAZ,GAAuB,IAAIrH,SAAJ,CAAciH,WAAW,CAACI,QAA1B,CAAvB;AACAJ,MAAAA,WAAW,CAACK,eAAZ,GAA8B/G,GAAG,CAACW,UAAJ,CAAe+F,WAAW,CAACK,eAA3B,CAA9B;AACD;;AAEDL,IAAAA,WAAW,CAACJ,aAAZ,GAA4BI,WAAW,CAACM,KAAZ,KAAsB,CAAlD;AACAN,IAAAA,WAAW,CAACO,QAAZ,GAAuBP,WAAW,CAACM,KAAZ,KAAsB,CAA7C;;AAEA,QAAIN,WAAW,CAACQ,cAAZ,KAA+B,CAAnC,EAAsC;AACpCR,MAAAA,WAAW,CAACS,iBAAZ,GAAgCnH,GAAG,CAACW,UAAJ,CAAe+F,WAAW,CAACU,QAA3B,CAAhC;AACAV,MAAAA,WAAW,CAACU,QAAZ,GAAuB,IAAvB;AACD,KAHD,MAGO;AACLV,MAAAA,WAAW,CAACS,iBAAZ,GAAgC,IAAhC;AACAT,MAAAA,WAAW,CAACU,QAAZ,GAAuB,KAAvB;AACD;;AAED,QAAIV,WAAW,CAACW,oBAAZ,KAAqC,CAAzC,EAA4C;AAC1CX,MAAAA,WAAW,CAACY,cAAZ,GAA6B,IAA7B;AACD,KAFD,MAEO;AACLZ,MAAAA,WAAW,CAACY,cAAZ,GAA6B,IAAI7H,SAAJ,CAAciH,WAAW,CAACY,cAA1B,CAA7B;AACD;;AAED,QAAI,CAACZ,WAAW,CAACE,IAAZ,CAAiBb,MAAjB,CAAwB,KAAKlH,SAA7B,CAAL,EAA8C;AAC5C,YAAM,IAAIiH,KAAJ,CACH,yBAAwBE,IAAI,CAACC,SAAL,CACvBS,WAAW,CAACE,IADW,CAEvB,QAAOZ,IAAI,CAACC,SAAL,CAAe,KAAKpH,SAApB,CAA+B,EAHpC,CAAN;AAKD;;AACD,WAAO6H,WAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACuB,QAAfa,eAAe,CAACC,QAAD,EAA6C;AAChE,UAAM3B,IAAI,GAAG,MAAM,KAAK1G,UAAL,CAAgBmF,cAAhB,CAA+BkD,QAA/B,CAAnB;;AACA,QAAI3B,IAAI,KAAK,IAAb,EAAmB;AACjB,YAAM,IAAIC,KAAJ,CAAU,yBAAV,CAAN;AACD;;AACD,QAAI,CAACD,IAAI,CAACjC,KAAL,CAAWmC,MAAX,CAAkB,KAAK/D,SAAvB,CAAL,EAAwC;AACtC,YAAM,IAAI8D,KAAJ,CAAW,wBAAX,CAAN;AACD;;AACD,QAAID,IAAI,CAACJ,IAAL,CAAUnF,MAAV,IAAoBuB,cAAc,CAACU,IAAvC,EAA6C;AAC3C,YAAM,IAAIuD,KAAJ,CAAW,uBAAX,CAAN;AACD;;AAED,UAAML,IAAI,GAAG7G,aAAM,CAACkB,IAAP,CAAY+F,IAAI,CAACJ,IAAjB,CAAb;AACA,UAAMgC,YAAY,GAAG5F,cAAc,CAACsE,MAAf,CAAsBV,IAAtB,CAArB;AACAgC,IAAAA,YAAY,CAACC,OAAb,GAAuB,IAAIjI,SAAJ,CAAcgI,YAAY,CAACC,OAA3B,CAAvB;AACAD,IAAAA,YAAY,CAACE,OAAb,GAAuB,IAAIlI,SAAJ,CAAcgI,YAAY,CAACE,OAA3B,CAAvB;AACAF,IAAAA,YAAY,CAACG,OAAb,GAAuB,IAAInI,SAAJ,CAAcgI,YAAY,CAACG,OAA3B,CAAvB;AACAH,IAAAA,YAAY,CAACI,OAAb,GAAuB,IAAIpI,SAAJ,CAAcgI,YAAY,CAACI,OAA3B,CAAvB;AACAJ,IAAAA,YAAY,CAACK,OAAb,GAAuB,IAAIrI,SAAJ,CAAcgI,YAAY,CAACK,OAA3B,CAAvB;AACAL,IAAAA,YAAY,CAACM,OAAb,GAAuB,IAAItI,SAAJ,CAAcgI,YAAY,CAACM,OAA3B,CAAvB;AACAN,IAAAA,YAAY,CAACO,OAAb,GAAuB,IAAIvI,SAAJ,CAAcgI,YAAY,CAACO,OAA3B,CAAvB;AACAP,IAAAA,YAAY,CAACQ,OAAb,GAAuB,IAAIxI,SAAJ,CAAcgI,YAAY,CAACQ,OAA3B,CAAvB;AACAR,IAAAA,YAAY,CAACS,OAAb,GAAuB,IAAIzI,SAAJ,CAAcgI,YAAY,CAACS,OAA3B,CAAvB;AACAT,IAAAA,YAAY,CAACU,QAAb,GAAwB,IAAI1I,SAAJ,CAAcgI,YAAY,CAACU,QAA3B,CAAxB;AACAV,IAAAA,YAAY,CAACW,QAAb,GAAwB,IAAI3I,SAAJ,CAAcgI,YAAY,CAACW,QAA3B,CAAxB;AAEA,WAAOX,YAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,QAAR9C,QAAQ,CACZ0D,MADY,EAEZC,WAFY,EAGZ1E,KAHY,EAIZ2E,YAJY,EAKZ7D,MALY,EAMmB;AAC/B,QAAI8D,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAAC0C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAAC/E,SAAvB;AACAQ,MAAAA,OAAO,GAAG,CAACuE,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACAvE,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,WAAO,MAAMtJ,yBAAyB,CACpC,UADoC,EAEpC,KAAKE,UAF+B,EAGpC,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC2G,yBAAN,CACE,KAAKzG,SADP,EAEEqG,MAFF,EAGEC,WAHF,EAIEE,cAJF,EAKED,YALF,EAME7D,MANF,CADF,CAHoC,EAapC,KAAKzC,KAb+B,EAcpC,GAAG5C,OAdiC,CAAtC;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACe,QAAPqJ,OAAO,CACXlC,OADW,EAEXM,QAFW,EAGXlD,KAHW,EAIX2E,YAJW,EAKX7D,MALW,EAMI;AACf,QAAI8D,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAAC0C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAAC/E,SAAvB;AACAQ,MAAAA,OAAO,GAAG,CAACuE,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACAvE,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,SAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC6G,wBAAN,CACE,KAAK3G,SADP,EAEEwE,OAFF,EAGEM,QAHF,EAIE0B,cAJF,EAKED,YALF,EAME7D,MANF,CADF,CAH6B,EAa7B,KAAKzC,KAbwB,EAc7B,GAAG5C,OAd0B,CAA/B;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACc,QAANuJ,MAAM,CACVpC,OADU,EAEV5C,KAFU,EAGV2E,YAHU,EAIK;AACf,QAAIC,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAAC0C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAAC/E,SAAvB;AACAQ,MAAAA,OAAO,GAAG,CAACuE,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACAvE,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,QAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC+G,uBAAN,CACE,KAAK7G,SADP,EAEEwE,OAFF,EAGEgC,cAHF,EAIED,YAJF,CADF,CAH6B,EAW7B,KAAKtG,KAXwB,EAY7B,GAAG5C,OAZ0B,CAA/B;AAcD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoB,QAAZyJ,YAAY,CAChBtC,OADgB,EAEhBuC,YAFgB,EAGhBC,aAHgB,EAIhBC,gBAJgB,EAKhBV,YALgB,EAMD;AACf,QAAIW,yBAAJ;AACA,QAAI7J,OAAJ;;AACA,QAAI6B,SAAS,CAAC+H,gBAAD,CAAb,EAAiC;AAC/BC,MAAAA,yBAAyB,GAAGD,gBAAgB,CAACpK,SAA7C;AACAQ,MAAAA,OAAO,GAAG,CAAC4J,gBAAD,CAAV;AACD,KAHD,MAGO;AACLC,MAAAA,yBAAyB,GAAGD,gBAA5B;AACA5J,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,cAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACqH,6BAAN,CACE,KAAKnH,SADP,EAEEwE,OAFF,EAGEuC,YAHF,EAIEC,aAJF,EAKEE,yBALF,EAMEX,YANF,CADF,CAH6B,EAa7B,KAAKtG,KAbwB,EAc7B,GAAG5C,OAd0B,CAA/B;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACc,QAAN+J,MAAM,CACVC,IADU,EAEVC,SAFU,EAGVf,YAHU,EAIV7D,MAJU,EAKK;AACf,QAAI8D,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAACoI,SAAD,CAAb,EAA0B;AACxBd,MAAAA,cAAc,GAAGc,SAAS,CAACzK,SAA3B;AACAQ,MAAAA,OAAO,GAAG,CAACiK,SAAD,CAAV;AACD,KAHD,MAGO;AACLd,MAAAA,cAAc,GAAGc,SAAjB;AACAjK,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,QAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACyH,uBAAN,CACE,KAAKvH,SADP,EAEE,KAAKnD,SAFP,EAGEwK,IAHF,EAIEb,cAJF,EAKED,YALF,EAME7D,MANF,CADF,CAH6B,EAa7B,KAAKzC,KAbwB,EAc7B,GAAG5C,OAd0B,CAA/B;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACY,QAAJmK,IAAI,CACRhD,OADQ,EAER5C,KAFQ,EAGR2E,YAHQ,EAIR7D,MAJQ,EAKO;AACf,QAAI8D,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAAC0C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAAC/E,SAAvB;AACAQ,MAAAA,OAAO,GAAG,CAACuE,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACAvE,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,MAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC2H,qBAAN,CACE,KAAKzH,SADP,EAEE,KAAKnD,SAFP,EAGE2H,OAHF,EAIEgC,cAJF,EAKED,YALF,EAME7D,MANF,CADF,CAH6B,EAa7B,KAAKzC,KAbwB,EAc7B,GAAG5C,OAd0B,CAA/B;AAgBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoB,QAAZqK,YAAY,CAChBlD,OADgB,EAEhB6C,IAFgB,EAGhBC,SAHgB,EAIhBf,YAJgB,EAKD;AACf,QAAIoB,kBAAJ;AACA,QAAItK,OAAJ;;AACA,QAAI6B,SAAS,CAACoI,SAAD,CAAb,EAA0B;AACxBK,MAAAA,kBAAkB,GAAGL,SAAS,CAACzK,SAA/B;AACAQ,MAAAA,OAAO,GAAG,CAACiK,SAAD,CAAV;AACD,KAHD,MAGO;AACLK,MAAAA,kBAAkB,GAAGL,SAArB;AACAjK,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,cAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC8H,6BAAN,CACE,KAAK5H,SADP,EAEEwE,OAFF,EAGE6C,IAHF,EAIEM,kBAJF,EAKEpB,YALF,CADF,CAH6B,EAY7B,KAAKtG,KAZwB,EAa7B,GAAG5C,OAb0B,CAA/B;AAeD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACqB,QAAbwK,aAAa,CACjBrD,OADiB,EAEjB8C,SAFiB,EAGjBf,YAHiB,EAIF;AACf,QAAIoB,kBAAJ;AACA,QAAItK,OAAJ;;AACA,QAAI6B,SAAS,CAACoI,SAAD,CAAb,EAA0B;AACxBK,MAAAA,kBAAkB,GAAGL,SAAS,CAACzK,SAA/B;AACAQ,MAAAA,OAAO,GAAG,CAACiK,SAAD,CAAV;AACD,KAHD,MAGO;AACLK,MAAAA,kBAAkB,GAAGL,SAArB;AACAjK,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,eAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACgI,8BAAN,CACE,KAAK9H,SADP,EAEEwE,OAFF,EAGE,KAAK3H,SAHP,EAIE8K,kBAJF,EAKEpB,YALF,CADF,CAH6B,EAY7B,KAAKtG,KAZwB,EAa7B,GAAG5C,OAb0B,CAA/B;AAeD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACmB,QAAX0K,WAAW,CACfvD,OADe,EAEf8C,SAFe,EAGff,YAHe,EAIA;AACf,QAAIoB,kBAAJ;AACA,QAAItK,OAAJ;;AACA,QAAI6B,SAAS,CAACoI,SAAD,CAAb,EAA0B;AACxBK,MAAAA,kBAAkB,GAAGL,SAAS,CAACzK,SAA/B;AACAQ,MAAAA,OAAO,GAAG,CAACiK,SAAD,CAAV;AACD,KAHD,MAGO;AACLK,MAAAA,kBAAkB,GAAGL,SAArB;AACAjK,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,aAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACkI,4BAAN,CACE,KAAKhI,SADP,EAEEwE,OAFF,EAGE,KAAK3H,SAHP,EAIE8K,kBAJF,EAKEpB,YALF,CADF,CAH6B,EAY7B,KAAKtG,KAZwB,EAa7B,GAAG5C,OAb0B,CAA/B;AAeD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACuB,QAAf4K,eAAe,CACnB5B,MADmB,EAEnBC,WAFmB,EAGnB1E,KAHmB,EAInB2E,YAJmB,EAKnB7D,MALmB,EAMnB7B,QANmB,EAOY;AAC/B,QAAI2F,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAAC0C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAAC/E,SAAvB;AACAQ,MAAAA,OAAO,GAAG,CAACuE,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACAvE,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,WAAO,MAAMtJ,yBAAyB,CACpC,iBADoC,EAEpC,KAAKE,UAF+B,EAGpC,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACoI,gCAAN,CACE,KAAKlI,SADP,EAEEqG,MAFF,EAGE,KAAKxJ,SAHP,EAIEyJ,WAJF,EAKEE,cALF,EAMED,YANF,EAOE7D,MAPF,EAQE7B,QARF,CADF,CAHoC,EAepC,KAAKZ,KAf+B,EAgBpC,GAAG5C,OAhBiC,CAAtC;AAkBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsB,QAAd8K,cAAc,CAClB3D,OADkB,EAElBM,QAFkB,EAGlBlD,KAHkB,EAIlB2E,YAJkB,EAKlB7D,MALkB,EAMlB7B,QANkB,EAOH;AACf,QAAI2F,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAAC0C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAAC/E,SAAvB;AACAQ,MAAAA,OAAO,GAAG,CAACuE,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACAvE,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,gBAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACsI,+BAAN,CACE,KAAKpI,SADP,EAEEwE,OAFF,EAGE,KAAK3H,SAHP,EAIEiI,QAJF,EAKE0B,cALF,EAMED,YANF,EAOE7D,MAPF,EAQE7B,QARF,CADF,CAH6B,EAe7B,KAAKZ,KAfwB,EAgB7B,GAAG5C,OAhB0B,CAA/B;AAkBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqB,QAAbgL,aAAa,CACjBhB,IADiB,EAEjBC,SAFiB,EAGjBf,YAHiB,EAIjB7D,MAJiB,EAKjB7B,QALiB,EAMF;AACf,QAAI2F,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAACoI,SAAD,CAAb,EAA0B;AACxBd,MAAAA,cAAc,GAAGc,SAAS,CAACzK,SAA3B;AACAQ,MAAAA,OAAO,GAAG,CAACiK,SAAD,CAAV;AACD,KAHD,MAGO;AACLd,MAAAA,cAAc,GAAGc,SAAjB;AACAjK,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,eAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAACwI,8BAAN,CACE,KAAKtI,SADP,EAEE,KAAKnD,SAFP,EAGEwK,IAHF,EAIEb,cAJF,EAKED,YALF,EAME7D,MANF,EAOE7B,QAPF,CADF,CAH6B,EAc7B,KAAKZ,KAdwB,EAe7B,GAAG5C,OAf0B,CAA/B;AAiBD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACmB,QAAXkL,WAAW,CACf/D,OADe,EAEf5C,KAFe,EAGf2E,YAHe,EAIf7D,MAJe,EAKf7B,QALe,EAMA;AACf,QAAI2F,cAAJ;AACA,QAAInJ,OAAJ;;AACA,QAAI6B,SAAS,CAAC0C,KAAD,CAAb,EAAsB;AACpB4E,MAAAA,cAAc,GAAG5E,KAAK,CAAC/E,SAAvB;AACAQ,MAAAA,OAAO,GAAG,CAACuE,KAAD,CAAV;AACD,KAHD,MAGO;AACL4E,MAAAA,cAAc,GAAG5E,KAAjB;AACAvE,MAAAA,OAAO,GAAGkJ,YAAV;AACD;;AACD,UAAMtJ,yBAAyB,CAC7B,aAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC0I,4BAAN,CACE,KAAKxI,SADP,EAEE,KAAKnD,SAFP,EAGE2H,OAHF,EAIEgC,cAJF,EAKED,YALF,EAME7D,MANF,EAOE7B,QAPF,CADF,CAH6B,EAc7B,KAAKZ,KAdwB,EAe7B,GAAG5C,OAf0B,CAA/B;AAiBD;AAED;AACF;AACA;AACA;AACA;;;AACkB,QAAVoL,UAAU,CAACC,aAAD,EAA0C;AACxD,UAAMzL,yBAAyB,CAC7B,YAD6B,EAE7B,KAAKE,UAFwB,EAG7B,IAAIgE,WAAJ,GAAkBC,GAAlB,CACEtB,KAAK,CAAC6I,2BAAN,CAAkC,KAAK3I,SAAvC,EAAkD0I,aAAlD,CADF,CAH6B,EAM7B,KAAKzI,KANwB,CAA/B;AAQD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACkC,SAAzB0B,yBAAyB,CAC9B3B,SAD8B,EAE9B4E,IAF8B,EAG9B/D,QAH8B,EAI9BF,aAJ8B,EAK9BC,eAL8B,EAMN;AACxB,QAAIoC,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEG,kBAAT;AAA6BF,MAAAA,QAAQ,EAAE,KAAvC;AAA8CC,MAAAA,UAAU,EAAE;AAA1D,KAFS,CAAX;AAIA,UAAMyF,iBAAiB,GAAG7L,MAAA,CAAoB,CAC5CA,EAAA,CAAgB,aAAhB,CAD4C,EAE5CA,EAAA,CAAgB,UAAhB,CAF4C,EAG5C4C,SAAA,CAAiB,eAAjB,CAH4C,EAI5C5C,EAAA,CAAgB,QAAhB,CAJ4C,EAK5C4C,SAAA,CAAiB,iBAAjB,CAL4C,CAApB,CAA1B;AAOA,QAAI8D,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa,IAAb,CAAX;AACA;AACE,YAAMoK,YAAY,GAAGD,iBAAiB,CAAClF,MAAlB,CACnB;AACEC,QAAAA,WAAW,EAAE,CADf;AACkB;AAChB9C,QAAAA,QAFF;AAGEF,QAAAA,aAAa,EAAE9C,cAAc,CAAC8C,aAAD,CAH/B;AAIEmI,QAAAA,MAAM,EAAElI,eAAe,KAAK,IAApB,GAA2B,CAA3B,GAA+B,CAJzC;AAKEA,QAAAA,eAAe,EAAE/C,cAAc,CAAC+C,eAAe,IAAI,IAAInD,SAAJ,CAAc,CAAd,CAApB;AALjC,OADmB,EAQnBgG,IARmB,CAArB;AAUAA,MAAAA,IAAI,GAAGA,IAAI,CAACzE,KAAL,CAAW,CAAX,EAAc6J,YAAd,CAAP;AACD;AAED,WAAO,IAAIE,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAFgC;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqC,SAA5B1B,4BAA4B,CACjC/B,SADiC,EAEjC4E,IAFiC,EAGjCJ,OAHiC,EAIjC5C,KAJiC,EAKT;AACxB,UAAMoB,IAAI,GAAG,CACX;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADW,EAEX;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFW,EAGX;AAACF,MAAAA,MAAM,EAAErB,KAAT;AAAgBsB,MAAAA,QAAQ,EAAE,KAA1B;AAAiCC,MAAAA,UAAU,EAAE;AAA7C,KAHW,EAIX;AAACF,MAAAA,MAAM,EAAEG,kBAAT;AAA6BF,MAAAA,QAAQ,EAAE,KAAvC;AAA8CC,MAAAA,UAAU,EAAE;AAA1D,KAJW,CAAb;AAMA,UAAMK,UAAU,GAAGzG,MAAA,CAAoB,CAACA,EAAA,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AACA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;;AAAA,KADF,EAIEF,IAJF;AAOA,WAAO,IAAIsF,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAFgC;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACkC,SAAzBgD,yBAAyB,CAC9BzG,SAD8B,EAE9BqG,MAF8B,EAG9BC,WAH8B,EAI9B1E,KAJ8B,EAK9B2E,YAL8B,EAM9B7D,MAN8B,EAON;AACxB,UAAMc,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErC4C,MAAA,CAAc,QAAd,CAFqC,CAApB,CAAnB;AAKA,UAAM8D,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBjB,MAAAA,MAAM,EAAE,IAAI1E,GAAJ,CAAQ0E,MAAR,EAAgB3E,QAAhB;AAFV,KADF,EAKE0F,IALF;AAQA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEoD,MAAT;AAAiBnD,MAAAA,QAAQ,EAAE,KAA3B;AAAkCC,MAAAA,UAAU,EAAE;AAA9C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEqD,WAAT;AAAsBpD,MAAAA,QAAQ,EAAE,KAAhC;AAAuCC,MAAAA,UAAU,EAAE;AAAnD,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAErB,KADA;AAERsB,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AACD,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACiC,SAAxBkD,wBAAwB,CAC7B3G,SAD6B,EAE7BwE,OAF6B,EAG7BM,QAH6B,EAI7BlD,KAJ6B,EAK7B2E,YAL6B,EAM7B7D,MAN6B,EAOL;AACxB,UAAMc,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErC4C,MAAA,CAAc,QAAd,CAFqC,CAApB,CAAnB;AAKA,UAAM8D,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBjB,MAAAA,MAAM,EAAE,IAAI1E,GAAJ,CAAQ0E,MAAR,EAAgB3E,QAAhB;AAFV,KADF,EAKE0F,IALF;AAQA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE6B,QAAT;AAAmB5B,MAAAA,QAAQ,EAAE,KAA7B;AAAoCC,MAAAA,UAAU,EAAE;AAAhD,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,IAA1B;AAAgCC,QAAAA,UAAU,EAAE;AAA5C,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgC,SAAvBoD,uBAAuB,CAC5B7G,SAD4B,EAE5BwE,OAF4B,EAG5B5C,KAH4B,EAI5B2E,YAJ4B,EAKJ;AACxB,UAAM/C,UAAU,GAAGzG,MAAA,CAAoB,CAACA,EAAA,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AAEA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CAAC;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KAAD,CAAX;;AACA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,IAA1B;AAAgCC,QAAAA,UAAU,EAAE;AAA5C,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsC,SAA7B0D,6BAA6B,CAClCnH,SADkC,EAElCwE,OAFkC,EAGlCuC,YAHkC,EAIlCC,aAJkC,EAKlCC,gBALkC,EAMlCV,YANkC,EAOV;AACxB,UAAMqC,iBAAiB,GAAG7L,MAAA,CAAoB,CAC5CA,EAAA,CAAgB,aAAhB,CAD4C,EAE5CA,EAAA,CAAgB,eAAhB,CAF4C,EAG5CA,EAAA,CAAgB,QAAhB,CAH4C,EAI5C4C,SAAA,CAAiB,cAAjB,CAJ4C,CAApB,CAA1B;AAOA,QAAI8D,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa,IAAb,CAAX;AACA;AACE,YAAMoK,YAAY,GAAGD,iBAAiB,CAAClF,MAAlB,CACnB;AACEC,QAAAA,WAAW,EAAE,CADf;AACkB;AAChBqD,QAAAA,aAAa,EAAE5H,kBAAkB,CAAC4H,aAAD,CAFnC;AAGE8B,QAAAA,MAAM,EAAE/B,YAAY,KAAK,IAAjB,GAAwB,CAAxB,GAA4B,CAHtC;AAIEA,QAAAA,YAAY,EAAElJ,cAAc,CAACkJ,YAAY,IAAI,IAAItJ,SAAJ,CAAc,CAAd,CAAjB;AAJ9B,OADmB,EAOnBgG,IAPmB,CAArB;AASAA,MAAAA,IAAI,GAAGA,IAAI,CAACzE,KAAL,CAAW,CAAX,EAAc6J,YAAd,CAAP;AACD;AAED,QAAI7F,IAAI,GAAG,CAAC;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KAAD,CAAX;;AACA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEgE,gBAAT;AAA2B/D,QAAAA,QAAQ,EAAE,IAArC;AAA2CC,QAAAA,UAAU,EAAE;AAAvD,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEgE,gBAAT;AAA2B/D,QAAAA,QAAQ,EAAE,KAArC;AAA4CC,QAAAA,UAAU,EAAE;AAAxD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgC,SAAvB8D,uBAAuB,CAC5BvH,SAD4B,EAE5B4E,IAF4B,EAG5ByC,IAH4B,EAI5BC,SAJ4B,EAK5Bf,YAL4B,EAM5B7D,MAN4B,EAOJ;AACxB,UAAMc,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErC4C,MAAA,CAAc,QAAd,CAFqC,CAApB,CAAnB;AAKA,UAAM8D,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBjB,MAAAA,MAAM,EAAE,IAAI1E,GAAJ,CAAQ0E,MAAR,EAAgB3E,QAAhB;AAFV,KADF,EAKE0F,IALF;AAQA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEoE,IAAT;AAAenE,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEqE,SADA;AAERpE,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,KAA9B;AAAqCC,QAAAA,UAAU,EAAE;AAAjD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC8B,SAArBgE,qBAAqB,CAC1BzH,SAD0B,EAE1B4E,IAF0B,EAG1BJ,OAH0B,EAI1B5C,KAJ0B,EAK1B2E,YAL0B,EAM1B7D,MAN0B,EAOF;AACxB,UAAMc,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErC4C,MAAA,CAAc,QAAd,CAFqC,CAApB,CAAnB;AAKA,UAAM8D,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;AACkB;AAChBjB,MAAAA,MAAM,EAAE,IAAI1E,GAAJ,CAAQ0E,MAAR,EAAgB3E,QAAhB;AAFV,KADF,EAKE0F,IALF;AAQA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAErB,KADA;AAERsB,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsC,SAA7BmE,6BAA6B,CAClC5H,SADkC,EAElCwE,OAFkC,EAGlC6C,IAHkC,EAIlCzF,KAJkC,EAKlC2E,YALkC,EAMV;AACxB,UAAM/C,UAAU,GAAGzG,MAAA,CAAoB,CAACA,EAAA,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AACA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,CADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEoE,IAAT;AAAenE,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,IAA1B;AAAgCC,QAAAA,UAAU,EAAE;AAA5C,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACuC,SAA9BqE,8BAA8B,CACnC9H,SADmC,EAEnCwE,OAFmC,EAGnCI,IAHmC,EAInC0C,SAJmC,EAKnCf,YALmC,EAMX;AACxB,UAAM/C,UAAU,GAAGzG,MAAA,CAAoB,CAACA,EAAA,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AACA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,IAA9B;AAAoCC,QAAAA,UAAU,EAAE;AAAhD,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,KAA9B;AAAqCC,QAAAA,UAAU,EAAE;AAAjD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqC,SAA5BuE,4BAA4B,CACjChI,SADiC,EAEjCwE,OAFiC,EAGjCI,IAHiC,EAIjC0C,SAJiC,EAKjCf,YALiC,EAMT;AACxB,UAAM/C,UAAU,GAAGzG,MAAA,CAAoB,CAACA,EAAA,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AACA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,IAA9B;AAAoCC,QAAAA,UAAU,EAAE;AAAhD,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,KAA9B;AAAqCC,QAAAA,UAAU,EAAE;AAAjD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACyC,SAAhCyE,gCAAgC,CACrClI,SADqC,EAErCqG,MAFqC,EAGrCzB,IAHqC,EAIrC0B,WAJqC,EAKrC1E,KALqC,EAMrC2E,YANqC,EAOrC7D,MAPqC,EAQrC7B,QARqC,EASb;AACxB,UAAM2C,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErC4C,MAAA,CAAc,QAAd,CAFqC,EAGrC5C,EAAA,CAAgB,UAAhB,CAHqC,CAApB,CAAnB;AAMA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;AACmB;AACjBjB,MAAAA,MAAM,EAAE,IAAI1E,GAAJ,CAAQ0E,MAAR,EAAgB3E,QAAhB,EAFV;AAGE8C,MAAAA;AAHF,KADF,EAME4C,IANF;AASA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEoD,MAAT;AAAiBnD,MAAAA,QAAQ,EAAE,KAA3B;AAAkCC,MAAAA,UAAU,EAAE;AAA9C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,EAGT;AAACF,MAAAA,MAAM,EAAEqD,WAAT;AAAsBpD,MAAAA,QAAQ,EAAE,KAAhC;AAAuCC,MAAAA,UAAU,EAAE;AAAnD,KAHS,CAAX;;AAKA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAErB,KADA;AAERsB,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AACD,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACwC,SAA/B2E,+BAA+B,CACpCpI,SADoC,EAEpCwE,OAFoC,EAGpCI,IAHoC,EAIpCE,QAJoC,EAKpClD,KALoC,EAMpC2E,YANoC,EAOpC7D,MAPoC,EAQpC7B,QARoC,EASZ;AACxB,UAAM2C,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErC4C,MAAA,CAAc,QAAd,CAFqC,EAGrC5C,EAAA,CAAgB,UAAhB,CAHqC,CAApB,CAAnB;AAMA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;AACmB;AACjBjB,MAAAA,MAAM,EAAE,IAAI1E,GAAJ,CAAQ0E,MAAR,EAAgB3E,QAAhB,EAFV;AAGE8C,MAAAA;AAHF,KADF,EAME4C,IANF;AASA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,EAGT;AAACF,MAAAA,MAAM,EAAE6B,QAAT;AAAmB5B,MAAAA,QAAQ,EAAE,KAA7B;AAAoCC,MAAAA,UAAU,EAAE;AAAhD,KAHS,CAAX;;AAKA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,IAA1B;AAAgCC,QAAAA,UAAU,EAAE;AAA5C,OAAV;AACD,KAFD,MAEO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACuC,SAA9B6E,8BAA8B,CACnCtI,SADmC,EAEnC4E,IAFmC,EAGnCyC,IAHmC,EAInCC,SAJmC,EAKnCf,YALmC,EAMnC7D,MANmC,EAOnC7B,QAPmC,EAQX;AACxB,UAAM2C,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErC4C,MAAA,CAAc,QAAd,CAFqC,EAGrC5C,EAAA,CAAgB,UAAhB,CAHqC,CAApB,CAAnB;AAMA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;AACmB;AACjBjB,MAAAA,MAAM,EAAE,IAAI1E,GAAJ,CAAQ0E,MAAR,EAAgB3E,QAAhB,EAFV;AAGE8C,MAAAA;AAHF,KADF,EAME4C,IANF;AASA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEoE,IAAT;AAAenE,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEqE,SADA;AAERpE,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAEqE,SAAT;AAAoBpE,QAAAA,QAAQ,EAAE,KAA9B;AAAqCC,QAAAA,UAAU,EAAE;AAAjD,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqC,SAA5B+E,4BAA4B,CACjCxI,SADiC,EAEjC4E,IAFiC,EAGjCJ,OAHiC,EAIjC5C,KAJiC,EAKjC2E,YALiC,EAMjC7D,MANiC,EAOjC7B,QAPiC,EAQT;AACxB,UAAM2C,UAAU,GAAGzG,MAAA,CAAoB,CACrCA,EAAA,CAAgB,aAAhB,CADqC,EAErC4C,MAAA,CAAc,QAAd,CAFqC,EAGrC5C,EAAA,CAAgB,UAAhB,CAHqC,CAApB,CAAnB;AAMA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;AACmB;AACjBjB,MAAAA,MAAM,EAAE,IAAI1E,GAAJ,CAAQ0E,MAAR,EAAgB3E,QAAhB,EAFV;AAGE8C,MAAAA;AAHF,KADF,EAME4C,IANF;AASA,QAAIT,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEuB,OAAT;AAAkBtB,MAAAA,QAAQ,EAAE,KAA5B;AAAmCC,MAAAA,UAAU,EAAE;AAA/C,KADS,EAET;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAFS,CAAX;;AAIA,QAAIoD,YAAY,CAACjI,MAAb,KAAwB,CAA5B,EAA+B;AAC7B0E,MAAAA,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAErB,KADA;AAERsB,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV;AAKD,KAND,MAMO;AACLH,MAAAA,IAAI,CAACO,IAAL,CAAU;AAACN,QAAAA,MAAM,EAAErB,KAAT;AAAgBsB,QAAAA,QAAQ,EAAE,KAA1B;AAAiCC,QAAAA,UAAU,EAAE;AAA7C,OAAV;AACAoD,MAAAA,YAAY,CAAClD,OAAb,CAAqBC,MAAM,IACzBN,IAAI,CAACO,IAAL,CAAU;AACRN,QAAAA,MAAM,EAAEK,MAAM,CAACzG,SADP;AAERqG,QAAAA,QAAQ,EAAE,IAFF;AAGRC,QAAAA,UAAU,EAAE;AAHJ,OAAV,CADF;AAOD;;AAED,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEA,SAFqB;AAGhCyD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACoC,SAA3BkF,2BAA2B,CAChC3I,SADgC,EAEhC0I,aAFgC,EAGR;AACxB,UAAMlF,UAAU,GAAGzG,MAAA,CAAoB,CAACA,EAAA,CAAgB,aAAhB,CAAD,CAApB,CAAnB;AAEA,UAAM0G,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa+E,UAAU,CAACjD,IAAxB,CAAb;AACAiD,IAAAA,UAAU,CAACE,MAAX,CACE;AACEC,MAAAA,WAAW,EAAE,EADf;;AAAA,KADF,EAIEF,IAJF;AAOA,QAAIT,IAAI,GAAG,CAAC;AAACC,MAAAA,MAAM,EAAEyF,aAAT;AAAwBxF,MAAAA,QAAQ,EAAE,KAAlC;AAAyCC,MAAAA,UAAU,EAAE;AAArD,KAAD,CAAX;AACA,WAAO,IAAI4F,sBAAJ,CAA2B;AAAC/F,MAAAA,IAAD;AAAOhD,MAAAA,SAAS,EAAEA,SAAlB;AAA6ByD,MAAAA;AAA7B,KAA3B,CAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACwC,eAAzBvB,yBAAyB,CACpC9B,mBADoC,EAEpCJ,SAFoC,EAGpC4E,IAHoC,EAIpChD,KAJoC,EAKpCoH,kBAAkB,GAAY,KALM,EAMhB;AACpB,QAAI,CAACA,kBAAD,IAAuB,CAACvL,SAAS,CAACwL,SAAV,CAAoBrH,KAAK,CAAC7D,QAAN,EAApB,CAA5B,EAAmE;AACjE,YAAM,IAAI+F,KAAJ,CAAW,sBAAqBlC,KAAK,CAAC7C,QAAN,EAAiB,EAAjD,CAAN;AACD;;AACD,WAAO,CACL,MAAMtB,SAAS,CAACyL,kBAAV,CACJ,CAACtH,KAAK,CAAC7D,QAAN,EAAD,EAAmBiC,SAAS,CAACjC,QAAV,EAAnB,EAAyC6G,IAAI,CAAC7G,QAAL,EAAzC,CADI,EAEJqC,mBAFI,CADD,EAKL,CALK,CAAP;AAMD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgD,SAAvCgC,uCAAuC,CAC5ChC,mBAD4C,EAE5CJ,SAF4C,EAG5C4E,IAH4C,EAI5CuE,iBAJ4C,EAK5CvH,KAL4C,EAM5C3B,KAN4C,EAOpB;AACxB,UAAMwD,IAAI,GAAG7G,aAAM,CAAC6B,KAAP,CAAa,CAAb,CAAb;AAEA,QAAIuE,IAAI,GAAG,CACT;AAACC,MAAAA,MAAM,EAAEhD,KAAT;AAAgBiD,MAAAA,QAAQ,EAAE,IAA1B;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KADS,EAET;AAACF,MAAAA,MAAM,EAAEkG,iBAAT;AAA4BjG,MAAAA,QAAQ,EAAE,KAAtC;AAA6CC,MAAAA,UAAU,EAAE;AAAzD,KAFS,EAGT;AAACF,MAAAA,MAAM,EAAErB,KAAT;AAAgBsB,MAAAA,QAAQ,EAAE,KAA1B;AAAiCC,MAAAA,UAAU,EAAE;AAA7C,KAHS,EAIT;AAACF,MAAAA,MAAM,EAAE2B,IAAT;AAAe1B,MAAAA,QAAQ,EAAE,KAAzB;AAAgCC,MAAAA,UAAU,EAAE;AAA5C,KAJS,EAKT;AAACF,MAAAA,MAAM,EAAE5B,aAAa,CAACrB,SAAvB;AAAkCkD,MAAAA,QAAQ,EAAE,KAA5C;AAAmDC,MAAAA,UAAU,EAAE;AAA/D,KALS,EAMT;AAACF,MAAAA,MAAM,EAAEjD,SAAT;AAAoBkD,MAAAA,QAAQ,EAAE,KAA9B;AAAqCC,MAAAA,UAAU,EAAE;AAAjD,KANS,EAOT;AAACF,MAAAA,MAAM,EAAEG,kBAAT;AAA6BF,MAAAA,QAAQ,EAAE,KAAvC;AAA8CC,MAAAA,UAAU,EAAE;AAA1D,KAPS,CAAX;AAUA,WAAO,IAAI4F,sBAAJ,CAA2B;AAChC/F,MAAAA,IADgC;AAEhChD,MAAAA,SAAS,EAAEI,mBAFqB;AAGhCqD,MAAAA;AAHgC,KAA3B,CAAP;AAKD;;AA7/DgB;;;;"}