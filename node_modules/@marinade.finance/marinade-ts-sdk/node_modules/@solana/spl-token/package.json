{"name": "@solana/spl-token", "version": "0.1.8", "description": "SPL Token JavaScript API", "license": "MIT", "author": "Solana Maintainers <<EMAIL>>", "homepage": "https://solana.com/", "repository": {"type": "git", "url": "https://github.com/solana-labs/solana-program-library"}, "bugs": {"url": "https://github.com/solana-labs/solana-program-library/issues"}, "publishConfig": {"access": "public"}, "browser": {"./lib/index.cjs.js": "./lib/index.browser.esm.js", "./lib/index.esm.js": "./lib/index.browser.esm.js"}, "main": "lib/index.cjs.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "browserslist": ["defaults", "not IE 11", "maintained node versions"], "files": ["/lib", "/module.flow.js"], "testnetDefaultChannel": "v1.3.17", "scripts": {"build": "rollup -c", "build:browser-test": "rollup -c test/rollup.config.js", "start": "babel-node cli/main.js", "start-with-test-validator": "start-server-and-test 'solana-test-validator --reset --quiet' http://localhost:8899/health start", "lint": "npm run pretty && eslint .", "lint:fix": "npm run pretty:fix && eslint . --fix", "flow": "flow check-contents < module.flow.js", "flow:watch": "watch 'flow' . --wait=1 --ignoreDirectoryPattern=/doc/", "lint:watch": "watch 'npm run lint:fix' . --wait=1", "build:program": "rm -f client/util/store/config.json; cargo build-bpf --manifest-path ../program/Cargo.toml", "cluster:localnet": "rm -f .env", "cluster:devnet": "cp cluster-devnet.env .env", "cluster:testnet": "cp cluster-testnet.env .env", "cluster:mainnet-beta": "cp cluster-mainnet-beta.env .env", "defs": "set -ex; flow check-contents < module.flow.js; tsc --esModuleInterop module.d.ts", "pretty": "prettier --check '{,cli*/**/}*.[jt]s'", "pretty:fix": "prettier --write '{,cli*/**/}*.[jt]s'", "test": "mocha './test/**/*.test.js'", "test:browser": "npm run build:browser-test && mocha-headless-chrome -f http://localhost:8080/mocha.html --timeout 180000", "test:browser-with-server": "start-server-and-test 'http-server -p 8080' 8080 test:browser"}, "dependencies": {"@babel/runtime": "^7.10.5", "@solana/web3.js": "^1.21.0", "bn.js": "^5.1.0", "buffer": "6.0.3", "buffer-layout": "^1.2.0", "dotenv": "10.0.0"}, "devDependencies": {"@babel/cli": "^7.10.5", "@babel/core": "^7.10.5", "@babel/node": "^7.10.5", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-transform-runtime": "^7.10.5", "@babel/preset-env": "^7.10.4", "@babel/preset-flow": "^7.10.4", "@babel/register": "^7.13.0", "@rollup/plugin-alias": "^3.1.2", "@rollup/plugin-babel": "^5.1.0", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@types/bn.js": "^5.1.0", "@typescript-eslint/eslint-plugin": "^4.14.2", "@typescript-eslint/parser": "^4.14.2", "babel-eslint": "^10.1.0", "chai": "^4.3.0", "chai-as-promised": "^7.1.1", "eslint": "^7.4.0", "eslint-plugin-flowtype": "^5.3.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-mocha": "^9.0.0", "esm": "^3.2.25", "flow-bin": "^0.157.0", "flow-remove-types": "^2.145.0", "flow-typed": "^3.3.1", "http-server": "^0.12.3", "mkdirp": "^1.0.4", "mocha": "^9.0.0", "mocha-headless-chrome": "^3.1.0", "mz": "^2.7.0", "prettier": "^2.0.5", "rollup": "^2.23.0", "rollup-plugin-copy": "^3.3.0", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-terser": "^7.0.2", "start-server-and-test": "^1.11.6", "typescript": "^4.1.3", "watch": "^1.0.2"}, "engines": {"node": ">= 10"}}