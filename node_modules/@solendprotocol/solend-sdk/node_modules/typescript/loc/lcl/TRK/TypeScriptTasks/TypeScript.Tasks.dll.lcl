﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="E:\A\_work\326\s\VS\TypeScriptTasks\bin\Release\TypeScript.Tasks.dll" PsrId="211" FileType="1" SrcCul="en-US" TgtCul="tr-TR" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="RCCX" />
  </OwnedComments>
  <Settings Name="@vsLocTools@\default.lss" Type="Lss" />
  <Item ItemId=";Managed Resources" ItemType="0" PsrId="211" Leaf="true">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
  </Item>
  <Item ItemId=";TypeScript.Tasks.Strings.resources" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" Path=" \ ;Managed Resources \ 0 \ 0" />
    <Item ItemId=";Strings" ItemType="0" PsrId="211" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";CompilerLogNotSpecified" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No compiler log specified, 'Clean' won't work.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Derleyici günlüğü belirtilmedi. 'Temizle' komutu çalışmaz.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConfigFileFoundOnDisk" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This project is using one or more tsconfig.json files for the build that may not be properly used by IntelliSense. Please set the item type of each tsconfig.json file to TypeScriptCompile or Content to ensure they are used properly by the editor.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Bu proje, IntelliSense tarafından düzgün şekilde kullanılamayan derleme için en az bir tsconfig.json dosyası kullanıyor. Düzenleyici tarafından düzgün şekilde kullanıldıklarından emin olmak için her tsconfig.json dosyasının öğe türünü TypeScriptCompile veya Content olarak ayarlayın.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorListBuildPrefix" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Derleme:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorWritingLog" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to write compiler log to '{0}. Exception: '{1}']]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Derleyici günlüğü '{0}' öğesine yazılamadı. Özel durum: '{1}']]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FallbackVersionWarning" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Using compiler version ({0}), if this is incorrect change the <TypeScriptToolsVersion> element in your project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Derleyici sürümü ({0}) kullanılıyor. Bu yanlışsa proje dosyanızdaki <TypeScriptToolsVersion> öğesini değiştirin.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LocatedReferenceFilesAt_0" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Located references file at: '{0}'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Başvuru dosyasının bulunduğu konum: '{0}'.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoCompilerError" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project file uses a different version of the TypeScript compiler and tools than is currently installed on this machine.  No compiler was found at {0}.  You may be able to fix this problem by changing the <TypeScriptToolsVersion> element in your project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Proje dosyanız, bu makinede yüklü olandan farklı bir TypeScript derleyicisi ve farklı araçlar kullanıyor. {0} konumunda derleyici bulunamadı. Bu sorunu, proje dosyanızda <TypeScriptToolsVersion> öğesini değiştirerek düzeltebilirsiniz.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeNotFound" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The build task could not find node.exe which is required to run the TypeScript compiler. Please install Node and ensure that the system path contains its location.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Derleme görevi, TypeScript derleyicisinin çalıştırılması için gerekli olan node.exe dosyasını bulamadı. Lütfen Node'u yükleyin ve sistem yolunun Node konumunu içerdiğinden emin olun.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolsVersionWarning" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Couldn't locate the compiler version ({0}) specified in the project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Proje dosyasında belirtilen derleyici sürümü ({0}) bulunamadı.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptCompileBlockedSet" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeScript compile is skipped because the TypeScriptCompileBlocked property is set to 'true'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeScriptCompileBlocked özelliği 'true' olarak ayarlandığından TypeScript derlemesi atlandı.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptNoVersionWarning" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project does not specify a TypeScriptToolsVersion. The latest available TypeScript compiler will be used ({0}). To remove this warning, set TypeScriptToolsVersion to a specific version or "Latest" to always select the latest compiler.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Projeniz bir TypeScriptToolsVersion belirtmiyor. Kullanılabilir son TypeScript derleyicisi ({0}) kullanılacak. Bu uyarıyı kaldırmak için, TypeScriptToolsVersion değerini belirli bir sürüme ayarlayın veya en son derleyiciyi seçmek için "Latest" değerine ayarlayın.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptVersionMismatchWarning" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project specifies TypeScriptToolsVersion {0}, but a matching compiler was not found. The latest available TypeScript compiler will be used ({1}). To remove this warning, install the TypeScript {0} SDK or update the value of TypeScriptToolsVersion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Projenizde TypeScriptToolsVersion {0} olarak belirtiliyor, ancak bununla eşleşen bir derleyici bulunamadı. Kullanılabilir son TypeScript derleyicisi ({1}) kullanılacak. Bu uyarıyı kaldırmak için, TypeScript {0} SDK'sını yükleyin veya TypeScriptToolsVersion değerini güncelleştirin.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
  <Item ItemId=";Version" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Ver" Disp="true" LocTbl="false" Path=" \ ;Version \ 8 \ 0" />
    <Item ItemId=";CompanyName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[Microsoft Corporation]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";FileDescription" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[TypeScript Build Tasks]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript Yapı Görevleri]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";InternalName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text" DevLk="true">
        <Val><![CDATA[TypeScript.Tasks.dll]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";LegalCopyright" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[© Microsoft Corporation. All rights reserved.]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[© Microsoft Corporation. Tüm hakları saklıdır.]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";OriginalFilename" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text" DevLk="true">
        <Val><![CDATA[TypeScript.Tasks.dll]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";ProductName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[TypeScript Build Tasks]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript Yapı Görevleri]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";Version" ItemType="8" PsrId="211" Leaf="true">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
  </Item>
</LCX>