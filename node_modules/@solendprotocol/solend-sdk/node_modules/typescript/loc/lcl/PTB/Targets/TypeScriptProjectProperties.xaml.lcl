﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="E:\A\_work\326\s\VS\TypeScriptTasks\bin\Release\Targets\TypeScriptProjectProperties.xaml" PsrId="22" FileType="1" SrcCul="en-US" TgtCul="pt-BR" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@vsLocTools@\default.lss" Type="Lss" />
  <Item ItemId=";&lt;Category&gt;" ItemType="0" PsrId="210" Leaf="false">
    <Disp Icon="Str" Disp="true" LocTbl="false" />
    <Item ItemId="0;typescriptbuild@Category@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[TypeScript Build]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Compilação TypeScript]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";&lt;EnumProperty&gt;" ItemType="0" PsrId="210" Leaf="false">
    <Disp Icon="Str" Disp="true" LocTbl="false" />
    <Item ItemId="0;typescriptcompileonsaveenabled@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Recompile sources on save]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Recompilar fontes ao salvar]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptgeneratesdeclarations@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Generate corresponding d.ts file]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Gerar arquivo d.ts correspondente]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptjsxemit@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Specify JSX code compilation mode for .tsx files, this doesn't affect .ts files]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Especificar o modo de compilação de código JSX para arquivos .tsx; isso não afeta arquivos .ts]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptmodulekind@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[External module code generation target]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Destino de geração de código do módulo externo]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptnoemitonerror@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Emit outputs if any errors were reported]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Emitir saídas se houver relato de erros]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptnoimplicitany@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Suppress warnings on expressions and declarations with an implied Any type]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Suprimir avisos em expressões e declarações com um tipo Any implícito]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptremovecomments@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Emit comments to output]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Emitir comentários de saída]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptsourcemap@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Generates corresponding .map file]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Gerar arquivo .map correspondente]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescripttarget@EnumProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[ECMAScript version to use for generated JavaScript]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Versão do ECMAScript a ser usada para o JavaScript gerado]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptcompileonsaveenabled@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Compile on save]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Compilar ao salvar]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptgeneratesdeclarations@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Generate declaration files]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Gerar arquivos de declaração]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptjsxemit@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Compilation mode for .tsx files]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Modo de compilação para arquivos .tsx]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptmodulekind@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Module system]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Sistema de módulo]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptnoemitonerror@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Emit on error]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Emitir quando houver erro]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptnoimplicitany@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Allow implicit 'any' types]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Permitir tipos 'any' implícitos]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptremovecomments@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Keep comments in JavaScript output]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Manter comentários na saída de JavaScript]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptsourcemap@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Generate source maps]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Gerar mapas de origem]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescripttarget@EnumProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[ECMAScript version]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Versão do ECMAScript]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";&lt;EnumValue&gt;" ItemType="0" PsrId="210" Leaf="false">
    <Disp Icon="Str" Disp="true" LocTbl="false" />
    <Item ItemId="0;amd@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[AMD]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[AMD]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;commonjs@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[CommonJS]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[CommonJS]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;es3@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[ECMAScript 3]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[ECMAScript 3]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;es5@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[ECMAScript 5]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[ECMAScript 5]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;es6@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[ECMAScript 6]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[ECMAScript 6]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;false@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[No]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Não]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;none@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[None]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Nenhum]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;preserve@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Preserve JSX elements]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Preservar elementos JSX]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;react@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Emit React call for JSX elements]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Emitir chamada de Reação para elementos JSX]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;system@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[System]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Sistema]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;true@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Yes]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Sim]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;umd@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[UMD]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[UMD]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="1;false@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Yes]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Sim]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="1;none@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[None]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Nenhum]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="1;true@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[No]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Não]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="2;false@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Yes]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Sim]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="2;true@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[No]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Não]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="3;false@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[No]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Não]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="3;true@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Yes]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Sim]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="4;false@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[No]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Não]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="4;true@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Yes]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Sim]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="5;false@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Yes]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Sim]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="5;true@EnumValue@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[No]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Não]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";&lt;Rule&gt;" ItemType="0" PsrId="210" Leaf="false">
    <Disp Icon="Str" Disp="true" LocTbl="false" />
    <Item ItemId="0;typescriptbuild@Rule@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[TypeScript Build]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Compilação TypeScript]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptbuild@Rule@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[TypeScript Build]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Compilação TypeScript]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";&lt;StringProperty&gt;" ItemType="0" PsrId="210" Leaf="false">
    <Disp Icon="Str" Disp="true" LocTbl="false" />
    <Item ItemId="0;typescriptmaproot@StringProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Emits the sourcemaps such that soucemaps while debugging will be located in the sourcemap root]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Emite o sourcemaps de maneira que este encontre-se na raiz de soucemap durante a depuração]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptoutdir@StringProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Redirect output to a different directory than sources]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Redirecionar a saída para um diretório diferente que as origens de]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptoutfile@StringProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Redirect output to a file]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Redirecionar a saída para um arquivo]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptsourceroot@StringProperty@Description" ItemType="47;XML:Attr:Description" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Emits the sourcemaps such that sources while debugging will be located in the source root]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Emite o sourcemaps de maneira que as origens se encontrem na raiz de origem durante a depuração]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptmaproot@StringProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Specify root directory of source maps]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Especificar o diretório raiz de mapas de origem]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptoutdir@StringProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Redirect JavaScript output to directory]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Redirecionar a saída de JavaScript para um diretório]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptoutfile@StringProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Combine JavaScript output into file]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Combinar saída de JavaScript em arquivo]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId="0;typescriptsourceroot@StringProperty@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[Specify root directory of TypeScript files]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[Especificar o diretório raiz dos arquivos TypeScript]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
</LCX>