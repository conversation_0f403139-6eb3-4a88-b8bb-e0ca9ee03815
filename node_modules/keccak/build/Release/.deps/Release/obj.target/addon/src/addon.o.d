cmd_Release/obj.target/addon/src/addon.o := c++ -o Release/obj.target/addon/src/addon.o ../src/addon.cc '-DNODE_GYP_MODULE_NAME=addon' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DNAPI_VERSION=3' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/src -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/23.10.0/deps/v8/include -I/Users/<USER>/Developer/Crypto/trunk-usdc/node_modules/node-addon-api -I../src/libkeccak-64  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=10.7 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++20 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/addon/src/addon.o.d.raw   -c
Release/obj.target/addon/src/addon.o: ../src/addon.cc \
  /Users/<USER>/Developer/Crypto/trunk-usdc/node_modules/node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/node_api_types.h \
  /Users/<USER>/Developer/Crypto/trunk-usdc/node_modules/node-addon-api/napi-inl.h \
  /Users/<USER>/Developer/Crypto/trunk-usdc/node_modules/node-addon-api/napi-inl.deprecated.h \
  ../src/libkeccak-64/KeccakSpongeWidth1600.h \
  ../src/libkeccak-64/KeccakSponge-common.h ../src/libkeccak-64/align.h \
  ../src/libkeccak-64/KeccakP-1600-SnP.h \
  ../src/libkeccak-64/brg_endian.h \
  ../src/libkeccak-64/KeccakP-1600-opt64-config.h
../src/addon.cc:
/Users/<USER>/Developer/Crypto/trunk-usdc/node_modules/node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/23.10.0/include/node/node_api_types.h:
/Users/<USER>/Developer/Crypto/trunk-usdc/node_modules/node-addon-api/napi-inl.h:
/Users/<USER>/Developer/Crypto/trunk-usdc/node_modules/node-addon-api/napi-inl.deprecated.h:
../src/libkeccak-64/KeccakSpongeWidth1600.h:
../src/libkeccak-64/KeccakSponge-common.h:
../src/libkeccak-64/align.h:
../src/libkeccak-64/KeccakP-1600-SnP.h:
../src/libkeccak-64/brg_endian.h:
../src/libkeccak-64/KeccakP-1600-opt64-config.h:
