import { Connection, PublicKey } from '@solana/web3.js';
import { SolendClient } from './solend-client';
import { notificationService } from './notifications';
import { config } from './config';
import { PoolStatus } from './types';

export class RealtimeMonitor {
  private solendClient: SolendClient;
  private connection: Connection;
  private isMonitoring = false;
  private lastStatus: PoolStatus | null = null;
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.solendClient = new SolendClient();
    this.connection = new Connection(config.rpcEndpoint, 'confirmed');
  }

  async initialize(): Promise<void> {
    await this.solendClient.initialize();
    console.log('✅ Real-time monitor initialized');
  }

  async startRealtimeMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.log('⚠️ Real-time monitoring already running');
      return;
    }

    this.isMonitoring = true;
    console.log(`🚀 Starting real-time USDC liquidity monitoring...`);
    console.log(`⏱️ Check interval: ${config.realtimeCheckIntervalSeconds} seconds`);

    await notificationService.sendNotification({
      title: 'Real-time Monitor Started',
      message: `Real-time USDC liquidity monitoring started.\nChecking every ${config.realtimeCheckIntervalSeconds} seconds for withdrawal opportunities.`,
      type: 'info'
    });

    // Start the monitoring loop
    this.startMonitoringLoop();

    // Also set up account change monitoring if possible
    this.setupAccountMonitoring();
  }

  private startMonitoringLoop(): void {
    const checkFunction = async () => {
      if (!this.isMonitoring) return;

      try {
        await this.checkForLiquidityChanges();
      } catch (error) {
        console.error('❌ Error in monitoring loop:', error);
      }
    };

    // Initial check
    checkFunction();

    // Set up interval
    this.checkInterval = setInterval(checkFunction, config.realtimeCheckIntervalSeconds * 1000);
  }

  private async setupAccountMonitoring(): Promise<void> {
    try {
      console.log('🔗 Setting up account change monitoring...');
      
      // Monitor the USDC reserve account for changes
      const usdcReserveAddress = '8nXn5zEXFbAvXyeYgLLethMVfHxU4QgyG2GVrHmKojqR';
      
      this.connection.onAccountChange(
        new PublicKey(usdcReserveAddress),
        async (accountInfo, context) => {
          console.log(`📡 USDC reserve account changed at slot ${context.slot}`);
          
          // Trigger immediate check when account changes
          setTimeout(async () => {
            try {
              await this.checkForLiquidityChanges();
            } catch (error) {
              console.error('❌ Error in account change handler:', error);
            }
          }, 1000); // Wait 1 second for data to propagate
        },
        'confirmed'
      );

      console.log('✅ Account change monitoring set up');
    } catch (error) {
      console.error('⚠️ Could not set up account monitoring:', error);
      console.log('Falling back to interval-based monitoring only');
    }
  }

  private async checkForLiquidityChanges(): Promise<void> {
    try {
      const status = await this.solendClient.getPoolStatus();
      
      console.log(`🔍 [${new Date().toISOString()}] Checking liquidity...`);
      console.log(`   USDC Available: ${status.availableLiquidity.toFixed(6)} USDC`);
      console.log(`   Max Withdraw: ${status.maxWithdrawAmount.toFixed(6)} USDC`);
      console.log(`   Utilization: ${(status.utilizationRate * 100).toFixed(2)}%`);

      // Check for significant changes
      if (this.lastStatus) {
        await this.analyzeChanges(this.lastStatus, status);
      }

      // Check if withdrawal is now possible
      if (status.canWithdraw && status.maxWithdrawAmount >= config.minUsdcBalanceToWithdraw) {
        console.log('🎉 WITHDRAWAL OPPORTUNITY DETECTED!');
        
        await notificationService.sendNotification({
          title: '🚨 USDC Withdrawal Opportunity!',
          message: `USDC liquidity is now available!\n\nAvailable: ${status.availableLiquidity.toFixed(6)} USDC\nMax Withdraw: ${status.maxWithdrawAmount.toFixed(6)} USDC\n\nAttempting withdrawal...`,
          type: 'success',
          data: {
            availableLiquidity: status.availableLiquidity,
            maxWithdrawAmount: status.maxWithdrawAmount,
            utilizationRate: status.utilizationRate
          }
        });

        // Attempt immediate withdrawal
        await this.attemptWithdrawal(status);
      }

      this.lastStatus = status;

    } catch (error) {
      console.error('❌ Error checking liquidity:', error);
    }
  }

  private async analyzeChanges(oldStatus: PoolStatus, newStatus: PoolStatus): Promise<void> {
    const changes: string[] = [];

    // Check for any liquidity increase
    if (newStatus.availableLiquidity > oldStatus.availableLiquidity) {
      const increase = newStatus.availableLiquidity - oldStatus.availableLiquidity;
      changes.push(`📈 USDC liquidity INCREASED by ${increase.toFixed(6)} USDC!`);
    } else if (newStatus.availableLiquidity < oldStatus.availableLiquidity) {
      const decrease = oldStatus.availableLiquidity - newStatus.availableLiquidity;
      changes.push(`📉 USDC liquidity decreased by ${decrease.toFixed(6)} USDC`);
    }

    // Check for utilization changes
    const utilizationChange = Math.abs(newStatus.utilizationRate - oldStatus.utilizationRate);
    if (utilizationChange > 0.01) { // 1% change
      changes.push(`📊 Utilization changed from ${(oldStatus.utilizationRate * 100).toFixed(2)}% to ${(newStatus.utilizationRate * 100).toFixed(2)}%`);
    }

    // Check for withdrawal possibility changes
    if (!oldStatus.canWithdraw && newStatus.canWithdraw) {
      changes.push('✅ Withdrawal is now possible!');
    } else if (oldStatus.canWithdraw && !newStatus.canWithdraw) {
      changes.push('❌ Withdrawal is no longer possible');
    }

    if (changes.length > 0) {
      console.log('📊 Pool changes detected:');
      changes.forEach(change => console.log(`   ${change}`));

      // Send notification for significant changes
      if (changes.some(c => c.includes('INCREASED') || c.includes('now possible'))) {
        await notificationService.sendNotification({
          title: 'Pool Status Update',
          message: changes.join('\n'),
          type: 'info',
          data: {
            oldLiquidity: oldStatus.availableLiquidity,
            newLiquidity: newStatus.availableLiquidity,
            oldUtilization: oldStatus.utilizationRate,
            newUtilization: newStatus.utilizationRate
          }
        });
      }
    }
  }

  private async attemptWithdrawal(status: PoolStatus): Promise<void> {
    try {
      console.log('💸 Attempting immediate USDC withdrawal...');
      
      // Calculate withdrawal amount (be conservative)
      const withdrawAmount = Math.min(
        status.maxWithdrawAmount * 0.95, // 95% of max to be safe
        status.userDeposits
      );

      console.log(`Withdrawing ${withdrawAmount.toFixed(6)} USDC...`);
      
      const attempt = await this.solendClient.attemptWithdrawal(withdrawAmount);
      
      if (attempt.success) {
        console.log('🎉 Withdrawal successful!');
        
        await notificationService.sendNotification({
          title: '🎉 USDC Withdrawal Successful!',
          message: `Successfully withdrew ${attempt.amount.toFixed(6)} USDC!\n\nTransaction: ${attempt.transactionId}`,
          type: 'success',
          data: attempt
        });

        // Stop monitoring after successful withdrawal
        this.stopMonitoring();
      } else {
        console.log('❌ Withdrawal failed:', attempt.error);
        
        await notificationService.sendNotification({
          title: 'Withdrawal Failed',
          message: `Failed to withdraw USDC: ${attempt.error}`,
          type: 'error',
          data: attempt
        });
      }
      
    } catch (error) {
      console.error('❌ Withdrawal attempt failed:', error);
    }
  }

  stopMonitoring(): void {
    this.isMonitoring = false;
    
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    console.log('🛑 Real-time monitoring stopped');
  }

  getStatus(): any {
    return {
      isMonitoring: this.isMonitoring,
      lastStatus: this.lastStatus,
      checkInterval: config.realtimeCheckIntervalSeconds,
      realtimeEnabled: config.enableRealtimeMonitoring
    };
  }
}
