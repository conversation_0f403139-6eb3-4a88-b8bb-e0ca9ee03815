import dotenv from 'dotenv';
import { Config } from './types';

dotenv.config();

export function loadConfig(): Config {
  const requiredEnvVars = [
    'PRIVATE_KEY',
    'WALLET_ADDRESS', 
    'POOL_ADDRESS',
    'OBLIGATION_ADDRESS',
    'RPC_ENDPOINT'
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }

  return {
    privateKey: process.env.PRIVATE_KEY!,
    walletAddress: process.env.WALLET_ADDRESS!,
    poolAddress: process.env.POOL_ADDRESS!,
    obligationAddress: process.env.OBLIGATION_ADDRESS!,
    rpcEndpoint: process.env.RPC_ENDPOINT!,
    checkIntervalMinutes: parseInt(process.env.CHECK_INTERVAL_MINUTES || '5'),
    minUsdcBalanceToWithdraw: parseFloat(process.env.MIN_USDC_BALANCE_TO_WITHDRAW || '1'),
    enableRealtimeMonitoring: process.env.ENABLE_REALTIME_MONITORING === 'true',
    realtimeCheckIntervalSeconds: Math.max(10, parseInt(process.env.REALTIME_CHECK_INTERVAL_SECONDS || '30')),
    discordWebhookUrl: process.env.DISCORD_WEBHOOK_URL,
    telegramBotToken: process.env.TELEGRAM_BOT_TOKEN,
    telegramChatId: process.env.TELEGRAM_CHAT_ID
  };
}

export const config = loadConfig();
