#!/usr/bin/env node

import { TrunkUsdcMonitor } from './monitor';
import { SolendClient } from './solend-client';
import { config } from './config';

async function showStatus() {
  console.log('📊 Getting current pool status...\n');
  
  const client = new SolendClient();
  await client.initialize();
  
  const status = await client.getPoolStatus();
  const balance = await client.getWalletBalance();
  
  console.log('=== POOL STATUS ===');
  console.log(`Pool Address: ${status.poolAddress}`);
  console.log(`Obligation Address: ${status.obligationAddress}`);
  console.log(`Timestamp: ${status.timestamp.toISOString()}\n`);
  
  console.log('=== POOL INFORMATION (USDC) ===');
  console.log(`Total Supply: ${status.totalSupply.toFixed(6)} USDC`);
  console.log(`Total Borrows: ${status.totalBorrows.toFixed(6)} USDC`);
  console.log(`Available Liquidity: ${status.availableLiquidity.toFixed(6)} USDC`);
  console.log(`Utilization Rate: ${(status.utilizationRate * 100).toFixed(2)}%\n`);

  console.log('=== YOUR POSITION ===');
  console.log(`Your Deposits: ${status.userDeposits.toFixed(6)} USDC`);
  console.log(`Your Borrows: ${status.userBorrows.toFixed(6)}`);
  console.log(`Your Collateral: ${status.userCollateral.toFixed(6)}\n`);
  
  console.log('=== HEALTH METRICS ===');
  console.log(`Borrow Limit: ${status.borrowLimit.toFixed(6)}`);
  console.log(`Liquidation Threshold: ${status.liquidationThreshold.toFixed(6)}`);
  console.log(`Health Factor: ${status.healthFactor.toFixed(2)}\n`);
  
  console.log('=== WITHDRAWAL STATUS ===');
  console.log(`Can Withdraw: ${status.canWithdraw ? '✅ YES' : '❌ NO'}`);
  console.log(`Max Withdraw Amount: ${status.maxWithdrawAmount.toFixed(6)} USDC\n`);
  
  console.log('=== TOKEN PRICES ===');
  console.log(`TRUNK Price: $${status.trunkPrice.toFixed(6)}`);
  console.log(`USDC Price: $${status.usdcPrice.toFixed(2)}\n`);
  
  console.log('=== WALLET BALANCE ===');
  console.log(`SOL: ${balance.sol.toFixed(6)}`);
  console.log(`USDC: ${balance.usdc.toFixed(6)}\n`);
  
  if (status.canWithdraw && status.maxWithdrawAmount >= config.minUsdcBalanceToWithdraw) {
    console.log('🎉 WITHDRAWAL IS POSSIBLE!');
    console.log(`You can withdraw up to ${status.maxWithdrawAmount.toFixed(6)} USDC`);
  } else if (!status.canWithdraw) {
    console.log('⚠️ Withdrawal is not currently possible');
    if (status.userBorrows > 0) {
      console.log('Reason: You have outstanding borrows');
    } else if (status.healthFactor < 1.5) {
      console.log('Reason: Health factor too low');
    }
  } else if (status.maxWithdrawAmount === 0) {
    console.log('🚨 NO USDC LIQUIDITY AVAILABLE!');
    console.log('Your USDC is locked because:');
    console.log('- All USDC in the pool has been borrowed (100% utilization)');
    console.log('- You must wait for borrowers to repay their USDC loans');
    console.log('- Monitor will watch for liquidity to become available');
  } else {
    console.log('⚠️ Withdrawal amount below minimum threshold');
  }
}

async function startMonitor() {
  console.log('🚀 Starting TRUNK-USDC Pool Monitor...\n');
  
  const monitor = new TrunkUsdcMonitor();
  await monitor.initialize();
  monitor.startMonitoring();
  
  console.log('Monitor is running. Press Ctrl+C to stop.\n');
  
  // Keep process alive
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping monitor...');
    process.exit(0);
  });
}

async function main() {
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'status':
        await showStatus();
        break;
      case 'monitor':
        await startMonitor();
        break;
      default:
        console.log('TRUNK-USDC Pool Monitor\n');
        console.log('Usage:');
        console.log('  npm run dev status   - Show current pool status');
        console.log('  npm run dev monitor  - Start continuous monitoring');
        console.log('  npm run monitor      - Start continuous monitoring (production)');
        console.log('\nConfiguration:');
        console.log(`  Wallet: ${config.walletAddress}`);
        console.log(`  Pool: ${config.poolAddress}`);
        console.log(`  Obligation: ${config.obligationAddress}`);
        console.log(`  Check Interval: ${config.checkIntervalMinutes} minutes`);
        console.log(`  Min Withdraw: ${config.minUsdcBalanceToWithdraw} TRUNK`);
        break;
    }
  } catch (error) {
    console.error('❌ Error:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
