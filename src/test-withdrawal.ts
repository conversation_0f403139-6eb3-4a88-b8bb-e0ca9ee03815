#!/usr/bin/env node

import { SolendClient } from './solend-client';

async function testWithdrawal() {
  console.log('🧪 Testing USDC withdrawal...\n');
  
  try {
    // Initialize client
    const client = new SolendClient();
    await client.initialize();
    
    // Get current status
    const status = await client.getPoolStatus();
    console.log(`Current status:`);
    console.log(`- Your deposits: ${status.userDeposits.toFixed(6)} USDC`);
    console.log(`- Available liquidity: ${status.availableLiquidity.toFixed(6)} USDC`);
    console.log(`- Max withdraw: ${status.maxWithdrawAmount.toFixed(6)} USDC`);
    console.log(`- Can withdraw: ${status.canWithdraw}`);
    
    if (!status.canWithdraw) {
      console.log('❌ Cannot withdraw - conditions not met');
      return;
    }
    
    if (status.maxWithdrawAmount < 0.01) {
      console.log('❌ Cannot withdraw - amount too small');
      return;
    }
    
    // Test with a small amount first (1 USDC)
    const testAmount = Math.min(1.0, status.maxWithdrawAmount * 0.1);
    console.log(`\n🧪 Testing withdrawal of ${testAmount.toFixed(6)} USDC...`);
    
    const attempt = await client.attemptWithdrawal(testAmount);
    
    if (attempt.success) {
      console.log('✅ Test withdrawal successful!');
      console.log(`Transaction ID: ${attempt.transactionId}`);
    } else {
      console.log('❌ Test withdrawal failed:');
      console.log(`Error: ${attempt.error}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testWithdrawal();
}

export { testWithdrawal };
