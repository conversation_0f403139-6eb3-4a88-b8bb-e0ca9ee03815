import { PublicKey } from '@solana/web3.js';

export interface PoolStatus {
  poolAddress: string;
  obligationAddress: string;
  timestamp: Date;
  
  // Pool information
  totalSupply: number;
  totalBorrows: number;
  availableLiquidity: number;
  utilizationRate: number;
  
  // User position
  userDeposits: number;
  userBorrows: number;
  userCollateral: number;
  
  // Health metrics
  borrowLimit: number;
  liquidationThreshold: number;
  healthFactor: number;
  
  // Withdrawal status
  canWithdraw: boolean;
  maxWithdrawAmount: number;
  
  // Token prices
  trunkPrice: number;
  usdcPrice: number;
}

export interface WithdrawalAttempt {
  timestamp: Date;
  amount: number;
  success: boolean;
  transactionId?: string;
  error?: string;
}

export interface Config {
  privateKey: string;
  walletAddress: string;
  poolAddress: string;
  obligationAddress: string;
  rpcEndpoint: string;
  checkIntervalMinutes: number;
  minUsdcBalanceToWithdraw: number;
  enableRealtimeMonitoring: boolean;
  realtimeCheckIntervalSeconds: number;
  discordWebhookUrl?: string;
  telegramBotToken?: string;
  telegramChatId?: string;
}

export interface NotificationMessage {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  data?: any;
}
