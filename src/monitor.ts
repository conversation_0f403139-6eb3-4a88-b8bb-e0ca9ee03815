import * as cron from 'node-cron';
import { SolendClient } from './solend-client';
import { RealtimeMonitor } from './realtime-monitor';
import { notificationService } from './notifications';
import { config } from './config';
import { PoolStatus, WithdrawalAttempt } from './types';

class TrunkUsdcMonitor {
  private solendClient: SolendClient;
  private realtimeMonitor: RealtimeMonitor | null = null;
  private lastStatus: PoolStatus | null = null;
  private withdrawalAttempts: WithdrawalAttempt[] = [];
  private isRunning = false;

  constructor() {
    this.solendClient = new SolendClient();
    if (config.enableRealtimeMonitoring) {
      this.realtimeMonitor = new RealtimeMonitor();
    }
  }

  async initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing USDC Monitor...');

      await this.solendClient.initialize();

      if (this.realtimeMonitor) {
        await this.realtimeMonitor.initialize();
      }

      const monitoringMode = config.enableRealtimeMonitoring ?
        `Real-time monitoring (${config.realtimeCheckIntervalSeconds}s intervals)` :
        `Standard monitoring (${config.checkIntervalMinutes} minute intervals)`;

      await notificationService.sendNotification({
        title: 'USDC Monitor Started',
        message: `USDC pool monitor initialized successfully.\n\nPool: ${config.poolAddress}\nObligation: ${config.obligationAddress}\nWallet: ${config.walletAddress}\n\nMode: ${monitoringMode}\nMin withdrawal: ${config.minUsdcBalanceToWithdraw} USDC`,
        type: 'info'
      });

      console.log('✅ Monitor initialized successfully');
      console.log(`📊 Monitoring mode: ${monitoringMode}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      await notificationService.sendNotification({
        title: 'Monitor Initialization Failed',
        message: `Failed to initialize the monitor: ${errorMessage}`,
        type: 'error'
      });

      throw error;
    }
  }

  async checkPoolStatus(): Promise<void> {
    if (this.isRunning) {
      console.log('⏳ Check already in progress, skipping...');
      return;
    }

    this.isRunning = true;
    
    try {
      console.log('🔍 Checking pool status...');
      
      const status = await this.solendClient.getPoolStatus();
      
      // Log current status
      console.log(`Pool Status at ${status.timestamp.toISOString()}:`);
      console.log(`- User Deposits: ${status.userDeposits.toFixed(6)} USDC`);
      console.log(`- User Borrows: ${status.userBorrows.toFixed(6)}`);
      console.log(`- Health Factor: ${status.healthFactor.toFixed(2)}`);
      console.log(`- Can Withdraw: ${status.canWithdraw}`);
      console.log(`- Max Withdraw: ${status.maxWithdrawAmount.toFixed(6)} USDC`);
      console.log(`- Available Liquidity: ${status.availableLiquidity.toFixed(6)} USDC`);
      
      // Check for significant changes
      if (this.lastStatus) {
        await this.checkForSignificantChanges(this.lastStatus, status);
      }
      
      // Attempt withdrawal if conditions are met
      if (status.canWithdraw && status.maxWithdrawAmount >= config.minUsdcBalanceToWithdraw) {
        await this.attemptWithdrawal(status);
      }
      
      this.lastStatus = status;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ Error checking pool status:', errorMessage);
      
      await notificationService.sendNotification({
        title: 'Pool Status Check Failed',
        message: `Failed to check pool status: ${errorMessage}`,
        type: 'error'
      });
    } finally {
      this.isRunning = false;
    }
  }

  private async checkForSignificantChanges(oldStatus: PoolStatus, newStatus: PoolStatus): Promise<void> {
    const changes: string[] = [];
    
    // Check for withdrawal availability change
    if (!oldStatus.canWithdraw && newStatus.canWithdraw) {
      changes.push('✅ Withdrawal is now possible!');
    } else if (oldStatus.canWithdraw && !newStatus.canWithdraw) {
      changes.push('❌ Withdrawal is no longer possible');
    }
    
    // Check for significant liquidity changes (especially important for USDC)
    const liquidityChange = Math.abs(newStatus.availableLiquidity - oldStatus.availableLiquidity);
    if (liquidityChange > 0.01) { // Any change > 0.01 USDC is significant when liquidity is low
      changes.push(`💧 USDC liquidity changed by ${liquidityChange.toFixed(6)} USDC (${oldStatus.availableLiquidity.toFixed(6)} → ${newStatus.availableLiquidity.toFixed(6)})`);
    }
    
    // Check for health factor changes
    const healthFactorChange = Math.abs(newStatus.healthFactor - oldStatus.healthFactor);
    if (healthFactorChange > 0.5) {
      changes.push(`❤️ Health factor changed from ${oldStatus.healthFactor.toFixed(2)} to ${newStatus.healthFactor.toFixed(2)}`);
    }
    
    // Check for max withdraw amount changes
    const withdrawChange = Math.abs(newStatus.maxWithdrawAmount - oldStatus.maxWithdrawAmount);
    if (withdrawChange > 0.01) { // Any change > 0.01 USDC is significant
      changes.push(`💰 Max withdraw amount changed by ${withdrawChange.toFixed(6)} USDC (${oldStatus.maxWithdrawAmount.toFixed(6)} → ${newStatus.maxWithdrawAmount.toFixed(6)})`);
    }
    
    if (changes.length > 0) {
      await notificationService.sendNotification({
        title: 'Pool Status Changes Detected',
        message: changes.join('\n'),
        type: 'info',
        data: {
          oldStatus: {
            canWithdraw: oldStatus.canWithdraw,
            maxWithdrawAmount: oldStatus.maxWithdrawAmount,
            healthFactor: oldStatus.healthFactor,
            availableLiquidity: oldStatus.availableLiquidity
          },
          newStatus: {
            canWithdraw: newStatus.canWithdraw,
            maxWithdrawAmount: newStatus.maxWithdrawAmount,
            healthFactor: newStatus.healthFactor,
            availableLiquidity: newStatus.availableLiquidity
          }
        }
      });
    }
  }

  private async attemptWithdrawal(status: PoolStatus): Promise<void> {
    try {
      console.log('💸 Attempting USDC withdrawal...');

      // Calculate withdrawal amount (be conservative)
      const withdrawAmount = Math.min(
        status.maxWithdrawAmount * 0.95, // 95% of max to be safe
        status.userDeposits
      );

      if (withdrawAmount < config.minUsdcBalanceToWithdraw) {
        console.log(`⏭️ Withdrawal amount ${withdrawAmount.toFixed(6)} USDC is below minimum threshold`);
        return;
      }

      console.log(`Attempting to withdraw ${withdrawAmount.toFixed(6)} USDC...`);
      
      const attempt = await this.solendClient.attemptWithdrawal(withdrawAmount);
      this.withdrawalAttempts.push(attempt);
      
      if (attempt.success) {
        await notificationService.sendNotification({
          title: '🎉 USDC Withdrawal Successful!',
          message: `Successfully withdrew ${attempt.amount.toFixed(6)} USDC from the pool!\n\nTransaction ID: ${attempt.transactionId}`,
          type: 'success',
          data: {
            amount: attempt.amount,
            transactionId: attempt.transactionId,
            timestamp: attempt.timestamp
          }
        });
      } else {
        await notificationService.sendNotification({
          title: 'USDC Withdrawal Failed',
          message: `Failed to withdraw ${attempt.amount.toFixed(6)} USDC: ${attempt.error}`,
          type: 'error',
          data: attempt
        });
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ Withdrawal attempt failed:', errorMessage);
      
      await notificationService.sendNotification({
        title: 'Withdrawal Attempt Error',
        message: `Error during withdrawal attempt: ${errorMessage}`,
        type: 'error'
      });
    }
  }

  startMonitoring(): void {
    if (config.enableRealtimeMonitoring && this.realtimeMonitor) {
      console.log('🚀 Starting real-time monitoring...');
      this.realtimeMonitor.startRealtimeMonitoring();
    } else {
      console.log(`🔄 Starting standard monitoring with ${config.checkIntervalMinutes} minute intervals...`);

      // Run initial check
      this.checkPoolStatus();

      // Schedule periodic checks
      const cronExpression = `*/${config.checkIntervalMinutes} * * * *`;
      cron.schedule(cronExpression, () => {
        this.checkPoolStatus();
      });
    }

    console.log('✅ Monitoring started successfully');
  }

  getStats(): any {
    return {
      lastStatus: this.lastStatus,
      withdrawalAttempts: this.withdrawalAttempts,
      totalAttempts: this.withdrawalAttempts.length,
      successfulAttempts: this.withdrawalAttempts.filter(a => a.success).length,
      isRunning: this.isRunning,
      realtimeMonitoring: config.enableRealtimeMonitoring,
      realtimeStatus: this.realtimeMonitor?.getStatus()
    };
  }
}

// Main execution
async function main() {
  const monitor = new TrunkUsdcMonitor();
  
  try {
    await monitor.initialize();
    monitor.startMonitoring();
    
    // Keep the process running
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down monitor...');
      
      await notificationService.sendNotification({
        title: 'Monitor Stopped',
        message: 'TRUNK-USDC pool monitor has been stopped.',
        type: 'info',
        data: monitor.getStats()
      });
      
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Failed to start monitor:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { TrunkUsdcMonitor };
