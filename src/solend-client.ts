import { Connection, PublicKey, Keypair } from '@solana/web3.js';
import { SolendMarket, SolendAction } from '@solendprotocol/solend-sdk';
import { config } from './config';
import { PoolStatus, WithdrawalAttempt } from './types';
import bs58 from 'bs58';
import BN from 'bn.js';

export class SolendClient {
  private connection: Connection;
  private wallet: Keypair;
  private market: SolendMarket | null = null;

  constructor() {
    this.connection = new Connection(config.rpcEndpoint, 'confirmed');
    
    try {
      // Decode the private key from base58
      const privateKeyBytes = bs58.decode(config.privateKey);
      this.wallet = Keypair.fromSecretKey(privateKeyBytes);
      
      // Verify the wallet address matches
      if (this.wallet.publicKey.toString() !== config.walletAddress) {
        throw new Error('Private key does not match the provided wallet address');
      }
    } catch (error) {
      throw new Error(`Failed to load wallet: ${error}`);
    }
  }

  async initialize(): Promise<void> {
    try {
      console.log('Initializing Solend market...');
      console.log('This appears to be a permissionless pool, trying to find the correct market...');

      // Based on diagnostic results, the pool address is actually the market address
      // for this permissionless pool
      console.log(`Initializing with pool address as market: ${config.poolAddress}`);

      this.market = await SolendMarket.initialize(
        this.connection,
        'production',
        config.poolAddress // Use pool address as market address
      );

      await this.market.loadReserves();

      console.log('Solend market initialized successfully');

      // Log available reserves for debugging
      console.log('Available reserves in this market:');
      if (this.market) {
        this.market.reserves.forEach(reserve => {
          console.log(`- ${reserve.config.liquidityToken.symbol}: ${reserve.config.liquidityToken.mint} (reserve: ${reserve.config.address})`);
        });
      }

    } catch (error) {
      console.error('Failed to initialize Solend market:', error);
      throw error;
    }
  }

  async getPoolStatus(): Promise<PoolStatus> {
    if (!this.market) {
      throw new Error('Market not initialized. Call initialize() first.');
    }

    try {
      // Refresh market data
      await this.market.refreshAll();
      
      // The pool address might not be a reserve address but rather a market address
      // Let's look for TRUNK and USDC reserves in this market since you mentioned TRUNK/USDC pool
      console.log('Looking for TRUNK and USDC reserves in this market...');

      const trunkReserve = this.market.reserves.find(r =>
        r.config.liquidityToken.symbol === 'TRUNK' ||
        r.config.liquidityToken.name.toLowerCase().includes('trunk')
      );

      const usdcReserve = this.market.reserves.find(r =>
        r.config.liquidityToken.symbol === 'USDC' ||
        r.config.liquidityToken.mint === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' // USDC mint
      );

      console.log(`TRUNK reserve found: ${trunkReserve ? 'YES' : 'NO'}`);
      console.log(`USDC reserve found: ${usdcReserve ? 'YES' : 'NO'}`);

      if (trunkReserve) {
        console.log(`TRUNK reserve: ${trunkReserve.config.liquidityToken.symbol} (${trunkReserve.config.liquidityToken.mint}) - Reserve: ${trunkReserve.config.address}`);
      }
      if (usdcReserve) {
        console.log(`USDC reserve: ${usdcReserve.config.liquidityToken.symbol} (${usdcReserve.config.liquidityToken.mint}) - Reserve: ${usdcReserve.config.address}`);
      }

      if (!trunkReserve && !usdcReserve) {
        console.log('Neither TRUNK nor USDC reserves found in this market');
        console.log('This might not be the correct market for the TRUNK/USDC pool');

        // Let's check if the pool address is actually an obligation or market address
        const poolAccount = await this.connection.getAccountInfo(new PublicKey(config.poolAddress));
        if (!poolAccount) {
          throw new Error(`Pool account ${config.poolAddress} not found`);
        }
        console.log(`Pool account found with owner: ${poolAccount.owner.toString()}`);
        throw new Error('Could not find TRUNK or USDC reserves in any market - this might be a different type of pool');
      }

      // Get user obligation
      const obligation = await this.market.fetchObligationByWallet(
        new PublicKey(config.walletAddress)
      );

      let userDeposits = 0;
      let userBorrows = 0;
      let userCollateral = 0;
      let canWithdraw = false;
      let maxWithdrawAmount = 0;
      let healthFactor = 0;
      let borrowLimit = 0;
      let liquidationThreshold = 0;

      // Determine which reserve to use for calculations
      // Since you mentioned you deposited USDC, prioritize USDC reserve
      const primaryReserve = usdcReserve || trunkReserve;

      if (obligation) {
        const stats = obligation.obligationStats;
        userDeposits = stats.userTotalDeposit;
        userBorrows = stats.userTotalBorrow;
        borrowLimit = stats.borrowLimit;
        liquidationThreshold = stats.liquidationThreshold;
        healthFactor = stats.borrowLimit > 0 ? stats.borrowLimit / Math.max(stats.userTotalBorrow, 0.01) : Infinity;

        // Check if user can withdraw
        canWithdraw = stats.userTotalBorrow === 0 || healthFactor > 1.5;

        // Calculate max withdraw amount (conservative approach)
        if (canWithdraw && primaryReserve && primaryReserve.stats) {
          const availableLiquidity = parseFloat(primaryReserve.stats.totalLiquidityWads.toString()) / Math.pow(10, primaryReserve.stats.decimals);
          maxWithdrawAmount = Math.min(
            userDeposits,
            availableLiquidity
          );
        }

        // Special handling for USDC deposits in TRUNK/USDC pool
        if (obligation.deposits.length > 0) {
          const usdcMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
          const usdcDeposit = obligation.deposits.find(d => d.mintAddress === usdcMint);

          if (usdcDeposit && usdcReserve && usdcReserve.stats) {
            const usdcAmount = parseFloat(usdcDeposit.amount.toString()) / Math.pow(10, usdcReserve.stats.decimals);
            const usdcLiquidity = parseFloat(usdcReserve.stats.totalLiquidityWads.toString()) / Math.pow(10, usdcReserve.stats.decimals);

            console.log(`USDC deposit found: ${usdcAmount.toFixed(6)} USDC`);
            console.log(`USDC liquidity available: ${usdcLiquidity.toFixed(6)} USDC`);

            if (canWithdraw) {
              maxWithdrawAmount = Math.min(usdcAmount, usdcLiquidity);
              console.log(`Max USDC withdrawal: ${maxWithdrawAmount.toFixed(6)} USDC`);
            }
          }
        }

        // Log detailed position info for debugging
        console.log('Obligation details:');
        console.log(`- Deposits: ${obligation.deposits.length} positions`);
        console.log(`- Borrows: ${obligation.borrows.length} positions`);
        obligation.deposits.forEach((deposit, i) => {
          console.log(`  Deposit ${i}: ${deposit.amount.toString()} of ${deposit.mintAddress}`);
        });
        obligation.borrows.forEach((borrow, i) => {
          console.log(`  Borrow ${i}: ${borrow.amount.toString()} of ${borrow.mintAddress}`);
        });
      }

      return {
        poolAddress: config.poolAddress,
        obligationAddress: config.obligationAddress,
        timestamp: new Date(),
        
        // Pool information (using USDC reserve since that's what user has deposited)
        totalSupply: usdcReserve && usdcReserve.stats ? parseFloat(usdcReserve.stats.totalDepositsWads.toString()) / Math.pow(10, usdcReserve.stats.decimals) : 0,
        totalBorrows: usdcReserve && usdcReserve.stats ? parseFloat(usdcReserve.stats.totalBorrowsWads.toString()) / Math.pow(10, usdcReserve.stats.decimals) : 0,
        availableLiquidity: usdcReserve && usdcReserve.stats ? parseFloat(usdcReserve.stats.totalLiquidityWads.toString()) / Math.pow(10, usdcReserve.stats.decimals) : 0,
        utilizationRate: usdcReserve && usdcReserve.stats ? (parseFloat(usdcReserve.stats.totalBorrowsWads.toString()) / Math.max(parseFloat(usdcReserve.stats.totalDepositsWads.toString()), 1)) : 0,
        
        // User position (convert to actual USDC amounts)
        userDeposits: obligation && obligation.deposits.length > 0 ?
          parseFloat(obligation.deposits.find(d => d.mintAddress === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')?.amount.toString() || '0') / Math.pow(10, 6) : 0,
        userBorrows,
        userCollateral,
        
        // Health metrics
        borrowLimit,
        liquidationThreshold,
        healthFactor,
        
        // Withdrawal status
        canWithdraw,
        maxWithdrawAmount,
        
        // Token prices (placeholder - would need price oracle)
        trunkPrice: 0.001, // Very low price assumption
        usdcPrice: 1.0
      };
      
    } catch (error) {
      console.error('Error getting pool status:', error);
      throw error;
    }
  }

  async attemptWithdrawal(amount: number): Promise<WithdrawalAttempt> {
    const attempt: WithdrawalAttempt = {
      timestamp: new Date(),
      amount,
      success: false
    };

    try {
      if (!this.market) {
        throw new Error('Market not initialized');
      }

      console.log(`Attempting to withdraw ${amount} TRUNK...`);
      
      // Check what the user actually has deposited
      const obligation = await this.market.fetchObligationByWallet(
        new PublicKey(config.walletAddress)
      );

      if (!obligation || obligation.deposits.length === 0) {
        throw new Error('No deposits found to withdraw');
      }

      // Find the USDC deposit (most likely what user wants to withdraw)
      const usdcMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
      const usdcDeposit = obligation.deposits.find(d => d.mintAddress === usdcMint);

      if (!usdcDeposit) {
        throw new Error('No USDC deposit found to withdraw');
      }

      // Find USDC reserve to get correct decimals
      const usdcReserve = this.market.reserves.find(r =>
        r.config.liquidityToken.mint === usdcMint
      );

      if (!usdcReserve || !usdcReserve.stats) {
        throw new Error('USDC reserve not found for withdrawal');
      }

      // Build withdrawal transaction for USDC
      const decimals = usdcReserve.stats.decimals;
      const solendAction = await SolendAction.buildWithdrawTxns(
        this.connection,
        new BN(amount * Math.pow(10, decimals)), // Convert to smallest unit with correct decimals
        'USDC', // Withdraw USDC, not TRUNK
        this.wallet.publicKey,
        'production'
      );

      // Send transaction
      const signature = await solendAction.sendTransactions(
        async (transaction) => {
          transaction.sign(this.wallet);
          return await this.connection.sendRawTransaction(transaction.serialize());
        }
      );

      attempt.success = true;
      attempt.transactionId = signature;
      
      console.log(`Withdrawal successful! Transaction: ${signature}`);
      
    } catch (error) {
      attempt.error = error instanceof Error ? error.message : String(error);
      console.error('Withdrawal failed:', attempt.error);
    }

    return attempt;
  }

  async getWalletBalance(): Promise<{ sol: number; usdc: number }> {
    try {
      const solBalance = await this.connection.getBalance(this.wallet.publicKey);
      
      // For USDC balance, we'd need to check the associated token account
      // This is a simplified version
      return {
        sol: solBalance / 1e9,
        usdc: 0 // Would need to implement USDC token account checking
      };
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      return { sol: 0, usdc: 0 };
    }
  }
}
