import { Connection, PublicKey, Keypair } from '@solana/web3.js';
import { SolendMarket, SolendAction } from '@solendprotocol/solend-sdk';
import { config } from './config';
import { PoolStatus, WithdrawalAttempt } from './types';
import bs58 from 'bs58';
import BN from 'bn.js';

export class SolendClient {
  private connection: Connection;
  private wallet: Keypair;
  private market: SolendMarket | null = null;

  constructor() {
    this.connection = new Connection(config.rpcEndpoint, 'confirmed');
    
    try {
      // Decode the private key from base58
      const privateKeyBytes = bs58.decode(config.privateKey);
      this.wallet = Keypair.fromSecretKey(privateKeyBytes);
      
      // Verify the wallet address matches
      if (this.wallet.publicKey.toString() !== config.walletAddress) {
        throw new Error('Private key does not match the provided wallet address');
      }
    } catch (error) {
      throw new Error(`Failed to load wallet: ${error}`);
    }
  }

  async initialize(): Promise<void> {
    try {
      console.log('Initializing Solend market...');

      // Try to initialize with the specific market address from the pool
      // Save.Finance might be using a custom market
      try {
        this.market = await SolendMarket.initialize(
          this.connection,
          'production',
          config.poolAddress // Try using the pool address as market address
        );
      } catch (error) {
        console.log('Failed to initialize with pool address as market, trying main market...');
        // Fallback to main market
        this.market = await SolendMarket.initialize(
          this.connection,
          'production'
        );
      }

      await this.market.loadReserves();
      console.log('Solend market initialized successfully');

      // Log available reserves for debugging
      console.log('Available reserves:');
      this.market.reserves.forEach(reserve => {
        console.log(`- ${reserve.config.liquidityToken.symbol}: ${reserve.config.liquidityToken.mint}`);
      });

    } catch (error) {
      console.error('Failed to initialize Solend market:', error);
      throw error;
    }
  }

  async getPoolStatus(): Promise<PoolStatus> {
    if (!this.market) {
      throw new Error('Market not initialized. Call initialize() first.');
    }

    try {
      // Refresh market data
      await this.market.refreshAll();
      
      // Find TRUNK and USDC reserves
      const trunkReserve = this.market.reserves.find(r =>
        r.config.liquidityToken.symbol === 'TRUNK' ||
        r.config.liquidityToken.mint === config.poolAddress ||
        r.config.address === config.poolAddress
      );

      const usdcReserve = this.market.reserves.find(r => r.config.liquidityToken.symbol === 'USDC');

      if (!trunkReserve) {
        // If we can't find TRUNK in the market, let's try to get account info directly
        console.log('TRUNK reserve not found in market, checking pool address directly...');
        const poolAccount = await this.connection.getAccountInfo(new PublicKey(config.poolAddress));
        if (!poolAccount) {
          throw new Error(`Pool account ${config.poolAddress} not found`);
        }
        console.log(`Pool account found with owner: ${poolAccount.owner.toString()}`);
        throw new Error('TRUNK reserve not found in market - Save.Finance might be using a different protocol');
      }

      if (!usdcReserve) {
        console.log('USDC reserve not found in market, this might be a custom market');
      }

      // Get user obligation
      const obligation = await this.market.fetchObligationByWallet(
        new PublicKey(config.walletAddress)
      );

      let userDeposits = 0;
      let userBorrows = 0;
      let userCollateral = 0;
      let canWithdraw = false;
      let maxWithdrawAmount = 0;
      let healthFactor = 0;
      let borrowLimit = 0;
      let liquidationThreshold = 0;

      if (obligation) {
        const stats = obligation.obligationStats;
        userDeposits = stats.userTotalDeposit;
        userBorrows = stats.userTotalBorrow;
        borrowLimit = stats.borrowLimit;
        liquidationThreshold = stats.liquidationThreshold;
        healthFactor = stats.borrowLimit > 0 ? stats.borrowLimit / Math.max(stats.userTotalBorrow, 0.01) : Infinity;

        // Check if user can withdraw
        canWithdraw = stats.userTotalBorrow === 0 || healthFactor > 1.5;

        // Calculate max withdraw amount (conservative approach)
        if (canWithdraw && trunkReserve.stats) {
          const availableLiquidity = parseFloat(trunkReserve.stats.totalLiquidityWads.toString()) / Math.pow(10, trunkReserve.stats.decimals);
          maxWithdrawAmount = Math.min(
            userDeposits,
            availableLiquidity
          );
        }
      }

      return {
        poolAddress: config.poolAddress,
        obligationAddress: config.obligationAddress,
        timestamp: new Date(),
        
        // Pool information (using string conversion to avoid overflow)
        totalSupply: trunkReserve.stats ? parseFloat(trunkReserve.stats.totalDepositsWads.toString()) / Math.pow(10, trunkReserve.stats.decimals) : 0,
        totalBorrows: trunkReserve.stats ? parseFloat(trunkReserve.stats.totalBorrowsWads.toString()) / Math.pow(10, trunkReserve.stats.decimals) : 0,
        availableLiquidity: trunkReserve.stats ? parseFloat(trunkReserve.stats.totalLiquidityWads.toString()) / Math.pow(10, trunkReserve.stats.decimals) : 0,
        utilizationRate: trunkReserve.stats ? (parseFloat(trunkReserve.stats.totalBorrowsWads.toString()) / Math.max(parseFloat(trunkReserve.stats.totalDepositsWads.toString()), 1)) : 0,
        
        // User position
        userDeposits,
        userBorrows,
        userCollateral,
        
        // Health metrics
        borrowLimit,
        liquidationThreshold,
        healthFactor,
        
        // Withdrawal status
        canWithdraw,
        maxWithdrawAmount,
        
        // Token prices (placeholder - would need price oracle)
        trunkPrice: 0.001, // Very low price assumption
        usdcPrice: 1.0
      };
      
    } catch (error) {
      console.error('Error getting pool status:', error);
      throw error;
    }
  }

  async attemptWithdrawal(amount: number): Promise<WithdrawalAttempt> {
    const attempt: WithdrawalAttempt = {
      timestamp: new Date(),
      amount,
      success: false
    };

    try {
      if (!this.market) {
        throw new Error('Market not initialized');
      }

      console.log(`Attempting to withdraw ${amount} TRUNK...`);
      
      // Find TRUNK reserve to get correct decimals
      const trunkReserve = this.market.reserves.find(r =>
        r.config.liquidityToken.symbol === 'TRUNK' ||
        r.config.liquidityToken.mint === config.poolAddress ||
        r.config.address === config.poolAddress
      );

      if (!trunkReserve || !trunkReserve.stats) {
        throw new Error('TRUNK reserve not found for withdrawal');
      }

      // Build withdrawal transaction
      const decimals = trunkReserve.stats.decimals;
      const solendAction = await SolendAction.buildWithdrawTxns(
        this.connection,
        new BN(amount * Math.pow(10, decimals)), // Convert to smallest unit with correct decimals
        'TRUNK',
        this.wallet.publicKey,
        'production'
      );

      // Send transaction
      const signature = await solendAction.sendTransactions(
        async (transaction) => {
          transaction.sign(this.wallet);
          return await this.connection.sendRawTransaction(transaction.serialize());
        }
      );

      attempt.success = true;
      attempt.transactionId = signature;
      
      console.log(`Withdrawal successful! Transaction: ${signature}`);
      
    } catch (error) {
      attempt.error = error instanceof Error ? error.message : String(error);
      console.error('Withdrawal failed:', attempt.error);
    }

    return attempt;
  }

  async getWalletBalance(): Promise<{ sol: number; usdc: number }> {
    try {
      const solBalance = await this.connection.getBalance(this.wallet.publicKey);
      
      // For USDC balance, we'd need to check the associated token account
      // This is a simplified version
      return {
        sol: solBalance / 1e9,
        usdc: 0 // Would need to implement USDC token account checking
      };
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      return { sol: 0, usdc: 0 };
    }
  }
}
