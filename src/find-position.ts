#!/usr/bin/env node

import { Connection, PublicKey } from '@solana/web3.js';
import { SolendMarket } from '@solendprotocol/solend-sdk';
import { config } from './config';

async function findUserPosition() {
  console.log('🔍 Searching for your USDC position across all Solend markets...\n');
  
  const connection = new Connection(config.rpcEndpoint, 'confirmed');
  const walletPubkey = new PublicKey(config.walletAddress);
  
  try {
    // Try different markets
    const marketsToCheck = [
      { name: 'Main Market', address: undefined },
      { name: 'Pool Address as Market', address: config.poolAddress },
    ];
    
    for (const marketInfo of marketsToCheck) {
      console.log(`\n📊 Checking ${marketInfo.name}...`);
      
      try {
        const market = await SolendMarket.initialize(
          connection,
          'production',
          marketInfo.address
        );
        
        await market.loadReserves();
        
        // Look for user obligations
        const obligation = await market.fetchObligationByWallet(walletPubkey);
        
        if (obligation) {
          console.log(`✅ Found obligation in ${marketInfo.name}!`);
          console.log(`   Obligation Address: ${obligation.obligationAddress.toString()}`);
          console.log(`   Wallet Address: ${obligation.walletAddress.toString()}`);
          console.log(`   Deposits: ${obligation.deposits.length} positions`);
          console.log(`   Borrows: ${obligation.borrows.length} positions`);
          
          if (obligation.deposits.length > 0) {
            console.log('\n   📈 Deposit Positions:');
            obligation.deposits.forEach((deposit, i) => {
              const reserve = market.reserves.find(r => 
                r.config.liquidityToken.mint === deposit.mintAddress
              );
              const symbol = reserve ? reserve.config.liquidityToken.symbol : 'Unknown';
              const decimals = reserve ? reserve.stats?.decimals || 6 : 6;
              const amount = parseFloat(deposit.amount.toString()) / Math.pow(10, decimals);
              
              console.log(`     ${i + 1}. ${amount.toFixed(6)} ${symbol} (${deposit.mintAddress})`);
            });
          }
          
          if (obligation.borrows.length > 0) {
            console.log('\n   📉 Borrow Positions:');
            obligation.borrows.forEach((borrow, i) => {
              const reserve = market.reserves.find(r => 
                r.config.liquidityToken.mint === borrow.mintAddress
              );
              const symbol = reserve ? reserve.config.liquidityToken.symbol : 'Unknown';
              const decimals = reserve ? reserve.stats?.decimals || 6 : 6;
              const amount = parseFloat(borrow.amount.toString()) / Math.pow(10, decimals);
              
              console.log(`     ${i + 1}. ${amount.toFixed(6)} ${symbol} (${borrow.mintAddress})`);
            });
          }
          
          console.log(`\n   💰 Stats:`);
          console.log(`     Total Deposits: $${obligation.obligationStats.userTotalDeposit.toFixed(2)}`);
          console.log(`     Total Borrows: $${obligation.obligationStats.userTotalBorrow.toFixed(2)}`);
          console.log(`     Health Factor: ${obligation.obligationStats.borrowLimit > 0 ? (obligation.obligationStats.borrowLimit / Math.max(obligation.obligationStats.userTotalBorrow, 0.01)).toFixed(2) : 'Infinity'}`);
          
        } else {
          console.log(`❌ No obligation found in ${marketInfo.name}`);
        }
        
      } catch (error) {
        console.log(`❌ Failed to check ${marketInfo.name}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
    
    // Also check if we can find any obligations directly associated with the wallet
    console.log('\n🔍 Searching for obligations directly...');
    
    // Try to find obligations by scanning program accounts
    try {
      const solendProgramId = new PublicKey('So1endDq2YkqhipRh3WViPa8hdiSpxWy6z3Z6tMCpAo');
      
      console.log('Scanning for obligation accounts...');
      const obligationAccounts = await connection.getProgramAccounts(solendProgramId, {
        filters: [
          { dataSize: 1300 }, // Approximate size of obligation accounts
        ]
      });
      
      console.log(`Found ${obligationAccounts.length} potential obligation accounts`);
      
      // Check a few of them to see if any belong to our wallet
      for (let i = 0; i < Math.min(obligationAccounts.length, 10); i++) {
        const account = obligationAccounts[i];
        console.log(`Checking account ${i + 1}: ${account.pubkey.toString()}`);
        
        // Try to parse as obligation (this is a simplified check)
        // In a real implementation, you'd parse the account data properly
      }
      
    } catch (error) {
      console.log(`Error scanning obligations: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    console.log('\n📋 Summary:');
    console.log('If no positions were found, this could mean:');
    console.log('1. The pool/obligation addresses in your .env are incorrect');
    console.log('2. Your position is in a different market not checked');
    console.log('3. Save.Finance uses a different protocol than Solend');
    console.log('4. The position data is stored differently');
    
    console.log('\n💡 Next steps:');
    console.log('1. Double-check the addresses from Save.Finance website');
    console.log('2. Look at the browser network tab when loading your position');
    console.log('3. Check if Save.Finance has its own API endpoints');
    
  } catch (error) {
    console.error('❌ Search failed:', error);
  }
}

if (require.main === module) {
  findUserPosition();
}

export { findUserPosition };
